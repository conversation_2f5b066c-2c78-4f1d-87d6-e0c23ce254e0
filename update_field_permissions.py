#!/usr/bin/env python
"""
批量更新议题详情页面中所有字段的权限控制
将 editable 类替换为权限检查宏
"""

import re
import os

def update_field_permissions():
    """更新字段权限控制"""
    template_path = "app/templates/issues/detail.html"
    
    if not os.path.exists(template_path):
        print(f"模板文件不存在: {template_path}")
        return False
    
    # 读取文件内容
    with open(template_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 需要替换的字段模式
    # 匹配: class="detail-value editable{{ ' empty' if not issue.xxx }} {{ is_field_updated('xxx') }}"
    # 替换为: class="detail-value {{ is_field_editable('xxx') }}{{ ' empty' if not issue.xxx }} {{ is_field_updated('xxx') }}"
    
    pattern = r'class="detail-value editable(\{\{[^}]+\}\})\s+(\{\{[^}]+\}\})"(\s+data-field="([^"]+)"[^>]*>)'
    
    def replacement(match):
        empty_check = match.group(1)  # {{ ' empty' if not issue.xxx }}
        update_check = match.group(2)  # {{ is_field_updated('xxx') }}
        data_attributes = match.group(3)  # data-field="xxx" 等属性
        field_name = match.group(4)  # 字段名
        
        # 添加 title 属性如果不存在
        if 'title=' not in data_attributes:
            # 在 > 之前插入 title 属性
            data_attributes = data_attributes.replace('>', f' title="{{{{ get_edit_permission_title(\'{field_name}\') }}}}">')
        
        return f'class="detail-value {{{{ is_field_editable(\'{field_name}\') }}}}{empty_check} {update_check}"{data_attributes}'
    
    # 执行替换
    updated_content = re.sub(pattern, replacement, content)
    
    # 统计替换次数
    matches = re.findall(pattern, content)
    print(f"找到 {len(matches)} 个需要更新的字段")
    
    if matches:
        for match in matches:
            field_name = match[3]
            print(f"  - 更新字段: {field_name}")
    
    # 写回文件
    if updated_content != content:
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        print(f"已更新模板文件: {template_path}")
        return True
    else:
        print("没有需要更新的内容")
        return False

def add_permission_hints():
    """为特定字段添加权限提示"""
    template_path = "app/templates/issues/detail.html"
    
    # 只读字段列表
    readonly_fields = [
        'project_number', 'sequence_number', 'anlauf_number', 'created_at',
        'vin', 'mileage', 'repair_station_info', 'after_sales_fault_description',
        'review_status', 'engineer_first_review_time', 'engineer_first_review_week',
        'zp8_delivery_date'
    ]
    
    # 项目负责人专属字段
    project_manager_fields = [
        'issue_status', 'problem_in_meeting', 'complaint_in_meeting'
    ]
    
    with open(template_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    updated_content = content
    
    # 为只读字段添加提示
    for field in readonly_fields:
        pattern = f'<div class="form-help-text">([^<]+)</div>'
        # 查找该字段的帮助文本
        field_pattern = f'data-field="{field}"[^>]*>.*?<div class="form-help-text">([^<]+)</div>'
        match = re.search(field_pattern, updated_content, re.DOTALL)
        if match:
            help_text = match.group(1)
            new_help_text = f'{help_text} <small class="text-muted">(只读字段)</small>'
            updated_content = updated_content.replace(
                f'<div class="form-help-text">{help_text}</div>',
                f'<div class="form-help-text">{new_help_text}</div>'
            )
            print(f"为只读字段 {field} 添加权限提示")
    
    # 写回文件
    if updated_content != content:
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        print("已添加权限提示")
        return True
    else:
        print("没有需要添加权限提示的内容")
        return False

def main():
    """主函数"""
    print("开始更新字段权限控制...")
    print("=" * 50)
    
    # 更新字段权限
    updated = update_field_permissions()
    
    # 添加权限提示
    # hints_added = add_permission_hints()
    
    print("\n" + "=" * 50)
    if updated:
        print("字段权限控制更新完成！")
        print("\n请检查以下内容:")
        print("1. 所有字段都使用了 {{ is_field_editable('field_name') }} 宏")
        print("2. 添加了 title 属性显示权限提示")
        print("3. JavaScript 代码已更新以支持只读字段")
        print("4. CSS 样式已添加只读字段的视觉效果")
    else:
        print("没有需要更新的内容")

if __name__ == '__main__':
    main()
