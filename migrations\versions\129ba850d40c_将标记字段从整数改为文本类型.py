"""将标记字段从整数改为文本类型

Revision ID: 129ba850d40c
Revises: c123456789ab
Create Date: 2025-07-17 19:24:39.079445

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '129ba850d40c'
down_revision = 'c123456789ab'
branch_labels = None
depends_on = None


def upgrade():
    # 将标记字段从整数类型改为文本类型
    # SQLite不支持直接修改列类型，需要使用更复杂的方法

    # 1. 添加新的文本类型字段
    op.add_column('issues', sa.Column('mark_new', sa.String(length=100), nullable=True, comment='标记(文本)'))

    # 2. 将现有整数数据转换为文本并复制到新字段
    connection = op.get_bind()
    connection.execute(sa.text("""
        UPDATE issues
        SET mark_new = CAST(mark AS TEXT)
        WHERE mark IS NOT NULL
    """))

    # 3. 删除旧的整数字段
    op.drop_column('issues', 'mark')

    # 4. 将新字段重命名为原字段名
    op.alter_column('issues', 'mark_new', new_column_name='mark')


def downgrade():
    # 回滚：将标记字段从文本类型改回整数类型

    # 1. 添加新的整数类型字段
    op.add_column('issues', sa.Column('mark_old', sa.Integer(), nullable=True, comment='标记(整数)'))

    # 2. 尝试将文本数据转换为整数并复制到新字段
    connection = op.get_bind()
    connection.execute(sa.text("""
        UPDATE issues
        SET mark_old = CASE
            WHEN mark GLOB '[0-9]*' AND mark != '' THEN CAST(mark AS INTEGER)
            ELSE NULL
        END
        WHERE mark IS NOT NULL
    """))

    # 3. 删除旧的文本字段
    op.drop_column('issues', 'mark')

    # 4. 将新字段重命名为原字段名
    op.alter_column('issues', 'mark_old', new_column_name='mark')
