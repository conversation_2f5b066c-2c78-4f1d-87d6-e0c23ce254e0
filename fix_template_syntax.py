#!/usr/bin/env python
"""
修复模板中的Jinja2语法错误
"""

import re
import os

def fix_template_syntax():
    """修复模板语法错误"""
    template_path = "app/templates/issues/detail.html"

    if not os.path.exists(template_path):
        print(f"模板文件不存在: {template_path}")
        return False

    # 读取文件内容
    with open(template_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # 统计修复前的错误数量
    before_fixes = content.count('{%%') + content.count('%%}')

    # 修复错误的Jinja2语法
    # 将 {%% 替换为 {%
    # 将 %%} 替换为 %}
    fixed_content = content.replace('{%%', '{%').replace('%%}', '%}')

    # 统计修复后的错误数量
    after_fixes = fixed_content.count('{%%') + fixed_content.count('%%}')

    if before_fixes > 0:
        # 写回文件
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(fixed_content)
        print(f"已修复 {before_fixes} 个语法错误")

        if after_fixes > 0:
            print(f"警告: 仍有 {after_fixes} 个语法错误未修复")
        else:
            print("所有语法错误已修复")
        return True
    else:
        print("没有发现语法错误")
        return False

def main():
    """主函数"""
    print("修复模板Jinja2语法错误...")
    print("=" * 40)
    
    success = fix_template_syntax()
    
    print("\n" + "=" * 40)
    if success:
        print("模板语法修复完成！")
        print("现在可以正常访问页面了")
    else:
        print("没有需要修复的语法错误")

if __name__ == '__main__':
    main()
