#!/usr/bin/env python
"""
测试议题状态导入功能
创建测试数据并验证状态映射
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import Issue
from app.models.enums import IssueStatus
from app.views.import_data import create_issue_from_data

def test_status_import():
    """测试状态导入功能"""
    print("测试议题状态导入功能...")
    
    app = create_app()
    with app.app_context():
        # 测试数据 - 包含各种中文状态值
        test_cases = [
            {
                'anlauf_number': 'TEST_STATUS_001',
                'project_number': 'TEST_PROJECT',
                'problem_description': '测试已上Pre-Anlauf状态',
                'issue_status': '已上Pre-Anlauf'
            },
            {
                'anlauf_number': 'TEST_STATUS_002',
                'project_number': 'TEST_PROJECT',
                'problem_description': '测试已上会分析中状态',
                'issue_status': '已上会，分析中'
            },
            {
                'anlauf_number': 'TEST_STATUS_003',
                'project_number': 'TEST_PROJECT',
                'problem_description': '测试已上会措施落实状态',
                'issue_status': '已上会，措施落实'
            },
            {
                'anlauf_number': 'TEST_STATUS_004',
                'project_number': 'TEST_PROJECT',
                'problem_description': '测试未上会确认中状态',
                'issue_status': '未上会，确认中'
            },
            {
                'anlauf_number': 'TEST_STATUS_005',
                'project_number': 'TEST_PROJECT',
                'problem_description': '测试未知状态',
                'issue_status': '未知状态值'
            },
            {
                'anlauf_number': 'TEST_STATUS_006',
                'project_number': 'TEST_PROJECT',
                'problem_description': '测试空状态',
                'issue_status': None
            }
        ]
        
        print(f"\n开始测试 {len(test_cases)} 个状态值...")
        
        success_count = 0
        for i, test_data in enumerate(test_cases, 1):
            try:
                print(f"\n测试 {i}: {test_data['issue_status']}")
                
                # 创建议题
                issue = create_issue_from_data(test_data)
                
                # 检查结果
                raw_status = issue._issue_status
                converted_status = issue.issue_status
                
                print(f"  原始存储: '{raw_status}'")
                if converted_status:
                    print(f"  转换结果: {converted_status.value} ({converted_status.chinese_label})")
                    success_count += 1
                else:
                    print(f"  转换结果: None (转换失败)")
                
                # 不提交到数据库，只测试逻辑
                db.session.rollback()
                
            except Exception as e:
                print(f"  错误: {e}")
                db.session.rollback()
        
        print(f"\n测试完成: {success_count}/{len(test_cases)} 成功")
        return success_count == len(test_cases)

def test_status_mapping_logic():
    """直接测试状态映射逻辑"""
    print("\n测试状态映射逻辑...")
    
    app = create_app()
    with app.app_context():
        # 创建一个测试议题对象
        issue = Issue()
        
        # 测试各种状态值
        test_statuses = [
            ('已上Pre-Anlauf', 'PRE_ANLAUF'),
            ('已上会，分析中', 'MEETING_ANALYZING'),
            ('已上会，分析完成', 'MEETING_ANALYSIS_COMPLETE'),
            ('已上会，措施制定', 'MEETING_MEASURES_FORMULATING'),
            ('已上会，措施落实', 'MEETING_MEASURES_IMPLEMENTING'),
            ('已上会，继续跟踪', 'MEETING_CONTINUE_TRACKING'),
            ('未上会，确认中', 'NO_MEETING_CONFIRMING'),
            ('未上会，继续跟踪', 'NO_MEETING_CONTINUE_TRACKING'),
            ('未上会，无效抱怨', 'NO_MEETING_INVALID_COMPLAINT'),
            ('未上会，已定义措施', 'NO_MEETING_MEASURES_DEFINED'),
            ('未上会，措施实施', 'NO_MEETING_MEASURES_IMPLEMENTING'),
        ]
        
        success_count = 0
        for chinese_status, expected_english in test_statuses:
            # 设置原始状态值
            issue._issue_status = chinese_status
            
            # 获取转换后的状态
            converted = issue.issue_status
            
            if converted and converted.value == expected_english:
                print(f"  [OK] '{chinese_status}' -> {converted.value}")
                success_count += 1
            else:
                actual = converted.value if converted else 'None'
                print(f"  [ERROR] '{chinese_status}' -> {actual}, 期望: {expected_english}")
        
        print(f"\n映射测试完成: {success_count}/{len(test_statuses)} 成功")
        return success_count == len(test_statuses)

def check_current_database():
    """检查当前数据库状态"""
    print("\n检查当前数据库状态...")
    
    app = create_app()
    with app.app_context():
        try:
            # 获取所有议题
            all_issues = Issue.query.all()
            print(f"数据库中共有 {len(all_issues)} 个议题")
            
            if all_issues:
                print("\n议题状态详情:")
                for issue in all_issues:
                    raw_status = issue._issue_status
                    converted_status = issue.issue_status
                    
                    if converted_status:
                        print(f"  {issue.anlauf_number}: '{raw_status}' -> {converted_status.value} ({converted_status.chinese_label})")
                    else:
                        print(f"  {issue.anlauf_number}: '{raw_status}' -> 转换失败")
            
            return True
            
        except Exception as e:
            print(f"检查失败: {e}")
            return False

def main():
    """主测试函数"""
    print("议题状态导入功能完整测试")
    print("=" * 50)
    
    # 检查当前数据库状态
    db_check = check_current_database()
    
    # 测试状态映射逻辑
    mapping_test = test_status_mapping_logic()
    
    # 测试导入功能
    import_test = test_status_import()
    
    # 总结
    print("\n测试结果总结:")
    print(f"  数据库检查: {'通过' if db_check else '失败'}")
    print(f"  状态映射测试: {'通过' if mapping_test else '失败'}")
    print(f"  导入功能测试: {'通过' if import_test else '失败'}")
    
    if db_check and mapping_test and import_test:
        print("\n所有测试通过！议题状态导入功能正常。")
        return True
    else:
        print("\n部分测试失败，请检查相关问题。")
        return False

if __name__ == '__main__':
    main()
