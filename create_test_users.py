#!/usr/bin/env python
"""
创建测试用户来验证字段编辑权限功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.user import User
from app.models.enums import UserRole, UserStatus
from app.models.project_assignment import ProjectAssignment

def create_test_users():
    """创建测试用户"""
    app = create_app()
    
    with app.app_context():
        # 创建项目负责人用户
        project_manager = User.query.filter_by(username='project_manager').first()
        if not project_manager:
            project_manager = User(
                username='project_manager',
                email='<EMAIL>',
                department='项目管理部',
                position='项目负责人',
                role=UserRole.PROJECT_MANAGER,
                status=UserStatus.ACTIVE,
                is_active=True
            )
            project_manager.set_password('123456')
            db.session.add(project_manager)
            print("创建项目负责人用户: project_manager / 123456")
        else:
            print("项目负责人用户已存在")

        # 创建普通用户
        normal_user = User.query.filter_by(username='normal_user').first()
        if not normal_user:
            normal_user = User(
                username='normal_user',
                email='<EMAIL>',
                department='技术部',
                position='普通用户',
                role=UserRole.USER,
                status=UserStatus.ACTIVE,
                is_active=True
            )
            normal_user.set_password('123456')
            db.session.add(normal_user)
            print("创建普通用户: normal_user / 123456")
        else:
            print("普通用户已存在")
        
        # 提交用户创建
        db.session.commit()
        
        # 为项目负责人分配项目
        if project_manager:
            # 检查是否已有项目分配
            existing_assignment = ProjectAssignment.query.filter_by(
                user_id=project_manager.id,
                project_number='TEST001',
                is_active=True
            ).first()
            
            if not existing_assignment:
                # 创建项目分配
                assignment = ProjectAssignment.assign_user_to_project(
                    user_id=project_manager.id,
                    project_number='TEST001',
                    assigned_by_id=1,  # admin用户ID
                    notes='测试项目分配'
                )
                db.session.commit()
                print("为项目负责人分配项目: TEST001")
            else:
                print("项目分配已存在")
        
        print("\n测试用户创建完成！")
        print("=" * 50)
        print("用户账号信息:")
        print("1. 管理员: admin / admin123 (所有权限)")
        print("2. 项目负责人: project_manager / 123456 (项目TEST001权限)")
        print("3. 普通用户: normal_user / 123456 (有限权限)")
        print("=" * 50)
        print("\n权限测试说明:")
        print("- 管理员: 可以编辑所有字段(除了只读字段)")
        print("- 项目负责人: 可以编辑议题状态、问题是否上会、抱怨是否上会等专属字段")
        print("- 普通用户: 只能编辑基本字段，不能编辑项目负责人专属字段")
        print("- 只读字段: 项目编号、序号、Anlauf编号、创建日期、VIN等对所有人只读")

if __name__ == '__main__':
    create_test_users()
