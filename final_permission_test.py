#!/usr/bin/env python
"""
最终权限测试 - 验证所有字段的权限配置
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.user import User
from app.models.issue import Issue
from app.models.enums import UserRole, UserStatus
from app.views.issues import get_field_edit_permissions

def final_permission_test():
    """最终权限测试"""
    app = create_app()
    
    with app.app_context():
        # 获取测试用户
        admin = User.query.filter_by(username='admin').first()
        project_manager = User.query.filter_by(username='project_manager').first()
        normal_user = User.query.filter_by(username='normal_user').first()
        
        if not all([admin, project_manager, normal_user]):
            print("错误: 测试用户不完整，请先运行 create_test_users.py")
            return
        
        # 获取测试议题
        test_issue = Issue.query.filter_by(project_number='TEST001').first()
        if not test_issue:
            print("错误: 测试议题不存在")
            return
        
        print("=" * 80)
        print("最终字段编辑权限测试结果")
        print("=" * 80)
        
        # 定义所有字段分类
        field_categories = {
            '系统只读字段': [
                'project_number', 'sequence_number', 'anlauf_number', 'created_at',
                'vin', 'mileage', 'repair_station_info', 'after_sales_fault_description',
                'review_status', 'engineer_first_review_time', 'engineer_first_review_week',
                'zp8_delivery_date'
            ],
            '项目负责人专属字段': [
                'problem_report_week', 'problem_report_date', 'issue_status',
                'problem_in_meeting', 'complaint_in_meeting'
            ],
            '项目负责人受限字段': [
                'department', 'engineer', 'vehicle_status', 'is_rework_vehicle',
                'kdnr', 'banr', 'breakdown', 'problem_description',
                'analysis_measures', 'problem_type', 'fault_parts_status',
                'warehouse_received_fault_parts', 'mark', 'meeting_topic_name'
            ]
        }
        
        users_to_test = [
            ('管理员', admin),
            ('项目负责人', project_manager),
            ('普通用户', normal_user)
        ]
        
        # 测试每个用户的权限
        for user_name, user in users_to_test:
            print(f"\n{user_name} ({user.username}) 的权限测试:")
            print("-" * 60)
            
            permissions = get_field_edit_permissions(user, test_issue)
            
            for category, fields in field_categories.items():
                print(f"\n  {category}:")
                for field in fields:
                    can_edit = permissions.get(field, False)
                    status = "[可编辑]" if can_edit else "[只读]"
                    print(f"    {field:30} {status}")
        
        print("\n" + "=" * 80)
        print("权限验证总结:")
        print("=" * 80)
        
        # 验证权限逻辑
        admin_permissions = get_field_edit_permissions(admin, test_issue)
        pm_permissions = get_field_edit_permissions(project_manager, test_issue)
        user_permissions = get_field_edit_permissions(normal_user, test_issue)
        
        # 验证管理员权限
        readonly_fields = field_categories['系统只读字段']
        admin_editable_count = sum(1 for field, can_edit in admin_permissions.items() 
                                 if can_edit and field not in readonly_fields)
        admin_readonly_count = sum(1 for field in readonly_fields 
                                 if not admin_permissions.get(field, True))
        
        print(f"1. 管理员权限验证:")
        print(f"   - 可编辑字段数: {admin_editable_count}")
        print(f"   - 只读字段数: {admin_readonly_count}")
        print(f"   - 验证结果: {'通过' if admin_readonly_count == len(readonly_fields) else '失败'}")
        
        # 验证项目负责人权限
        pm_exclusive_fields = field_categories['项目负责人专属字段']
        pm_restricted_fields = field_categories['项目负责人受限字段']
        
        pm_exclusive_editable = sum(1 for field in pm_exclusive_fields 
                                  if pm_permissions.get(field, False))
        pm_restricted_readonly = sum(1 for field in pm_restricted_fields 
                                   if not pm_permissions.get(field, True))
        
        print(f"\n2. 项目负责人权限验证:")
        print(f"   - 专属字段可编辑数: {pm_exclusive_editable}/{len(pm_exclusive_fields)}")
        print(f"   - 受限字段只读数: {pm_restricted_readonly}/{len(pm_restricted_fields)}")
        print(f"   - 验证结果: {'通过' if pm_exclusive_editable == len(pm_exclusive_fields) and pm_restricted_readonly == len(pm_restricted_fields) else '失败'}")
        
        # 验证普通用户权限
        user_readonly_count = sum(1 for field, can_edit in user_permissions.items() 
                                if not can_edit)
        
        print(f"\n3. 普通用户权限验证:")
        print(f"   - 只读字段数: {user_readonly_count}")
        print(f"   - 验证结果: {'通过' if user_readonly_count > 0 else '失败'}")
        
        print(f"\n4. 总体验证结果:")
        all_tests_passed = (
            admin_readonly_count == len(readonly_fields) and
            pm_exclusive_editable == len(pm_exclusive_fields) and
            pm_restricted_readonly == len(pm_restricted_fields) and
            user_readonly_count > 0
        )
        print(f"   - 所有权限测试: {'全部通过' if all_tests_passed else '存在问题'}")
        
        if all_tests_passed:
            print("\n[成功] 字段编辑权限控制功能实现完成！")
            print("[√] 系统只读字段对所有用户只读")
            print("[√] 项目负责人专属字段权限正确")
            print("[√] 项目负责人受限字段权限正确")
            print("[√] 管理员拥有最高权限")
        else:
            print("\n[警告] 权限配置存在问题，请检查实现")

if __name__ == '__main__':
    final_permission_test()
