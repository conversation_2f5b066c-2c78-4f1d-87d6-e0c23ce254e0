#!/usr/bin/env python
"""
测试议题状态导入功能
验证各种议题状态值的正确映射和导入
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import Issue
from app.models.enums import IssueStatus
from app.views.import_data import create_issue_from_data

def test_issue_status_mapping():
    """测试议题状态映射逻辑"""
    print("\n测试议题状态映射逻辑...")
    
    app = create_app()
    with app.app_context():
        # 测试用例：输入状态值 -> 期望的枚举值
        test_cases = [
            # 中文标签
            ('已上Pre-Anlauf', 'PRE_ANLAUF'),
            ('已上会，分析中', 'MEETING_ANALYZING'),
            ('已上会，分析完成', 'MEETING_ANALYSIS_COMPLETE'),
            ('已上会，措施制定', 'MEETING_MEASURES_FORMULATING'),
            ('已上会，措施落实', 'MEETING_MEASURES_IMPLEMENTING'),
            ('已上会，继续跟踪', 'MEETING_CONTINUE_TRACKING'),
            ('未上会，确认中', 'NO_MEETING_CONFIRMING'),
            ('未上会，继续跟踪', 'NO_MEETING_CONTINUE_TRACKING'),
            ('未上会，无效抱怨', 'NO_MEETING_INVALID_COMPLAINT'),
            ('未上会，已定义措施', 'NO_MEETING_MEASURES_DEFINED'),
            ('未上会，措施实施', 'NO_MEETING_MEASURES_IMPLEMENTING'),
            # 英文枚举值
            ('PRE_ANLAUF', 'PRE_ANLAUF'),
            ('MEETING_ANALYZING', 'MEETING_ANALYZING'),
            ('NO_MEETING_CONFIRMING', 'NO_MEETING_CONFIRMING'),
            # 兼容旧值
            ('待确认上会', 'NO_MEETING_CONFIRMING'),
            ('讨论中', 'MEETING_ANALYZING'),
            # 未知值（应该使用默认值）
            ('未知状态', 'NO_MEETING_CONFIRMING'),
            ('', 'NO_MEETING_CONFIRMING'),
        ]
        
        success_count = 0
        total_count = len(test_cases)
        
        for input_status, expected_enum in test_cases:
            try:
                # 创建测试数据
                test_data = {
                    'anlauf_number': f'TEST{success_count:04d}',
                    'project_number': 'TEST_PROJECT',
                    'issue_status': input_status,
                    'problem_description': '测试议题状态导入'
                }
                
                # 创建议题
                issue = create_issue_from_data(test_data)
                
                # 检查结果
                if issue.issue_status and issue.issue_status.value == expected_enum:
                    print(f"   [OK] '{input_status}' -> {issue.issue_status.value} ({issue.issue_status.chinese_label})")
                    success_count += 1
                else:
                    actual_value = issue.issue_status.value if issue.issue_status else 'None'
                    print(f"   [ERROR] '{input_status}' -> {actual_value}, 期望: {expected_enum}")
                
                # 清理测试数据（不提交到数据库）
                db.session.rollback()
                
            except Exception as e:
                print(f"   [ERROR] '{input_status}' 处理失败: {e}")
                db.session.rollback()
        
        print(f"\n测试结果: {success_count}/{total_count} 通过")
        return success_count == total_count

def test_issue_status_enum_completeness():
    """测试议题状态枚举的完整性"""
    print("\n测试议题状态枚举完整性...")
    
    # 检查所有枚举值都有对应的中文标签
    missing_labels = []
    for status in IssueStatus:
        if not status.chinese_label or status.chinese_label == status.value:
            missing_labels.append(status.value)
    
    if missing_labels:
        print(f"   [ERROR] 以下枚举值缺少中文标签: {missing_labels}")
        return False
    else:
        print("   [OK] 所有枚举值都有对应的中文标签")
        
    # 显示所有可用的状态
    print("\n   可用的议题状态:")
    for status in IssueStatus:
        print(f"     - {status.value}: {status.chinese_label}")
    
    return True

def test_default_status():
    """测试默认状态设置"""
    print("\n测试默认状态设置...")
    
    app = create_app()
    with app.app_context():
        try:
            # 创建没有状态的议题
            test_data = {
                'anlauf_number': 'TEST_DEFAULT',
                'project_number': 'TEST_PROJECT',
                'problem_description': '测试默认状态'
            }
            
            issue = create_issue_from_data(test_data)
            
            if issue.issue_status and issue.issue_status.value == 'NO_MEETING_CONFIRMING':
                print(f"   [OK] 默认状态设置正确: {issue.issue_status.chinese_label}")
                db.session.rollback()
                return True
            else:
                actual_value = issue.issue_status.value if issue.issue_status else 'None'
                print(f"   [ERROR] 默认状态错误: {actual_value}, 期望: NO_MEETING_CONFIRMING")
                db.session.rollback()
                return False
                
        except Exception as e:
            print(f"   [ERROR] 测试默认状态失败: {e}")
            db.session.rollback()
            return False

def main():
    """主测试函数"""
    print("议题状态导入功能测试")
    print("=" * 50)
    
    # 测试枚举完整性
    enum_test = test_issue_status_enum_completeness()
    
    # 测试状态映射
    mapping_test = test_issue_status_mapping()
    
    # 测试默认状态
    default_test = test_default_status()
    
    # 总结
    print("\n测试结果总结:")
    print(f"   枚举完整性测试: {'通过' if enum_test else '失败'}")
    print(f"   状态映射测试: {'通过' if mapping_test else '失败'}")
    print(f"   默认状态测试: {'通过' if default_test else '失败'}")
    
    if enum_test and mapping_test and default_test:
        print("\n所有测试通过！议题状态导入功能正常。")
        return True
    else:
        print("\n部分测试失败，请检查相关问题。")
        return False

if __name__ == '__main__':
    main()
