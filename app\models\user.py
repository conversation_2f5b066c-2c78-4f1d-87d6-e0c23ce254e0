"""
用户数据模型
包含用户基本信息、角色权限和认证相关功能
"""

from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
from app import db
from .enums import UserRole, UserStatus
from app.utils.timezone_utils import beijing_now


class User(db.Model):
    """
    用户模型类
    
    定义用户的基本信息、角色权限、认证信息等
    """
    
    __tablename__ = 'users'
    
    # 基础字段
    id = db.Column(db.Integer, primary_key=True, comment='用户ID')
    username = db.Column(db.String(64), unique=True, nullable=False, index=True, comment='用户名')
    email = db.Column(db.String(120), unique=True, nullable=False, index=True, comment='邮箱')
    password_hash = db.Column(db.String(128), comment='密码哈希')
    
    # 个人信息
    employee_number = db.Column(db.String(5), comment='工号', nullable=True, unique=True, index=True)
    department = db.Column(db.String(100), comment='部门')
    position = db.Column(db.String(100), comment='职位')
    phone = db.Column(db.String(20), comment='电话')
    employee_id = db.Column(db.String(20), comment='员工编号')
    
    # 角色权限
    role = db.Column(db.Enum(UserRole), default=UserRole.USER, nullable=False, comment='用户角色')
    status = db.Column(db.Enum(UserStatus), default=UserStatus.PENDING, nullable=False, comment='用户状态')
    is_active = db.Column(db.Boolean, default=False, comment='是否激活')
    is_admin = db.Column(db.Boolean, default=False, comment='是否管理员')
    
    # 审核信息
    approved_by = db.Column(db.Integer, db.ForeignKey('users.id'), comment='审核人ID')
    approved_at = db.Column(db.DateTime, comment='审核时间')
    approval_reason = db.Column(db.Text, comment='审核原因/备注')
    
    # 时间字段
    created_at = db.Column(db.DateTime, default=beijing_now, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=beijing_now, onupdate=beijing_now, comment='更新时间')
    last_login = db.Column(db.DateTime, comment='最后登录时间')
    last_seen = db.Column(db.DateTime, comment='最后活跃时间')
    
    # 设置字段
    avatar_url = db.Column(db.String(200), comment='头像URL')
    timezone = db.Column(db.String(50), default='Asia/Shanghai', comment='时区')
    language = db.Column(db.String(10), default='zh-CN', comment='语言偏好')
    
    # 通知设置
    email_notifications = db.Column(db.Boolean, default=True, comment='邮件通知')
    issue_notifications = db.Column(db.Boolean, default=True, comment='议题通知')
    
    # 外键关系
    approver = db.relationship('User', remote_side=[id], backref='approved_users')

    def __init__(self, **kwargs):
        """初始化用户实例"""
        super(User, self).__init__(**kwargs)
        if self.created_at is None:
            self.created_at = beijing_now()
        if self.updated_at is None:
            self.updated_at = beijing_now()
    
    def __repr__(self):
        """返回用户的字符串表示"""
        return f'<User {self.username}>'
    
    def set_password(self, password):
        """设置密码"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)
    
    def ping(self):
        """更新最后活跃时间"""
        self.last_seen = beijing_now()
        db.session.add(self)
        db.session.commit()
    
    def can(self, permission):
        """检查用户权限"""
        # 如果用户状态不是激活状态，则无权限
        if self.status != UserStatus.ACTIVE:
            return False
            
        permissions = {
            UserRole.ADMIN: ['read', 'write', 'delete', 'admin', 'user_management'],
            UserRole.PROJECT_MANAGER: ['read', 'write', 'delete', 'all_issues'],
            UserRole.USER: ['read', 'write', 'own_issues'],
            # 保留原有角色的权限设置，向后兼容
            UserRole.MANAGER: ['read', 'write', 'delete'],
            UserRole.ENGINEER: ['read', 'write'],
            UserRole.OPERATOR: ['read', 'write'],
            UserRole.VIEWER: ['read']
        }
        return permission in permissions.get(self.role, [])
    
    def can_edit_issue(self, issue):
        """检查是否可以编辑议题"""
        # 如果用户状态不是激活状态，则无权限
        if self.status != UserStatus.ACTIVE:
            return False

        # 管理员可以编辑所有议题
        if self.is_admin or self.role == UserRole.ADMIN:
            return True

        # 项目负责人权限控制
        if self.role == UserRole.PROJECT_MANAGER:
            assigned_projects = self.get_assigned_projects()
            if assigned_projects:
                # 有激活的项目分配，能编辑：分配的项目议题 + 工程师字段等于自己用户名的议题
                return (self.is_assigned_to_project(issue.project_number) or
                       issue.engineer == self.username)
            else:
                # 没有激活的项目分配，降级为普通用户权限
                return issue.engineer == self.username

        # 普通用户只能编辑工程师字段等于自己用户名的议题
        if self.role == UserRole.USER:
            return issue.engineer == self.username

        # 保留原有角色的权限逻辑，向后兼容
        if self.role in [UserRole.MANAGER, UserRole.ENGINEER]:
            return True
        if issue.engineer == self.username:
            return True

        return False
    
    def can_delete_issue(self, issue):
        """检查是否可以删除议题"""
        # 如果用户状态不是激活状态，则无权限
        if self.status != UserStatus.ACTIVE:
            return False

        # 管理员可以删除所有议题
        if self.is_admin or self.role == UserRole.ADMIN:
            return True

        # 项目负责人权限控制
        if self.role == UserRole.PROJECT_MANAGER:
            assigned_projects = self.get_assigned_projects()
            if assigned_projects:
                # 有激活的项目分配，能删除：分配的项目议题
                return self.is_assigned_to_project(issue.project_number)
            else:
                # 没有激活的项目分配，降级为普通用户权限
                return False

        # 普通用户不能删除议题
        if self.role == UserRole.USER:
            return False

        # 保留原有角色的权限逻辑，向后兼容
        if self.role == UserRole.MANAGER:
            return True

        return False
    
    def can_view_issue(self, issue):
        """检查是否可以查看议题"""
        # 如果用户状态不是激活状态，则无权限
        if self.status != UserStatus.ACTIVE:
            return False

        # 管理员可以查看所有议题
        if self.is_admin or self.role == UserRole.ADMIN:
            return True

        # 项目负责人权限控制
        if self.role == UserRole.PROJECT_MANAGER:
            assigned_projects = self.get_assigned_projects()
            if assigned_projects:
                # 有激活的项目分配，能查看：分配的项目议题 + 工程师字段等于自己用户名的议题
                return (self.is_assigned_to_project(issue.project_number) or
                       issue.engineer == self.username)
            else:
                # 没有激活的项目分配，降级为普通用户权限
                return issue.engineer == self.username

        # 普通用户只能查看工程师字段等于自己用户名的议题
        if self.role == UserRole.USER:
            return issue.engineer == self.username

        # 保留原有角色的权限逻辑，向后兼容
        return True
    
    def get_accessible_issues(self, status=None):
        """获取用户可访问的议题"""
        from .issue import Issue
        
        # 管理员可以访问所有议题
        if self.is_admin or self.role == UserRole.ADMIN:
            query = Issue.query
        # 项目负责人权限控制
        elif self.role == UserRole.PROJECT_MANAGER:
            assigned_projects = self.get_assigned_projects()
            if assigned_projects:
                # 有激活的项目分配，能访问：分配的项目议题 + 工程师字段等于自己用户名的议题
                query = Issue.query.filter(
                    db.or_(
                        Issue.project_number.in_(assigned_projects),  # 管理的项目议题
                        Issue.engineer == self.username              # 工程师字段等于自己用户名的议题
                    )
                )
            else:
                # 没有激活的项目分配，降级为普通用户权限
                query = Issue.query.filter(Issue.engineer == self.username)
        # 普通用户只能访问工程师字段等于自己用户名的议题
        elif self.role == UserRole.USER:
            query = Issue.query.filter(Issue.engineer == self.username)
        else:
            # 保留原有角色的权限逻辑，向后兼容
            query = Issue.query
            
        if status:
            query = query.filter_by(issue_status=status)
        return query
    
    def get_assigned_issues(self, status=None):
        """获取工程师字段等于用户名的议题"""
        from .issue import Issue
        query = Issue.query.filter_by(engineer=self.username)
        if status:
            query = query.filter_by(issue_status=status)
        return query

    def get_created_issues(self, status=None):
        """获取用户负责的议题（与get_assigned_issues相同）"""
        return self.get_assigned_issues(status)
    
    def approve(self, approver, reason=None):
        """批准用户"""
        self.status = UserStatus.ACTIVE
        self.is_active = True
        self.approved_by = approver.id
        self.approved_at = beijing_now()
        self.approval_reason = reason
        
    def reject(self, approver, reason=None):
        """拒绝用户"""
        self.status = UserStatus.REJECTED
        self.is_active = False
        self.approved_by = approver.id
        self.approved_at = beijing_now()
        self.approval_reason = reason
        
    def deactivate(self, approver, reason=None):
        """禁用用户"""
        self.status = UserStatus.INACTIVE
        self.is_active = False
        self.approved_by = approver.id
        self.approved_at = beijing_now()
        self.approval_reason = reason
    
    def to_dict(self):
        """将用户对象转换为字典"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'employee_number': self.employee_number,
            'department': self.department,
            'position': self.position,
            'role': self.role.value if self.role else None,
            'status': self.status.value if self.status else None,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'avatar_url': self.avatar_url,
            'approved_at': self.approved_at.isoformat() if self.approved_at else None,
            'approval_reason': self.approval_reason
        }
    
    @staticmethod
    def from_dict(data):
        """从字典创建用户对象"""
        user = User()
        for field, value in data.items():
            if hasattr(user, field) and value is not None:
                if field == 'role':
                    setattr(user, field, UserRole(value))
                elif field == 'status':
                    setattr(user, field, UserStatus(value))
                elif field != 'password':  # 密码需要单独处理
                    setattr(user, field, value)
        return user
    
    @classmethod
    def search(cls, query, filters=None):
        """
        搜索用户
        
        Args:
            query (str): 搜索关键词
            filters (dict): 过滤条件
            
        Returns:
            Query: SQLAlchemy查询对象
        """
        search = cls.query
        
        # 关键词搜索
        if query:
            search = search.filter(
                db.or_(
                    cls.username.contains(query),
                    cls.employee_number.contains(query),
                    cls.email.contains(query),
                    cls.department.contains(query)
                )
            )
        
        # 应用过滤器
        if filters:
            if filters.get('role'):
                search = search.filter(cls.role == filters['role'])
            if filters.get('status'):
                search = search.filter(cls.status == filters['status'])
            if filters.get('department'):
                search = search.filter(cls.department == filters['department'])
            if filters.get('is_active') is not None:
                search = search.filter(cls.is_active == filters['is_active'])
        
        return search
    
    def get_assigned_projects(self):
        """获取分配给用户的所有项目编号"""
        from .project_assignment import ProjectAssignment
        return ProjectAssignment.get_user_projects(self.id, active_only=True)
    
    def is_assigned_to_project(self, project_number):
        """检查用户是否被分配到指定项目（只检查激活的分配）"""
        from .project_assignment import ProjectAssignment
        return ProjectAssignment.is_user_assigned_to_project(self.id, project_number)
    
    def assign_to_project(self, project_number, assigned_by_id, notes=None):
        """将用户分配到项目"""
        from .project_assignment import ProjectAssignment
        return ProjectAssignment.assign_user_to_project(
            self.id, project_number, assigned_by_id, notes
        )
    
    def remove_from_project(self, project_number, by_user_id=None):
        """移除用户的项目分配"""
        from .project_assignment import ProjectAssignment
        return ProjectAssignment.remove_user_from_project(
            self.id, project_number, by_user_id
        ) 