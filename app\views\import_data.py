"""
数据导入视图控制器
支持Excel、CSV等格式的议题数据导入功能
"""

import os
import pandas as pd
from functools import wraps
from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash, current_app, session
from werkzeug.utils import secure_filename
from datetime import datetime
from app import db
from app.models import Issue, Project
from app.models.enums import IssueStatus, UserRole, MaintenanceStatus
from app.utils.timezone_utils import beijing_now


def import_permission_required(f):
    """检查用户是否有数据导入权限的装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        user_role = session.get('user_role')
        if user_role not in [UserRole.ADMIN.value, UserRole.PROJECT_MANAGER.value]:
            flash('您没有权限访问此页面', 'error')
            return redirect(url_for('main.index'))
        return f(*args, **kwargs)
    return decorated_function


import_data = Blueprint('import_data', __name__)


@import_data.route('/')
@import_permission_required
def index():
    """数据导入主页"""
    return render_template('import/index.html')


@import_data.route('/upload', methods=['POST'])
@import_permission_required
def upload_file():
    """上传文件进行预览"""
    try:
        if 'file' not in request.files:
            current_app.logger.error('上传请求中没有文件')
            return jsonify({'success': False, 'message': '请选择文件'}), 400
        
        file = request.files['file']
        if file.filename == '':
            current_app.logger.error('未选择文件')
            return jsonify({'success': False, 'message': '未选择文件'}), 400
        
        # 检查文件格式
        allowed_extensions = current_app.config.get('ALLOWED_EXTENSIONS', {'xlsx', 'xls', 'csv'})
        if not allowed_file(file.filename, allowed_extensions):
            current_app.logger.error(f'不支持的文件格式: {file.filename}')
            return jsonify({'success': False, 'message': f'不支持的文件格式，仅支持：{", ".join(allowed_extensions)}'}), 400
        
        # 保存上传的文件
        filename = secure_filename(file.filename)
        upload_folder = current_app.config['UPLOAD_FOLDER']
        
        # 确保上传目录存在
        if not os.path.exists(upload_folder):
            current_app.logger.info(f'创建上传目录: {upload_folder}')
            os.makedirs(upload_folder)
        
        file_path = os.path.join(upload_folder, filename)
        current_app.logger.info(f'保存文件到: {file_path}')
        file.save(file_path)
        
        # 解析文件内容
        result = parse_file(file_path)
        
        # 删除临时文件
        try:
            os.remove(file_path)
            current_app.logger.info(f'删除临时文件: {file_path}')
        except Exception as e:
            current_app.logger.warning(f'删除临时文件失败: {e}')
        
        return jsonify({
            'success': True,
            'message': '文件解析成功',
            'data': result
        })
        
    except Exception as e:
        current_app.logger.error(f'文件上传解析失败: {e}', exc_info=True)
        return jsonify({'success': False, 'message': f'文件解析失败: {str(e)}'}), 500


@import_data.route('/preview', methods=['POST'])
@import_permission_required
def preview_data():
    """预览导入数据"""
    data = request.json.get('data', [])
    mapping = request.json.get('mapping', {})
    
    if not data:
        return jsonify({'success': False, 'message': '没有数据可预览'}), 400
    
    try:
        # 转换数据格式
        issues_data = []
        for row in data[:10]:  # 只预览前10行
            issue_data = convert_row_to_issue(row, mapping)
            issues_data.append(issue_data)
        
        # 检测重复记录
        duplicate_info = detect_duplicates(data)
        
        return jsonify({
            'success': True,
            'message': f'预览前10行数据，共{len(data)}行',
            'preview': issues_data,
            'total_rows': len(data),
            'duplicates': duplicate_info
        })
        
    except Exception as e:
        current_app.logger.error(f'数据预览失败: {e}')
        return jsonify({'success': False, 'message': f'数据预览失败: {str(e)}'}), 500


@import_data.route('/execute', methods=['POST'])
@import_permission_required
def execute_import():
    """执行数据导入 - 基于Anlauf编号进行重复判断"""
    data = request.json.get('data', [])
    mapping = request.json.get('mapping', {})
    import_mode = request.json.get('mode', 'create')  # create, update, skip
    
    if not data:
        return jsonify({'success': False, 'message': '没有数据可导入'}), 400
    
    try:
        success_count = 0
        error_count = 0
        skip_count = 0
        errors = []
        
        for i, row in enumerate(data):
            try:
                issue_data = convert_row_to_issue(row, mapping)
                
                # 基于项目编号和Anlauf编号检查议题是否已存在
                existing_issue = None
                anlauf_number = issue_data.get('anlauf_number')
                project_number = issue_data.get('project_number')
                
                if anlauf_number and project_number:
                    # 优先使用项目编号+Anlauf编号进行匹配
                    existing_issue = Issue.query.filter_by(
                        project_number=project_number,
                        anlauf_number=anlauf_number
                    ).first()
                elif anlauf_number:
                    # 如果只有Anlauf编号，仍然支持单独匹配
                    existing_issue = Issue.query.filter_by(anlauf_number=anlauf_number).first()
                
                if existing_issue:
                    if import_mode == 'skip':
                        skip_count += 1
                        current_app.logger.info(f'跳过重复Anlauf编号: {anlauf_number}')
                        continue
                    elif import_mode == 'update':
                        # 更新现有议题
                        current_app.logger.info(f'更新议题: Anlauf编号={anlauf_number}')
                        update_issue_from_data(existing_issue, issue_data)
                        db.session.add(existing_issue)
                        success_count += 1
                    else:  # import_mode == 'create'
                        # 跳过重复的议题
                        skip_count += 1
                        current_app.logger.info(f'跳过重复Anlauf编号 (仅创建模式): {anlauf_number}')
                        continue
                else:
                    # 创建新议题
                    issue = create_issue_from_data(issue_data)
                    db.session.add(issue)
                    success_count += 1
                    current_app.logger.info(f'创建新议题: Anlauf编号={anlauf_number}')
                
            except Exception as e:
                error_count += 1
                error_message = f'第{i+1}行: {str(e)}'
                errors.append(error_message)
                current_app.logger.error(f'导入第{i+1}行失败: {e}')
                continue
        
        # 提交数据库事务
        db.session.commit()
        current_app.logger.info(f'数据导入完成: 成功{success_count}, 失败{error_count}, 跳过{skip_count}')
        
        return jsonify({
            'success': True,
            'message': '数据导入完成',
            'statistics': {
                'success': success_count,
                'error': error_count,
                'skip': skip_count,
                'total': len(data)
            },
            'errors': errors[:10]
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'数据导入失败: {e}', exc_info=True)
        return jsonify({'success': False, 'message': f'数据导入失败: {str(e)}'}), 500


@import_data.route('/template')
@import_permission_required
def download_template():
    """下载导入模板 - 基于32个新字段"""
    try:
        current_app.logger.info('开始生成导入模板')
        
        # 基于用户要求创建32个字段的模板数据
        template_data = {
            '项目编号': ['PROJ001', 'PROJ002'],
            '序号': [1, 2],
            'Anlauf编号': ['12345678', '12345679'],
            '创建日期': ['2024-01-15 09:00:00', '2024-01-16 10:00:00'],
            '问题上报周次': [3, 4],
            '审核状态': ['待审核', '已审核'],
            '工程师首次审核时间': ['2024-01-16 14:00:00', '2024-01-17 15:00:00'],
            '工程师首次审核周次': [3, 4],
            'VIN': ['1HGBH41JXMN109186', '1HGBH41JXMN109187'],
            '抱怨类别': ['PDI', '售后'],
            'KDNR': ['1234', '5678'],
            'BANR': ['BAN001', 'BAN002'],
            '抛锚': ['否', '是'],
            '售后故障描述': ['发动机异常噪音', '刹车系统故障'],
            '里程': [1500.5, 2300.8],
            'ZP8报交日期': ['2024-01-20 16:00:00', '2024-01-21 17:00:00'],
            '部门': ['质量部', '技术部'],
            '工程师': ['张工程师', '李工程师'],
            '分析/措施': ['检查发动机参数，调整怠速', '更换刹车片，检查制动液'],
            '仓库收到故障件': ['是', '否'],
            '维修站号+中文名': ['001-北京维修站', '002-上海维修站'],
            '问题上报日期': ['2024-01-15 08:30:00', '2024-01-16 09:45:00'],
            '车辆状态': ['商品车', '试驾车'],
            '是否返工车辆': ['否', '是'],
            '议题状态': ['待确认上会', '讨论中'],
            '问题描述': ['车辆启动时发动机有异常噪音', '刹车踏板感觉偏软，制动距离增加'],
            '上会议题名': ['发动机噪音问题分析', '刹车系统性能优化'],
            '标记': [1, 2],
            '故障件状态': ['已收到', '待收取'],
            '问题类型': ['质量问题', '设计缺陷'],
            '问题是否上会': ['是', '否'],
            '抱怨是否上会': ['是', '否']
        }
        
        df = pd.DataFrame(template_data)
        current_app.logger.info(f'模板数据创建成功，包含 {len(template_data)} 个字段')
        
        # 确保上传目录存在
        upload_folder = current_app.config['UPLOAD_FOLDER']
        if not os.path.exists(upload_folder):
            current_app.logger.info(f'创建上传目录: {upload_folder}')
            os.makedirs(upload_folder)
        
        # 保存为Excel文件
        template_path = os.path.join(upload_folder, 'template.xlsx')
        current_app.logger.info(f'保存模板到: {template_path}')
        
        try:
            with pd.ExcelWriter(template_path, engine='openpyxl') as writer:
                # 主数据表
                df.to_excel(writer, sheet_name='议题数据', index=False)
                current_app.logger.info('议题数据表写入成功')
                
                # 字段说明表
                field_descriptions = create_field_descriptions()
                field_df = pd.DataFrame(field_descriptions)
                field_df.to_excel(writer, sheet_name='字段说明', index=False)
                current_app.logger.info('字段说明表写入成功')
        
        except Exception as e:
            current_app.logger.error(f'写入Excel文件失败: {e}', exc_info=True)
            # 如果写入Excel失败，生成CSV文件
            csv_path = os.path.join(upload_folder, 'template.csv')
            df.to_csv(csv_path, index=False, encoding='utf-8-sig')
            current_app.logger.info(f'生成CSV模板文件: {csv_path}')
            return jsonify({
                'success': True,
                'message': '模板生成成功（CSV格式）',
                'download_url': '/static/uploads/template.csv'
            })
        
        current_app.logger.info('模板文件生成完成')
        
        return jsonify({
            'success': True,
            'message': '模板生成成功',
            'download_url': '/static/uploads/template.xlsx'
        })
        
    except Exception as e:
        current_app.logger.error(f'模板生成失败: {e}', exc_info=True)
        return jsonify({'success': False, 'message': f'模板生成失败: {str(e)}'}), 500


def create_field_descriptions():
    """创建字段说明数据 - 基于32个新字段"""
    return [
        {'字段名称': '项目编号', '字段属性': '字符串', '必填': '否', '说明': '项目的唯一编号标识'},
        {'字段名称': '序号', '字段属性': '整数', '必填': '否', '说明': '议题序列号'},
        {'字段名称': 'Anlauf编号', '字段属性': '8位数字', '必填': '否', '说明': 'Anlauf编号，8位数字格式'},
        {'字段名称': '创建日期', '字段属性': '日期时间', '必填': '否', '说明': '议题创建的日期时间'},
        {'字段名称': '问题上报周次', '字段属性': '整数', '必填': '否', '说明': '问题上报的周次'},
        {'字段名称': '审核状态', '字段属性': '字符串', '必填': '否', '说明': '当前审核状态'},
        {'字段名称': '工程师首次审核时间', '字段属性': '日期时间', '必填': '否', '说明': '工程师第一次审核的时间'},
        {'字段名称': '工程师首次审核周次', '字段属性': '整数', '必填': '否', '说明': '工程师第一次审核的周次'},
        {'字段名称': 'VIN', '字段属性': '17位字符', '必填': '否', '说明': '车辆识别代码，17位字符'},
        {'字段名称': '抱怨类别', '字段属性': 'PDI/售后', '必填': '否', '说明': '抱怨类别，选择PDI或售后'},
        {'字段名称': 'KDNR', '字段属性': '4位字符串', '必填': '否', '说明': 'KDNR编码，4位字符串'},
        {'字段名称': 'BANR', '字段属性': '字符串', '必填': '否', '说明': 'BANR编码'},
        {'字段名称': '抛锚', '字段属性': '是/否', '必填': '否', '说明': '是否发生抛锚情况'},
        {'字段名称': '售后故障描述', '字段属性': '字符串', '必填': '否', '说明': '售后服务中的故障详细描述'},
        {'字段名称': '里程', '字段属性': '数字', '必填': '否', '说明': '车辆行驶里程数'},
        {'字段名称': 'ZP8报交日期', '字段属性': '日期时间', '必填': '否', '说明': 'ZP8报告提交日期'},
        {'字段名称': '部门', '字段属性': '字符串', '必填': '否', '说明': '负责处理的部门'},
        {'字段名称': '工程师', '字段属性': '字符串', '必填': '否', '说明': '负责的工程师姓名'},
        {'字段名称': '分析/措施', '字段属性': '字符串', '必填': '否', '说明': '问题分析和解决措施'},
        {'字段名称': '仓库收到故障件', '字段属性': '是/否', '必填': '否', '说明': '仓库是否已收到故障件'},
        {'字段名称': '维修站号+中文名', '字段属性': '字符串', '必填': '否', '说明': '维修站编号和中文名称'},
        {'字段名称': '问题上报日期', '字段属性': '日期时间', '必填': '否', '说明': '问题最初上报的日期'},
        {'字段名称': '车辆状态', '字段属性': '售前车/试驾车/展车/商品车', '必填': '否', '说明': '车辆的当前状态分类'},
        {'字段名称': '是否返工车辆', '字段属性': '是/否', '必填': '否', '说明': '是否为返工车辆'},
        {'字段名称': '议题状态', '字段属性': '枚举值', '必填': '否', '说明': '议题的当前处理状态'},
        {'字段名称': '问题描述', '字段属性': '字符串', '必填': '否', '说明': '问题的详细描述'},
        {'字段名称': '上会议题名', '字段属性': '字符串', '必填': '否', '说明': '在会议中讨论的议题名称'},
        {'字段名称': '标记', '字段属性': '整数', '必填': '否', '说明': '议题标记号'},
        {'字段名称': '故障件状态', '字段属性': '字符串', '必填': '否', '说明': '故障件的当前状态'},
        {'字段名称': '问题类型', '字段属性': '字符串', '必填': '否', '说明': '问题的类型分类'},
        {'字段名称': '问题是否上会', '字段属性': '是/否', '必填': '否', '说明': '问题是否需要在会议中讨论'},
        {'字段名称': '抱怨是否上会', '字段属性': '是/否', '必填': '否', '说明': '抱怨是否需要在会议中讨论'}
    ]


def allowed_file(filename, allowed_extensions):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in allowed_extensions


def parse_file(file_path):
    """解析上传的文件"""
    file_ext = file_path.rsplit('.', 1)[1].lower()
    
    try:
        current_app.logger.info(f'开始解析文件: {file_path}, 格式: {file_ext}')
        
        if file_ext in ['xlsx', 'xls']:
            df = pd.read_excel(file_path, sheet_name=0)
        elif file_ext == 'csv':
            # 尝试不同的编码
            try:
                df = pd.read_csv(file_path, encoding='utf-8')
            except UnicodeDecodeError:
                try:
                    df = pd.read_csv(file_path, encoding='gbk')
                except UnicodeDecodeError:
                    df = pd.read_csv(file_path, encoding='latin-1')
        else:
            raise ValueError(f'不支持的文件格式: {file_ext}')
        
        current_app.logger.info(f'文件解析成功，共 {len(df)} 行数据，列: {list(df.columns)}')
        
        # 转换为字典列表，并处理NaN值
        data = []
        for index, row in df.iterrows():
            row_dict = {}
            for col, value in row.items():
                # 处理NaN值和空值
                if pd.isna(value) or value == '' or value is None:
                    row_dict[col] = None
                else:
                    row_dict[col] = str(value).strip()
            data.append(row_dict)
        
        # 获取列名
        columns = list(df.columns)
        
        current_app.logger.info(f'数据转换完成，有效数据行数: {len(data)}')
        
        return {
            'columns': columns,
            'data': data,
            'total_rows': len(df)
        }
        
    except Exception as e:
        current_app.logger.error(f'文件解析失败: {e}', exc_info=True)
        raise Exception(f'文件解析失败: {str(e)}')


def convert_row_to_issue(row, mapping=None):
    """将数据行转换为议题数据 - 基于32个新字段"""
    issue_data = {}
    
    current_app.logger.info(f'开始转换数据行，原始字段: {list(row.keys())}')
    
    # 获取字段映射表
    field_mapping = get_field_mapping()
    
    # 如果没有提供映射，智能判断使用哪种映射
    if not mapping:
        # 检测是否为新的32字段格式
        has_new_fields = any(key in row for key in ['项目编号', 'Anlauf编号', '序号', '问题上报周次'])
        has_old_fields = any(key in row for key in ['关键字', '概要', '状态', '严重度'])
        has_english_fields = any(key in row for key in ['project_number', 'anlauf_number', 'sequence_number'])
        
        if has_new_fields and not has_old_fields:
            # 新的32字段格式
            current_app.logger.info('检测到新的32字段格式，使用新字段映射')
            mapping = field_mapping
        elif has_english_fields:
            # 英文字段名直接映射
            current_app.logger.info('检测到英文字段名，使用直接映射')
            mapping = {key: key for key in row.keys() if key in field_mapping.values()}
        elif has_old_fields:
            # 旧字段格式，提供向后兼容
            current_app.logger.info('检测到旧字段格式，使用兼容性映射')
            mapping = {
                '关键字': 'anlauf_number',
                '概要': 'problem_description',
                '状态': 'issue_status',
                '严重度': 'severity',
                '项目': 'project_number',
            }
        else:
            current_app.logger.info('使用默认字段映射')
            mapping = field_mapping
    
    # 处理字段映射
    for source_field, target_field in mapping.items():
        if source_field in row:
            value = row[source_field]
            if value is not None and str(value).strip() and str(value).strip().lower() not in ['nan', 'none', '']:
                # 处理不同类型的字段
                processed_value = process_field_value(target_field, value)
                if processed_value is not None:
                    issue_data[target_field] = processed_value
    
    current_app.logger.info(f'转换后的数据字段: {list(issue_data.keys())}')
    
    # 验证必填字段 - Anlauf编号作为主要标识
    if not issue_data.get('anlauf_number'):
        # 如果没有Anlauf编号，尝试从其他字段生成
        if issue_data.get('project_number') and issue_data.get('sequence_number'):
            # 基于项目编号和序号生成临时Anlauf编号
            temp_anlauf = f"{issue_data.get('project_number', '')[:4]}{issue_data.get('sequence_number', 0):04d}"
            if len(temp_anlauf) <= 8:
                issue_data['anlauf_number'] = temp_anlauf
                current_app.logger.info(f'自动生成Anlauf编号: {temp_anlauf}')
            else:
                raise ValueError('缺少必填字段: Anlauf编号，且无法自动生成')
        else:
            raise ValueError('缺少必填字段: Anlauf编号')
    
    return issue_data


def process_field_value(target_field, value):
    """处理字段值，根据字段类型进行转换"""
    try:
        # 布尔值字段
        if target_field in ['breakdown', 'warehouse_received_fault_parts', 'is_rework_vehicle', 
                           'problem_in_meeting', 'complaint_in_meeting']:
            if str(value).strip().lower() in ['是', 'true', '1', 'yes', 'y', '1.0']:
                return True
            elif str(value).strip().lower() in ['否', 'false', '0', 'no', 'n', '0.0']:
                return False
            else:
                current_app.logger.warning(f'无法识别的布尔值: {value}，字段: {target_field}')
                return None
                
        # 整数字段
        elif target_field in ['sequence_number', 'problem_report_week', 'engineer_first_review_week', 'mark']:
            return safe_int(value)
            
        # 数字字段
        elif target_field in ['mileage']:
            try:
                return float(str(value).strip())
            except (ValueError, TypeError):
                current_app.logger.warning(f'无法转换数字值: {value}，字段: {target_field}')
                return None
                
        # 日期时间字段
        elif target_field in ['created_at', 'engineer_first_review_time', 'zp8_delivery_date', 'problem_report_date']:
            return safe_datetime(value)
            
        # 字符串长度限制字段
        elif target_field == 'anlauf_number':
            anlauf_value = str(value).strip()
            if len(anlauf_value) > 8:
                current_app.logger.warning(f'Anlauf编号长度超过8位，将截断: {anlauf_value}')
                anlauf_value = anlauf_value[:8]
            # 验证是否为数字
            if not anlauf_value.isdigit():
                current_app.logger.warning(f'Anlauf编号必须为数字: {anlauf_value}')
            return anlauf_value
            
        elif target_field == 'vin':
            vin_value = str(value).strip()
            if len(vin_value) > 17:
                current_app.logger.warning(f'VIN码长度超过17位，将截断: {vin_value}')
                vin_value = vin_value[:17]
            return vin_value
            
        elif target_field == 'kdnr':
            kdnr_value = str(value).strip()
            if len(kdnr_value) > 4:
                current_app.logger.warning(f'KDNR长度超过4位，将截断: {kdnr_value}')
                kdnr_value = kdnr_value[:4]
            return kdnr_value
            
        # 枚举字段
        elif target_field == 'complaint_category':
            category_value = str(value).strip()
            if category_value in ['PDI', 'pdi']:
                return 'PDI'
            elif category_value in ['售后', '售后服务', 'AFTER_SALES', 'after_sales']:
                return 'AFTER_SALES'
            else:
                current_app.logger.warning(f'未知的抱怨类别: {category_value}')
                return category_value
                
        elif target_field == 'vehicle_status':
            status_value = str(value).strip()
            status_mapping = {
                '售前车': 'PRE_SALE_PDI',  # 售前车归类为售前PDI
                '试驾车': 'TEST_DRIVE',
                '展车': 'DISPLAY',
                '商品车': 'PRE_SALE_PDI',  # 商品车归类为售前PDI
                '客户车': 'CUSTOMER_CAR',
                '售前PDI': 'PRE_SALE_PDI'
            }
            return status_mapping.get(status_value, status_value)
            
        elif target_field == 'issue_status':
            # 议题状态的映射
            status_value = str(value).strip()
            return status_value  # 保持原值，让数据库处理
            
        else:
            # 普通字符串字段
            return str(value).strip()
            
    except Exception as e:
        current_app.logger.error(f'处理字段值失败: {target_field} = {value}, 错误: {e}')
        return None


def create_issue_from_data(data):
    """从数据创建议题对象 - 基于32个新字段"""
    # 在函数开始就导入所有需要的枚举类
    from app.models.enums import IssueStatus, ComplaintCategory, VehicleStatus, MaintenanceStatus
    
    issue = Issue()
    
    # 1. Anlauf编号（主要标识字段）
    anlauf_number = data.get('anlauf_number')
    if not anlauf_number:
        raise ValueError('Anlauf编号不能为空')
    issue.anlauf_number = anlauf_number
    
    # 设置基本字段
    basic_fields = [
        'project_number', 'sequence_number', 'created_at', 'problem_report_week',
        'review_status', 'engineer_first_review_time', 'engineer_first_review_week',
        'vin', 'kdnr', 'banr', 'breakdown', 'after_sales_fault_description',
        'mileage', 'zp8_delivery_date', 'department', 'engineer', 'analysis_measures',
        'warehouse_received_fault_parts', 'repair_station_info', 'problem_report_date',
        'is_rework_vehicle', 'problem_description', 'meeting_topic_name', 'mark',
        'fault_parts_status', 'problem_type', 'problem_in_meeting', 'complaint_in_meeting'
    ]
    
    for field in basic_fields:
        if data.get(field) is not None:
            setattr(issue, field, data.get(field))
    
    # 10. 抱怨类别
    if data.get('complaint_category'):
        try:
            if data.get('complaint_category') == 'PDI':
                issue.complaint_category = ComplaintCategory.PDI
            elif data.get('complaint_category') in ['售后', 'AFTER_SALES']:
                issue.complaint_category = ComplaintCategory.AFTER_SALES
        except Exception as e:
            current_app.logger.warning(f'设置抱怨类别失败: {e}')
    
    # 23. 车辆状态
    if data.get('vehicle_status'):
        try:
            status_mapping = {
                'PRE_SALE': VehicleStatus.PRE_SALE_PDI,  # 旧的售前车映射到售前PDI
                'PRE_SALE_PDI': VehicleStatus.PRE_SALE_PDI,
                'TEST_DRIVE': VehicleStatus.TEST_DRIVE,
                'DISPLAY': VehicleStatus.DISPLAY,
                'COMMODITY': VehicleStatus.PRE_SALE_PDI,  # 旧的商品车映射到售前PDI
                'COMMERCIAL': VehicleStatus.PRE_SALE_PDI,  # 兼容旧的商品车
                'CUSTOMER_CAR': VehicleStatus.CUSTOMER_CAR
            }
            if data.get('vehicle_status') in status_mapping:
                issue.vehicle_status = status_mapping[data.get('vehicle_status')]
        except Exception as e:
            current_app.logger.warning(f'设置车辆状态失败: {e}')
    
    # 25. 议题状态
    try:
        if data.get('issue_status'):
            status_value = str(data.get('issue_status')).strip()

            # 完整的议题状态映射表
            status_mapping = {
                # 中文标签映射
                '已上Pre-Anlauf': IssueStatus.PRE_ANLAUF,
                '已上会，分析中': IssueStatus.MEETING_ANALYZING,
                '已上会，分析完成': IssueStatus.MEETING_ANALYSIS_COMPLETE,
                '已上会，措施制定': IssueStatus.MEETING_MEASURES_FORMULATING,
                '已上会，措施落实': IssueStatus.MEETING_MEASURES_IMPLEMENTING,
                '已上会，继续跟踪': IssueStatus.MEETING_CONTINUE_TRACKING,
                '未上会，确认中': IssueStatus.NO_MEETING_CONFIRMING,
                '未上会，继续跟踪': IssueStatus.NO_MEETING_CONTINUE_TRACKING,
                '未上会，无效抱怨': IssueStatus.NO_MEETING_INVALID_COMPLAINT,
                '未上会，已定义措施': IssueStatus.NO_MEETING_MEASURES_DEFINED,
                '未上会，措施实施': IssueStatus.NO_MEETING_MEASURES_IMPLEMENTING,
                # 英文枚举值映射（兼容直接使用枚举值的情况）
                'PRE_ANLAUF': IssueStatus.PRE_ANLAUF,
                'MEETING_ANALYZING': IssueStatus.MEETING_ANALYZING,
                'MEETING_ANALYSIS_COMPLETE': IssueStatus.MEETING_ANALYSIS_COMPLETE,
                'MEETING_MEASURES_FORMULATING': IssueStatus.MEETING_MEASURES_FORMULATING,
                'MEETING_MEASURES_IMPLEMENTING': IssueStatus.MEETING_MEASURES_IMPLEMENTING,
                'MEETING_CONTINUE_TRACKING': IssueStatus.MEETING_CONTINUE_TRACKING,
                'NO_MEETING_CONFIRMING': IssueStatus.NO_MEETING_CONFIRMING,
                'NO_MEETING_CONTINUE_TRACKING': IssueStatus.NO_MEETING_CONTINUE_TRACKING,
                'NO_MEETING_INVALID_COMPLAINT': IssueStatus.NO_MEETING_INVALID_COMPLAINT,
                'NO_MEETING_MEASURES_DEFINED': IssueStatus.NO_MEETING_MEASURES_DEFINED,
                'NO_MEETING_MEASURES_IMPLEMENTING': IssueStatus.NO_MEETING_MEASURES_IMPLEMENTING,
                # 兼容旧的状态值
                '待确认上会': IssueStatus.NO_MEETING_CONFIRMING,
                '讨论中': IssueStatus.MEETING_ANALYZING,
            }

            if status_value in status_mapping:
                issue.issue_status = status_mapping[status_value]
                current_app.logger.info(f'议题状态映射成功: {status_value} -> {status_mapping[status_value].value}')
            else:
                current_app.logger.warning(f'未知的议题状态值: {status_value}，使用默认状态')
                issue.issue_status = IssueStatus.NO_MEETING_CONFIRMING
        else:
            issue.issue_status = IssueStatus.NO_MEETING_CONFIRMING
    except Exception as e:
        current_app.logger.warning(f'设置议题状态失败: {e}')
        issue.issue_status = IssueStatus.NO_MEETING_CONFIRMING
    
    # 设置兼容性字段
    if issue.anlauf_number:
        issue.key_field = issue.anlauf_number
    if issue.project_number:
        issue.project = issue.project_number
    if issue.vin:
        issue.vehicle_vin = issue.vin
    
    # 设置默认值
    issue.maintenance_status = MaintenanceStatus.PENDING
    
    current_app.logger.info(f'创建议题对象完成: Anlauf编号={issue.anlauf_number}')
    return issue


def update_issue_from_data(issue, data):
    """更新现有议题并跟踪字段更新"""
    from app.utils.timezone_utils import beijing_now
    
    current_app.logger.info(f'开始更新议题 {issue.anlauf_number}')
    
    updated_fields = []
    
    for field_name, new_value in data.items():
        if field_name in ['anlauf_number', 'project_number']:
            continue  # 跳过主键字段
            
        if hasattr(issue, field_name):
            old_value = getattr(issue, field_name)
            
            # 智能比较字段值，处理不同数据类型
            old_str = str(old_value) if old_value is not None else ''
            new_str = str(new_value) if new_value is not None else ''
            
            # 对于空值的特殊处理
            if old_str in ['None', 'nan', ''] and new_str in ['None', 'nan', '']:
                continue
                
            if old_str != new_str:
                setattr(issue, field_name, new_value)
                updated_fields.append(field_name)
                current_app.logger.info(f'更新字段 {field_name}: {old_value} -> {new_value}')
    
    # 如果有字段更新，设置更新标记
    if updated_fields:
        issue.set_updated_fields(updated_fields)
        # 自动将维护状态设为待维护
        if hasattr(issue, 'maintenance_status'):
            from app.models.enums import MaintenanceStatus
            issue.maintenance_status = MaintenanceStatus.PENDING
        # 更新时间戳
        issue.updated_at = beijing_now()
        current_app.logger.info(f'议题 {issue.anlauf_number} 有 {len(updated_fields)} 个字段更新，已设置更新标记')
    
    return updated_fields


def safe_int(value):
    """安全转换为整数"""
    if value is None or value == '':
        return None
    try:
        return int(float(value))
    except (ValueError, TypeError):
        return None


def safe_datetime(value):
    """安全转换为日期时间"""
    if not value:
        return None
    
    if isinstance(value, str):
        value = value.strip()
        if not value or value.lower() in ['nan', 'none', 'null']:
            return None
    
    try:
        value_str = str(value)
        
        # 检查是否是DD-MM-YYYY HH:MM:SS格式
        if len(value_str) >= 19 and '-' in value_str[:10]:
            try:
                parsed_datetime = datetime.strptime(value_str[:19], '%d-%m-%Y %H:%M:%S')
                return parsed_datetime
            except ValueError:
                pass
        
        # 检查是否是DD-MM-YYYY格式（只有日期）
        elif len(value_str) >= 10 and '-' in value_str[:10]:
            try:
                parsed_datetime = datetime.strptime(value_str[:10], '%d-%m-%Y')
                return parsed_datetime
            except ValueError:
                pass
        
        # 使用pandas解析
        parsed_datetime = pd.to_datetime(value, dayfirst=True)
        if hasattr(parsed_datetime, 'to_pydatetime'):
            return parsed_datetime.to_pydatetime().replace(tzinfo=None)
        else:
            return parsed_datetime.replace(tzinfo=None)
        
    except Exception as e:
        current_app.logger.warning(f'无法解析日期时间格式: {value}, 错误: {e}')
        return None


def detect_duplicates(data):
    """检测重复记录 - 基于Anlauf编号进行重复检测"""
    if not data or len(data) < 2:
        return {
            'has_duplicates': False,
            'duplicate_rows': [],
            'duplicate_count': 0,
            'total_rows': len(data) if data else 0,
            'duplicate_summary': {},
            'db_duplicates': [],
            'db_duplicate_count': 0
        }
    
    duplicate_rows = set()
    duplicate_groups = {}
    
    # 智能检测字段名类型
    sample_row = data[0] if data else {}
    anlauf_field_name = None
    
    if 'Anlauf编号' in sample_row:
        anlauf_field_name = 'Anlauf编号'
    elif 'anlauf_number' in sample_row:
        anlauf_field_name = 'anlauf_number'
    elif '关键字' in sample_row:
        anlauf_field_name = '关键字'
    elif 'key_field' in sample_row:
        anlauf_field_name = 'key_field'
    else:
        current_app.logger.warning('未找到Anlauf编号字段，无法进行重复检测')
        return {
            'has_duplicates': False,
            'duplicate_rows': [],
            'duplicate_count': 0,
            'total_rows': len(data),
            'duplicate_summary': {},
            'db_duplicates': [],
            'db_duplicate_count': 0
        }
    
    # 使用Anlauf编号作为唯一标识符进行重复检测
    for i, row in enumerate(data):
        anlauf_number = row.get(anlauf_field_name, '').strip()
        
        if not anlauf_number or str(anlauf_number).lower() in ['', 'nan', 'none']:
            continue
        
        normalized_anlauf = str(anlauf_number).strip()
        
        if normalized_anlauf in duplicate_groups:
            duplicate_groups[normalized_anlauf].append(i)
            duplicate_rows.add(i)
            duplicate_rows.add(duplicate_groups[normalized_anlauf][0])
        else:
            duplicate_groups[normalized_anlauf] = [i]
    
    # 检查与数据库中已存在记录的重复
    db_duplicates = check_database_duplicates(data, anlauf_field_name)
    
    return {
        'has_duplicates': len(duplicate_rows) > 0 or len(db_duplicates) > 0,
        'duplicate_rows': list(duplicate_rows),
        'duplicate_count': len(duplicate_rows),
        'total_rows': len(data),
        'duplicate_summary': {},
        'db_duplicates': db_duplicates,
        'db_duplicate_count': len(db_duplicates)
    }


def check_database_duplicates(data, anlauf_field_name='Anlauf编号'):
    """检查数据库中已存在的重复记录 - 基于项目编号和Anlauf编号"""
    db_duplicates = []
    
    try:
        # 智能检测项目编号字段名
        sample_row = data[0] if data else {}
        project_field_name = None
        
        if '项目编号' in sample_row:
            project_field_name = '项目编号'
        elif 'project_number' in sample_row:
            project_field_name = 'project_number'
        elif 'project' in sample_row:
            project_field_name = 'project'
        
        for i, row in enumerate(data):
            anlauf_number = row.get(anlauf_field_name, '').strip()
            project_number = row.get(project_field_name, '').strip() if project_field_name else None
            
            if anlauf_number and str(anlauf_number).lower() not in ['', 'nan', 'none']:
                existing_issue = None
                
                # 优先使用项目编号+Anlauf编号进行匹配
                if project_number and str(project_number).lower() not in ['', 'nan', 'none']:
                    existing_issue = Issue.query.filter_by(
                        project_number=str(project_number),
                        anlauf_number=str(anlauf_number)
                    ).first()
                
                # 如果没有匹配到，仍然尝试仅用Anlauf编号匹配
                if not existing_issue:
                    existing_issue = Issue.query.filter_by(anlauf_number=str(anlauf_number)).first()
                
                if existing_issue:
                    db_duplicates.append({
                        'row_index': i,
                        'anlauf_number': anlauf_number,
                        'project_number': project_number,
                        'existing_id': existing_issue.id,
                        'existing_summary': existing_issue.problem_description or existing_issue.after_sales_fault_description or '无描述'
                    })
    except Exception as e:
        current_app.logger.error(f'检查数据库重复记录失败: {e}')
    
    return db_duplicates


def get_field_mapping():
    """获取字段名到数据库字段的映射表 - 更新为32个新字段"""
    return {
        # 32个新字段的映射
        '项目编号': 'project_number',
        '序号': 'sequence_number',
        'Anlauf编号': 'anlauf_number',
        '创建日期': 'created_at',
        '问题上报周次': 'problem_report_week',
        '审核状态': 'review_status',
        '工程师首次审核时间': 'engineer_first_review_time',
        '工程师首次审核周次': 'engineer_first_review_week',
        'VIN': 'vin',
        '抱怨类别': 'complaint_category',
        'KDNR': 'kdnr',
        'BANR': 'banr',
        '抛锚': 'breakdown',
        '售后故障描述': 'after_sales_fault_description',
        '里程': 'mileage',
        'ZP8报交日期': 'zp8_delivery_date',
        '部门': 'department',
        '工程师': 'engineer',
        '分析/措施': 'analysis_measures',
        '仓库收到故障件': 'warehouse_received_fault_parts',
        '维修站号+中文名': 'repair_station_info',
        '问题上报日期': 'problem_report_date',
        '车辆状态': 'vehicle_status',
        '是否返工车辆': 'is_rework_vehicle',
        '议题状态': 'issue_status',
        '问题描述': 'problem_description',
        '上会议题名': 'meeting_topic_name',
        '标记': 'mark',
        '故障件状态': 'fault_parts_status',
        '问题类型': 'problem_type',
        '问题是否上会': 'problem_in_meeting',
        '抱怨是否上会': 'complaint_in_meeting',
        
        # 向后兼容的映射
        'project_number': 'project_number',
        'sequence_number': 'sequence_number',
        'anlauf_number': 'anlauf_number',
        'created_at': 'created_at',
        'problem_report_week': 'problem_report_week',
        'review_status': 'review_status',
        'engineer_first_review_time': 'engineer_first_review_time',
        'engineer_first_review_week': 'engineer_first_review_week',
        'vin': 'vin',
        'complaint_category': 'complaint_category',
        'kdnr': 'kdnr',
        'banr': 'banr',
        'breakdown': 'breakdown',
        'after_sales_fault_description': 'after_sales_fault_description',
        'mileage': 'mileage',
        'zp8_delivery_date': 'zp8_delivery_date',
        'department': 'department',
        'engineer': 'engineer',
        'analysis_measures': 'analysis_measures',
        'warehouse_received_fault_parts': 'warehouse_received_fault_parts',
        'repair_station_info': 'repair_station_info',
        'problem_report_date': 'problem_report_date',
        'vehicle_status': 'vehicle_status',
        'is_rework_vehicle': 'is_rework_vehicle',
        'issue_status': 'issue_status',
        'problem_description': 'problem_description',
        'meeting_topic_name': 'meeting_topic_name',
        'mark': 'mark',
        'fault_parts_status': 'fault_parts_status',
        'problem_type': 'problem_type',
        'problem_in_meeting': 'problem_in_meeting',
        'complaint_in_meeting': 'complaint_in_meeting',
    } 