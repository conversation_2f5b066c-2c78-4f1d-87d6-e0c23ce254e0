"""
议题数据模型
包含议题的完整数据结构和业务逻辑
"""

from datetime import datetime
from sqlalchemy import Index, Table
from app import db
from .enums import IssueStatus, MaintenanceStatus, ComplaintCategory, VehicleStatus
from app.utils.timezone_utils import beijing_now


# 议题标签关联表（多对多关系）
issue_tags = Table('issue_tags', db.metadata,
    db.Column('issue_id', db.Integer, db.ForeignKey('issues.id'), primary_key=True),
    db.Column('tag_id', db.Integer, db.ForeignKey('tags.id'), primary_key=True),
    db.Column('created_at', db.DateTime, default=beijing_now)
)


class Issue(db.Model):
    """
    议题模型类
    
    根据用户要求重新设计的Profi-Anlauf议题数据结构
    """
    
    __tablename__ = 'issues'
    
    # 基础标识字段
    id = db.Column(db.Integer, primary_key=True, comment='主键ID')
    
    # 1. 项目编号(字符串)
    project_number = db.Column(db.String(50), nullable=True, index=True, comment='项目编号')
    
    # 2. 序号(整数)
    sequence_number = db.Column(db.Integer, nullable=True, index=True, comment='序号')
    
    # 3. Anlauf编号(8位数字)
    anlauf_number = db.Column(db.String(8), nullable=True, unique=True, index=True, comment='Anlauf编号-8位数字')
    
    # 4. 创建日期(日期时间)
    created_at = db.Column(db.DateTime, default=beijing_now, nullable=False, comment='创建日期')
    
    # 5. 问题上报周次(整数)
    problem_report_week = db.Column(db.Integer, comment='问题上报周次')
    
    # 6. 审核状态(字符串)
    review_status = db.Column(db.String(50), comment='审核状态')
    
    # 7. 工程师首次审核时间(日期时间)
    engineer_first_review_time = db.Column(db.DateTime, comment='工程师首次审核时间')
    
    # 8. 工程师首次审核周次(整数)
    engineer_first_review_week = db.Column(db.Integer, comment='工程师首次审核周次')
    
    # 9. VIN(17位字符)
    vin = db.Column(db.String(17), comment='VIN码-17位字符')
    
    # 10. 抱怨类别(枚举值，PDI/售后)
    # 🔄 修改：临时改为字符串类型以兼容旧数据中的错误值
    _complaint_category = db.Column('complaint_category', db.String(50), comment='抱怨类别')
    
    # 11. KDNR(4位字符串)
    kdnr = db.Column(db.String(4), comment='KDNR-4位字符串')
    
    # 12. BANR(字符串)
    banr = db.Column(db.String(100), comment='BANR')
    
    # 13. 抛锚(布尔值，是/否)
    breakdown = db.Column(db.Boolean, default=False, comment='抛锚-是/否')
    
    # 14. 售后故障描述(字符串)
    after_sales_fault_description = db.Column(db.Text, comment='售后故障描述')
    
    # 15. 里程(数字)
    mileage = db.Column(db.Numeric(10, 2), comment='里程')
    
    # 16. ZP8报交日期(日期时间)
    zp8_delivery_date = db.Column(db.DateTime, comment='ZP8报交日期')
    
    # 17. 部门(字符串)
    department = db.Column(db.String(100), comment='部门')
    
    # 18. 工程师(字符串)
    engineer = db.Column(db.String(100), comment='工程师')
    
    # 19. 分析/措施(字符串)
    analysis_measures = db.Column(db.Text, comment='分析/措施')
    
    # 20. 仓库收到故障件(布尔值，是/否)
    warehouse_received_fault_parts = db.Column(db.Boolean, default=False, comment='仓库收到故障件-是/否')
    
    # 21. 维修站号+中文名(字符串)
    repair_station_info = db.Column(db.String(200), comment='维修站号+中文名')
    
    # 22. 问题上报日期(日期时间)
    problem_report_date = db.Column(db.DateTime, comment='问题上报日期')
    
    # 23. 车辆状态(枚举值，售前车/试驾车/展车/商品车)
    # 🔄 修改：临时改为字符串类型以兼容旧数据中的'客户车'值
    _vehicle_status = db.Column('vehicle_status', db.String(50), comment='车辆状态')
    
    # 24. 是否返工车辆(布尔值，是/否)
    is_rework_vehicle = db.Column(db.Boolean, default=False, comment='是否返工车辆-是/否')
    
    # 🔄 修改：将议题状态字段临时改为字符串类型以避免枚举错误
    _issue_status = db.Column('issue_status', db.String(100), default='NO_MEETING_CONFIRMING', 
                            nullable=False, index=True, comment='议题状态')
    
    # 26. 问题描述(字符串)
    problem_description = db.Column(db.Text, comment='问题描述')
    
    # 27. 上会议题名(字符串)
    meeting_topic_name = db.Column(db.String(200), comment='上会议题名')
    
    # 28. 标记(整数)
    mark = db.Column(db.Integer, comment='标记')
    
    # 29. 故障件状态(字符串)
    fault_parts_status = db.Column(db.String(100), comment='故障件状态')
    
    # 30. 问题类型(字符串)
    problem_type = db.Column(db.String(100), comment='问题类型')
    
    # 31. 问题是否上会(布尔值，是/否)
    problem_in_meeting = db.Column(db.Boolean, default=False, comment='问题是否上会-是/否')
    
    # 32. 抱怨是否上会(布尔值，是/否)
    complaint_in_meeting = db.Column(db.Boolean, default=False, comment='抱怨是否上会-是/否')
    
    # 兼容性字段（保持向后兼容）
    severity = db.Column(db.String(1), comment='严重度-1位字符')
    handler = db.Column(db.String(100), comment='经办人')
    reporter = db.Column(db.String(100), comment='报告人')
    project = db.Column(db.String(50), comment='项目名称（兼容性字段）')
    key_field = db.Column(db.String(11), comment='关键字（兼容性字段）')
    vehicle_vin = db.Column(db.String(17), comment='车辆VIN（兼容性字段）')
    
    # 系统维护字段
    updated_at = db.Column(db.DateTime, default=beijing_now, onupdate=beijing_now, 
                          nullable=False, comment='更新时间')
    maintenance_status = db.Column(db.Enum(MaintenanceStatus), default=MaintenanceStatus.PENDING, 
                                  comment='维护状态（快速操作）')
    
    # 字段更新跟踪（用于标识导入更新的字段）
    updated_fields = db.Column(db.Text, comment='已更新的字段列表（JSON格式）')
    has_updates = db.Column(db.Boolean, default=False, comment='是否有字段更新')
    last_import_update_time = db.Column(db.DateTime, comment='最后一次导入更新时间')
    
    # AI标签状态字段
    ai_tag_status = db.Column(db.String(20), default='none', comment='AI标签状态：none/processing/completed/error')
    ai_tag_updated_at = db.Column(db.DateTime, comment='AI标签更新时间')
    ai_tag_error_message = db.Column(db.Text, comment='AI标签错误信息')
    
    # 🔄 添加议题状态的智能转换属性
    @property
    def issue_status(self):
        """智能转换议题状态"""
        status_value = self._issue_status
        
        # 中文标签到英文枚举值的映射
        chinese_to_english = {
            '已上Pre-Anlauf': 'PRE_ANLAUF',
            '已上会，分析中': 'MEETING_ANALYZING',
            '已上会，分析完成': 'MEETING_ANALYSIS_COMPLETE',
            '已上会，措施制定': 'MEETING_MEASURES_FORMULATING',
            '已上会，措施落实': 'MEETING_MEASURES_IMPLEMENTING',
            '已上会，继续跟踪': 'MEETING_CONTINUE_TRACKING',
            '未上会，确认中': 'NO_MEETING_CONFIRMING',
            '未上会，继续跟踪': 'NO_MEETING_CONTINUE_TRACKING',
            '未上会，无效抱怨': 'NO_MEETING_INVALID_COMPLAINT',
            '未上会，已定义措施': 'NO_MEETING_MEASURES_DEFINED',
            '未上会，措施实施': 'NO_MEETING_MEASURES_IMPLEMENTING',
        }
        
        # 如果是中文标签，转换为英文
        if status_value in chinese_to_english:
            status_value = chinese_to_english[status_value]
        
        # 尝试获取枚举值
        try:
            return IssueStatus(status_value)
        except ValueError:
            # 如果无法匹配，返回默认状态
            return IssueStatus.NO_MEETING_CONFIRMING
    
    @issue_status.setter
    def issue_status(self, value):
        """设置议题状态"""
        if isinstance(value, IssueStatus):
            self._issue_status = value.value
        else:
            self._issue_status = str(value)

    # 🔄 添加车辆状态的智能转换属性
    @property
    def vehicle_status(self):
        """智能转换车辆状态"""
        status_value = self._vehicle_status

        if not status_value:
            return None

        # 中文标签到英文枚举值的映射
        chinese_to_english = {
            '客户车': 'CUSTOMER_CAR',
            '售前车': 'PRE_SALE',
            '试驾车': 'TEST_DRIVE',
            '展车': 'DISPLAY',
            '商品车': 'COMMERCIAL',
        }

        # 如果是中文标签，转换为英文
        if status_value in chinese_to_english:
            status_value = chinese_to_english[status_value]

        # 尝试获取枚举值
        try:
            return VehicleStatus(status_value)
        except ValueError:
            # 如果无法匹配，尝试通过中文标签查找
            for enum_value in VehicleStatus:
                if enum_value.chinese_label == self._vehicle_status:
                    return enum_value
            # 如果还是无法匹配，返回None
            return None

    @vehicle_status.setter
    def vehicle_status(self, value):
        """设置车辆状态"""
        if isinstance(value, VehicleStatus):
            self._vehicle_status = value.value
        elif value is None:
            self._vehicle_status = None
        else:
            self._vehicle_status = str(value)

    # 🔄 添加抱怨类别的智能转换属性
    @property
    def complaint_category(self):
        """智能转换抱怨类别"""
        category_value = self._complaint_category

        if not category_value:
            return None

        # 如果是错误的车辆状态值（如'展车'），返回None
        vehicle_status_values = ['客户车', '售前车', '试驾车', '展车', '商品车', 'PRE_SALE', 'TEST_DRIVE', 'DISPLAY', 'COMMERCIAL']
        if category_value in vehicle_status_values:
            return None

        # 中文标签到英文枚举值的映射
        chinese_to_english = {
            '售后': 'AFTER_SALES',
            'PDI': 'PDI',
        }

        # 如果是中文标签，转换为英文
        if category_value in chinese_to_english:
            category_value = chinese_to_english[category_value]

        # 尝试获取枚举值
        try:
            return ComplaintCategory(category_value)
        except ValueError:
            # 如果无法匹配，返回None
            return None

    @complaint_category.setter
    def complaint_category(self, value):
        """设置抱怨类别"""
        if isinstance(value, ComplaintCategory):
            self._complaint_category = value.value
        elif value is None:
            self._complaint_category = None
        else:
            self._complaint_category = str(value)

    # 兼容性属性别名，确保模板和视图可以正确访问字段
    @property
    def created_date(self):
        """创建日期的别名，用于模板兼容性"""
        return self.created_at

    @property 
    def updated(self):
        """更新时间的别名，用于模板兼容性"""
        return self.updated_at

    @property
    def report_date(self):
        """报告日期，使用问题上报日期"""
        return self.problem_report_date or self.created_at
    
    # 关系字段
    comments = db.relationship('Comment', backref='issue', lazy='dynamic', 
                              cascade='all, delete-orphan')
    
    # 标签关系（多对多）
    tags_relationship = db.relationship('Tag', secondary=issue_tags, 
                                       backref=db.backref('issues', lazy='dynamic'),
                                       lazy='dynamic')
    
    # 索引定义
    __table_args__ = (
        Index('idx_issue_anlauf_number', 'anlauf_number'),
        Index('idx_issue_project_number', 'project_number'),
        Index('idx_issue_sequence', 'sequence_number'),
        Index('idx_issue_status', 'issue_status'),
        Index('idx_issue_times', 'created_at', 'updated_at', 'problem_report_date'),
        Index('idx_issue_vin', 'vin'),
        Index('idx_issue_has_updates', 'has_updates'),
        Index('idx_issue_complaint_category', 'complaint_category'),
        Index('idx_issue_vehicle_status', 'vehicle_status'),
        Index('idx_issue_severity', 'severity'),
        Index('idx_issue_handler', 'handler'),
        Index('idx_issue_reporter', 'reporter'),
        Index('idx_issue_project', 'project'),
        Index('idx_issue_key_field', 'key_field'),
        Index('idx_issue_vehicle_vin', 'vehicle_vin'),
        Index('idx_issue_ai_tag_status', 'ai_tag_status'),
    )
    
    def __init__(self, **kwargs):
        """初始化议题实例"""
        super(Issue, self).__init__(**kwargs)
        
        # 同步数据：如果设置了新字段，同步到兼容性字段
        if self.project_number and not self.project:
            self.project = self.project_number
        if self.anlauf_number and not self.key_field:
            self.key_field = self.anlauf_number
        if self.vin and not self.vehicle_vin:
            self.vehicle_vin = self.vin
    
    def set_updated_fields(self, updated_fields_list):
        """设置已更新的字段列表"""
        import json
        self.updated_fields = json.dumps(updated_fields_list, ensure_ascii=False)
        self.has_updates = len(updated_fields_list) > 0
        if self.has_updates:
            self.last_import_update_time = beijing_now()
    
    def get_updated_fields(self):
        """获取已更新的字段列表"""
        import json
        if self.updated_fields:
            try:
                return json.loads(self.updated_fields)
            except:
                return []
        return []
    
    def clear_updates(self):
        """清除更新标记"""
        self.updated_fields = None
        self.has_updates = False
        self.last_import_update_time = None
    
    def is_field_updated(self, field_name):
        """检查指定字段是否在最近的导入中被更新"""
        return field_name in self.get_updated_fields()
    
    def add_tag(self, tag):
        """添加标签"""
        if not self.has_tag(tag):
            self.tags_relationship.append(tag)
    
    def remove_tag(self, tag):
        """移除标签"""
        if self.has_tag(tag):
            self.tags_relationship.remove(tag)
    
    def has_tag(self, tag):
        """检查是否有指定标签"""
        return self.tags_relationship.filter_by(id=tag.id).count() > 0
    
    def get_tag_names(self):
        """获取所有标签名称"""
        return [tag.name for tag in self.tags_relationship.all()]
    
    def set_ai_tag_status(self, status, error_message=None):
        """设置AI标签状态"""
        self.ai_tag_status = status
        self.ai_tag_updated_at = beijing_now()
        if error_message:
            self.ai_tag_error_message = error_message
        elif status == 'completed':
            self.ai_tag_error_message = None
    
    def get_ai_tag_status_display(self):
        """获取AI标签状态显示文本"""
        status_map = {
            'none': '未处理',
            'processing': '更新中',
            'completed': '标签已生成',
            'error': '生成失败'
        }
        return status_map.get(self.ai_tag_status, '未知状态')
    
    def __repr__(self):
        return f'<Issue {self.anlauf_number or self.key_field}: {self.problem_description or "无描述"}>'
    
    def to_dict(self):
        """将议题对象转换为字典，用于JSON序列化"""
        return {
            'id': self.id,
            'project_number': self.project_number,
            'sequence_number': self.sequence_number,
            'anlauf_number': self.anlauf_number,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'problem_report_week': self.problem_report_week,
            'review_status': self.review_status,
            'engineer_first_review_time': self.engineer_first_review_time.isoformat() if self.engineer_first_review_time else None,
            'engineer_first_review_week': self.engineer_first_review_week,
            'vin': self.vin,
            'complaint_category': self.complaint_category.value if self.complaint_category else None,
            'kdnr': self.kdnr,
            'banr': self.banr,
            'breakdown': self.breakdown,
            'after_sales_fault_description': self.after_sales_fault_description,
            'mileage': float(self.mileage) if self.mileage else None,
            'zp8_delivery_date': self.zp8_delivery_date.isoformat() if self.zp8_delivery_date else None,
            'department': self.department,
            'engineer': self.engineer,
            'analysis_measures': self.analysis_measures,
            'warehouse_received_fault_parts': self.warehouse_received_fault_parts,
            'repair_station_info': self.repair_station_info,
            'problem_report_date': self.problem_report_date.isoformat() if self.problem_report_date else None,
            'vehicle_status': self.vehicle_status.value if self.vehicle_status else None,
            'is_rework_vehicle': self.is_rework_vehicle,
            'issue_status': self.issue_status.value if self.issue_status else None,
            'problem_description': self.problem_description,
            'meeting_topic_name': self.meeting_topic_name,
            'mark': self.mark,
            'fault_parts_status': self.fault_parts_status,
            'problem_type': self.problem_type,
            'problem_in_meeting': self.problem_in_meeting,
            'complaint_in_meeting': self.complaint_in_meeting,
            'severity': self.severity,
            'handler': self.handler,
            'reporter': self.reporter,
            'project': self.project,
            'key_field': self.key_field,
            'vehicle_vin': self.vehicle_vin,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'maintenance_status': self.maintenance_status.value if self.maintenance_status else None,
            'has_updates': self.has_updates,
            'last_import_update_time': self.last_import_update_time.isoformat() if self.last_import_update_time else None,
            'ai_tag_status': self.ai_tag_status,
            'ai_tag_updated_at': self.ai_tag_updated_at.isoformat() if self.ai_tag_updated_at else None,
            'ai_tag_error_message': self.ai_tag_error_message,
        }
    
    @staticmethod
    def from_dict(data):
        """从字典创建议题对象"""
        issue = Issue()
        for field, value in data.items():
            if hasattr(issue, field) and value is not None:
                setattr(issue, field, value)
        return issue
    
    def update_status(self, new_status, user=None):
        """更新议题状态"""
        old_status = self.issue_status
        self.issue_status = new_status
        self.updated_at = beijing_now()
        
        # 记录状态变更历史
        if user:
            comment_content = f"状态从 '{old_status.chinese_label}' 更改为 '{new_status.chinese_label}'"
            self.add_comment(comment_content, user, comment_type='system')
    
    def add_comment(self, content, user=None, comment_type='user'):
        """添加评论"""
        from .comment import Comment
        comment = Comment(
            content=content,
            author=user.username if user else '系统',
            author_role=user.role.value if user else '系统',
            issue_id=self.id,
            comment_type=comment_type,
            created_at=beijing_now()
        )
        db.session.add(comment)
        return comment
    
    def is_overdue(self):
        """检查是否超期"""
        # 这里可以根据业务逻辑定义超期条件
        return False
    
    def get_progress_percentage(self):
        """获取进度百分比"""
        status_progress_map = {
            IssueStatus.NO_MEETING_CONFIRMING: 10,
            IssueStatus.NO_MEETING_CONTINUE_TRACKING: 20,
            IssueStatus.NO_MEETING_INVALID_COMPLAINT: 100,
            IssueStatus.NO_MEETING_MEASURES_DEFINED: 40,
            IssueStatus.NO_MEETING_MEASURES_IMPLEMENTING: 60,
            IssueStatus.PRE_ANLAUF: 30,
            IssueStatus.MEETING_ANALYZING: 50,
            IssueStatus.MEETING_ANALYSIS_COMPLETE: 70,
            IssueStatus.MEETING_MEASURES_FORMULATING: 80,
            IssueStatus.MEETING_MEASURES_IMPLEMENTING: 90,
            IssueStatus.MEETING_CONTINUE_TRACKING: 95,
        }
        return status_progress_map.get(self.issue_status, 0)
    
    def update_maintenance_status(self, new_status, user=None):
        """更新维护状态"""
        old_status = self.maintenance_status
        self.maintenance_status = new_status
        self.updated_at = beijing_now()
        
        # 记录维护状态变更历史
        if user:
            comment_content = f"维护状态从 '{old_status.chinese_label}' 更改为 '{new_status.chinese_label}'"
            self.add_comment(comment_content, user, comment_type='maintenance')
    
    @classmethod
    def search(cls, query, filters=None):
        """搜索议题"""
        base_query = cls.query
        
        if query:
            search_pattern = f'%{query}%'
            base_query = base_query.filter(
                db.or_(
                    cls.anlauf_number.like(search_pattern),
                    cls.project_number.like(search_pattern),
                    cls.problem_description.like(search_pattern),
                    cls.engineer.like(search_pattern),
                    cls.kdnr.like(search_pattern),
                    cls.banr.like(search_pattern)
                )
            )
        
        if filters:
            for key, value in filters.items():
                if hasattr(cls, key) and value:
                    base_query = base_query.filter(getattr(cls, key) == value)
        
        return base_query
    
    def get_active_comments_count(self):
        """获取有效评论数量（排除已删除的评论）"""
        return self.comments.filter_by(is_deleted=False).count()
    
    def get_active_comments(self):
        """获取有效评论列表（排除已删除的评论）"""
        return self.comments.filter_by(is_deleted=False).order_by(Comment.created_at.asc()).all()

    def get_field_completion_stats(self):
        """获取字段填写完成度统计 - 基于四个卡片"""
        # 按卡片分组定义重要字段
        field_groups = {
            'basic': [
                'project_number', 'sequence_number', 'anlauf_number', 'created_at',
                'problem_report_week', 'problem_report_date', 'department', 'engineer'
            ],
            'vehicle': [
                'vin', 'vehicle_status', 'mileage', 'is_rework_vehicle',
                'kdnr', 'banr', 'breakdown', 'repair_station_info'
            ],
            'problem': [
                'complaint_category', 'issue_status', 'problem_description', 'after_sales_fault_description',
                'analysis_measures', 'problem_type', 'fault_parts_status', 'warehouse_received_fault_parts', 'mark'
            ],
            'process': [
                'review_status', 'engineer_first_review_time', 'engineer_first_review_week',
                'zp8_delivery_date', 'meeting_topic_name', 'problem_in_meeting', 'complaint_in_meeting'
            ]
        }
        
        # 计算各卡片的完成度
        card_stats = {}
        total_filled = 0
        total_fields = 0
        
        for card_name, fields in field_groups.items():
            filled_count = 0
            for field in fields:
                value = getattr(self, field, None)
                # 对布尔类型字段特殊处理
                if field in ['is_rework_vehicle', 'breakdown', 'warehouse_received_fault_parts', 'problem_in_meeting', 'complaint_in_meeting']:
                    if value is not None:
                        filled_count += 1
                else:
                    if value is not None and str(value).strip():
                        filled_count += 1
            
            card_completion = int((filled_count / len(fields)) * 100) if len(fields) > 0 else 0
            card_stats[card_name] = {
                'filled_count': filled_count,
                'total_count': len(fields),
                'completion_percentage': card_completion
            }
            
            total_filled += filled_count
            total_fields += len(fields)
        
        # 计算总体完成度
        overall_completion = int((total_filled / total_fields) * 100) if total_fields > 0 else 0
        
        return {
            'filled_count': total_filled,
            'total_count': total_fields,
            'completion_percentage': overall_completion,
            'card_stats': card_stats
        }

    def get_updated_fields_stats(self):
        """获取更新字段统计信息"""
        try:
            updated_fields = self.get_updated_fields()
        except Exception:
            updated_fields = []
        
        return {
            'updated_count': len(updated_fields),
            'updated_fields': updated_fields,
            'has_updates': len(updated_fields) > 0,
            'last_update_time': self.last_import_update_time
        }

    def get_days_since_report(self):
        """计算自上报以来的天数"""
        from datetime import datetime
        
        # 优先使用问题上报日期，其次使用创建日期
        report_date = self.problem_report_date or self.created_at
        if not report_date:
            return 0
        
        # 确保使用本地时间进行计算
        today = datetime.now().date()
        
        # 安全地提取日期部分
        if hasattr(report_date, 'date'):
            report_date = report_date.date()
        elif isinstance(report_date, str):
            try:
                report_date = datetime.strptime(report_date, '%Y-%m-%d').date()
            except ValueError:
                try:
                    report_date = datetime.strptime(report_date, '%Y-%m-%d %H:%M:%S').date()
                except ValueError:
                    return 0
        
        # 计算天数差
        days_diff = (today - report_date).days
        
        # 确保返回非负数，如果是负数说明数据有问题，返回0
        return max(0, days_diff)