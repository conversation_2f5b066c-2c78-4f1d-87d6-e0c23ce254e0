{% extends "base.html" %}

{% block title %}议题详情 - {{ app_name }}{% endblock %}

{% block extra_meta %}
<meta name="csrf-token" content="{{ csrf_token() }}">
{% endblock %}

{# 检查字段是否被更新的宏 #}
{% macro is_field_updated(field_name) %}
    {%- if issue.is_field_updated(field_name) -%}
        field-updated
    {%- endif -%}
{% endmacro %}

{# 检查字段标签是否需要更新标识的宏 #}
{% macro get_label_class(field_name) %}
    {%- if issue.is_field_updated(field_name) -%}
        field-updated-label
    {%- else -%}
        detail-label
    {%- endif -%}
{% endmacro %}

{# 获取更新标记的宏 #}
{% macro get_update_marker(field_name) %}
    {%- if issue.is_field_updated(field_name) -%}
        <i class="fas fa-circle update-marker" title="此字段在最近的导入中被更新"></i>
    {%- endif -%}
{% endmacro %}

{# 检查字段是否可编辑的宏 #}
{% macro is_field_editable(field_name) %}
    {%- if field_permissions is defined and field_permissions and field_permissions.get(field_name, False) -%}
        editable
    {%- else -%}
        readonly
    {%- endif -%}
{% endmacro %}

{# 获取字段编辑权限提示的宏 #}
{% macro get_edit_permission_title(field_name) %}
    {%- if field_permissions is defined and field_permissions and field_permissions.get(field_name, False) -%}
        双击编辑此字段
    {%- else -%}
        此字段为只读，您没有编辑权限
    {%- endif -%}
{% endmacro %}

{% block extra_css %}
<style>
/* 表单区域样式 */
.form-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    border-left: 4px solid #007bff;
}

/* 议题详情卡片样式 */
.detail-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

.detail-value .badge {
    font-size: 0.8rem;
}

.progress {
    background-color: #e9ecef;
    border-radius: 4px;
}

/* 圆形图标统计样式 */
.circular-stats-container {
    padding: 10px 0;
}

.circular-stat-item {
    margin-bottom: 15px;
}

.circular-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    position: relative;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.circular-icon:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.circular-content {
    text-align: center;
    color: white;
}

.circular-number {
    font-size: 24px;
    font-weight: bold;
    line-height: 1;
}

.circular-label {
    font-size: 12px;
    opacity: 0.9;
    margin-top: 2px;
}

.circular-title {
    font-size: 13px;
    color: #6c757d;
    font-weight: 500;
}

/* 上报天数圆形图标颜色 */
#days-since-report-circle {
    background: linear-gradient(135deg, #28a745, #20c997);
}

/* 根据天数设置不同颜色 */
.days-today {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
}

.days-recent {
    background: linear-gradient(135deg, #ffc107, #fd7e14) !important;
}

.days-old {
    background: linear-gradient(135deg, #6c757d, #495057) !important;
}

/* 字段更新圆形图标颜色 */
#updates-count-circle {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.updates-has-updates {
    background: linear-gradient(135deg, #dc3545, #c82333) !important;
}

.updates-no-updates {
    background: linear-gradient(135deg, #6c757d, #495057) !important;
}

/* 更新动画效果 */
.circular-icon.updated {
    animation: pulse-circular 1s ease-in-out;
}

@keyframes pulse-circular {
    0% { 
        transform: scale(1) translateY(0); 
    }
    50% { 
        transform: scale(1.1) translateY(-2px); 
    }
    100% { 
        transform: scale(1) translateY(0); 
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .circular-icon {
        width: 70px;
        height: 70px;
    }
    
    .circular-number {
        font-size: 20px;
    }
    
    .circular-label {
        font-size: 11px;
    }
    
    .circular-title {
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .circular-icon {
        width: 60px;
        height: 60px;
    }
    
    .circular-number {
        font-size: 18px;
    }
    
    .circular-label {
        font-size: 10px;
    }
    
    .circular-title {
        font-size: 11px;
    }
}

/* 自定义信息面板样式 */
.upload-area {
    position: relative;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    transition: all 0.3s ease;
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.upload-area:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.upload-area.dragover {
    border-color: #007bff;
    background-color: #e7f3ff;
}

.upload-area input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.upload-placeholder {
    pointer-events: none;
}

.color-picker-btn {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 2px solid #dee2e6;
    cursor: pointer;
    transition: all 0.2s ease;
}

.color-picker-btn:hover {
    transform: scale(1.1);
    border-color: #007bff;
}

.color-picker-btn.selected {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.attachments-list {
    max-height: 300px;
    overflow-y: auto;
}

.attachment-item {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
    background: #f8f9fa;
    transition: all 0.2s ease;
}

.attachment-item:hover {
    background: #e9ecef;
    border-color: #007bff;
}

.attachment-icon {
    font-size: 24px;
    margin-right: 12px;
}

.attachment-info {
    flex-grow: 1;
}

.attachment-name {
    font-weight: 600;
    margin-bottom: 4px;
}

.attachment-meta {
    font-size: 0.8rem;
    color: #6c757d;
}

/* 上传进度条样式 */
#upload-progress {
    margin-top: 10px;
}

#upload-progress .progress {
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
}

#upload-progress .progress-bar {
    background-color: #007bff;
    transition: width 0.3s ease;
}

.progress-bar-animated {
    background-image: linear-gradient(45deg, rgba(255,255,255,.15) 25%, transparent 25%, transparent 50%, rgba(255,255,255,.15) 50%, rgba(255,255,255,.15) 75%, transparent 75%, transparent);
    background-size: 1rem 1rem;
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    0% {
        background-position: 1rem 0;
    }
    100% {
        background-position: 0 0;
    }
}

.attachment-actions {
    display: flex;
    gap: 8px;
}

.attachment-preview {
    max-width: 100px;
    max-height: 100px;
    object-fit: cover;
    border-radius: 4px;
}

.tag-suggestion {
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.tag-suggestion:hover {
    background-color: #e9ecef;
}

/* 文件预览模态框样式 */
.file-preview-modal .modal-dialog {
    max-width: 90%;
    height: 90%;
}

.file-preview-modal .modal-content {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.file-preview-modal .modal-body {
    flex: 1;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.file-preview-content {
    max-width: 100%;
    max-height: 100%;
    overflow: auto;
}

.file-preview-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.file-preview-document {
    width: 100%;
    height: 100%;
    border: none;
}

.form-section h5 {
    color: #495057;
    margin-bottom: 15px;
    font-weight: 600;
}

.detail-field {
    margin-bottom: 15px;
    position: relative;
}

.detail-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.detail-value {
    color: #212529;
    padding: 12px 16px;
    background: #fff;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    min-height: 44px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    word-wrap: break-word;
    position: relative;
}

.detail-value:hover {
    border-color: #007bff;
    box-shadow: 0 0 0 0.15rem rgba(0, 123, 255, 0.15);
}

.detail-value.editing {
    padding: 0;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.detail-value.empty {
    color: #6c757d;
    font-style: italic;
}

.edit-input, .edit-textarea, .edit-select {
    width: 100%;
    border: none;
    outline: none;
    padding: 12px 16px;
    background: transparent;
    font-size: inherit;
    font-family: inherit;
    color: inherit;
    border-radius: 6px;
}

.edit-textarea {
    resize: vertical;
    min-height: 80px;
}

/* 议题状态徽章样式 - 统一高度 */
.status-badge-enhanced {
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    font-weight: 600;
    font-size: 0.8rem;
    letter-spacing: 0.3px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    overflow: hidden;
    text-transform: none;
    white-space: nowrap;
    display: inline-flex;
    align-items: center;
    gap: 0.4rem;
    vertical-align: middle;
    min-height: 2rem;          /* 统一最小高度 */
    height: 2rem;              /* 固定高度 */
    line-height: 1.2;          /* 统一行高 */
}

.status-badge-enhanced:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.18);
}

.status-badge-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
    transition: left 0.5s;
}

.status-badge-enhanced:hover::before {
    left: 100%;
}

/* 状态图标大小调整 */
.status-badge-enhanced i {
    font-size: 0.75rem;
}

/* 维护状态标签样式优化 - 与议题状态保持完全一致的高度 */
.badge {
    font-size: 0.8rem !important;
    padding: 0.4rem 0.8rem !important;
    border-radius: 15px !important;
    font-weight: 600 !important;
    vertical-align: middle;
    letter-spacing: 0.3px;
    min-height: 2rem !important;      /* 与议题状态统一最小高度 */
    height: 2rem !important;          /* 与议题状态统一固定高度 */
    line-height: 1.2 !important;      /* 与议题状态统一行高 */
    display: inline-flex !important;  /* 统一显示方式 */
    align-items: center !important;   /* 垂直居中对齐 */
    justify-content: center !important; /* 水平居中对齐 */
}

/* 维护状态的不同颜色样式 */
.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #000 !important;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

.badge.bg-success {
    background-color: #198754 !important;
    color: #fff !important;
    box-shadow: 0 2px 8px rgba(25, 135, 84, 0.3);
}

.badge.bg-secondary {
    background-color: #6c757d !important;
    color: #fff !important;
    box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
}

/* 状态容器布局优化 - 确保标签对齐 */
.card-body .row .col-md-3 {
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;           /* 垂直居中对齐整个容器 */
    min-height: 2.5rem;           /* 容器最小高度 */
}

.card-body .row .col-md-3 strong {
    display: inline-block;
    margin-right: 0.5rem;
    vertical-align: middle;
    color: #495057;
    font-size: 0.9rem;
    line-height: 2rem;            /* 与标签高度对齐 */
}

/* 状态标签悬停效果统一 */
.badge:hover, .status-badge-enhanced:hover {
    transform: translateY(-1px);
    transition: all 0.3s ease;
}

.badge.bg-warning:hover {
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4);
}

.badge.bg-success:hover {
    box-shadow: 0 4px 12px rgba(25, 135, 84, 0.4);
}

.badge.bg-secondary:hover {
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.4);
}

/* 特殊情况：未设置状态的样式也要统一高度 */
.badge.bg-secondary,
.status-badge-enhanced + .badge {
    min-height: 2rem !important;
    height: 2rem !important;
    line-height: 1.2 !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* 响应式调整 - 小屏幕上也保持统一高度 */
@media (max-width: 768px) {
    .status-badge-enhanced {
        min-height: 1.8rem;
        height: 1.8rem;
        padding: 0.3rem 0.6rem;
        font-size: 0.75rem;
        gap: 0.3rem;
    }
    
    .status-badge-enhanced i {
        font-size: 0.7rem;
    }
    
    .badge {
        font-size: 0.75rem !important;
        padding: 0.3rem 0.6rem !important;
        min-height: 1.8rem !important;      /* 小屏幕统一高度 */
        height: 1.8rem !important;
        line-height: 1.2 !important;
    }
    
    .card-body .row .col-md-3 {
        margin-bottom: 1rem;
        flex-direction: column;
        align-items: flex-start;
        min-height: auto;
    }
    
    .card-body .row .col-md-3 strong {
        display: block;
        margin-bottom: 0.3rem;
        margin-right: 0;
        line-height: normal;
    }
}

/* 更大屏幕上确保水平对齐 */
@media (min-width: 768px) {
    .card-body .row .col-md-3 {
        flex-direction: row;
        align-items: center;
    }
    
    .card-body .row .col-md-3 strong {
        line-height: 2rem;
    }
}

/* 确保所有状态相关元素的基线对齐 */
.card-body .row {
    align-items: center;
}

/* 议题状态和维护状态容器的统一样式 */
.card-body .row .col-md-3:nth-child(1),
.card-body .row .col-md-3:nth-child(2) {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* 更新字段标识样式 */
.field-updated {
    color: #dc3545 !important;
    font-weight: 600;
    position: relative;
    padding-left: 20px;
}

.field-updated::before {
    content: '●';
    color: #dc3545;
    font-size: 0.8rem;
    position: absolute;
    left: 0;
    top: 0;
    animation: pulse-red 2s infinite;
}

.field-updated-label {
    color: #dc3545 !important;
    font-weight: 600;
}

.field-updated-label::after {
    content: ' (已更新)';
    font-size: 0.7rem;
    color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
    padding: 2px 4px;
    border-radius: 3px;
    margin-left: 5px;
}

@keyframes pulse-red {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 更新字段的边框高亮 */
.detail-value.field-updated {
    border-color: #dc3545 !important;
    background-color: rgba(220, 53, 69, 0.05);
    box-shadow: 0 0 0 0.15rem rgba(220, 53, 69, 0.15);
}

/* 更新标记图标 */
.update-marker {
    color: #dc3545;
    font-size: 0.75rem;
    margin-left: 5px;
    animation: pulse-red 2s infinite;
}

/* 快速操作面板样式 */
.quick-actions-panel {
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.quick-actions-panel .card-header {
    background: #007bff !important;
    border: none;
    color: white;
    border-radius: 10px 10px 0 0;
}

.quick-actions-panel .btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    padding: 12px 20px;
}

.quick-actions-panel .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.quick-actions-panel .btn-warning {
    background-color: #ffc107;
    color: #000;
}

.quick-actions-panel .btn-success {
    background-color: #198754;
    color: #fff;
}

.quick-actions-panel .btn-secondary {
    background-color: #6c757d;
    color: #fff;
}

/* 选项卡样式 */
.nav-tabs .nav-link {
    color: #495057;
    border: 1px solid transparent;
    border-top-left-radius: 0.375rem;
    border-top-right-radius: 0.375rem;
    font-weight: 500;
}

.nav-tabs .nav-link.active {
    color: #007bff;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}

.tab-content {
    border: 1px solid #dee2e6;
    border-top: none;
    padding: 20px;
    background: #fff;
    border-radius: 0 0 0.375rem 0.375rem;
}

.edit-hint {
    position: absolute;
    top: -30px;
    left: 0;
    font-size: 11px;
    color: #6c757d;
    background: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    display: none;
    z-index: 10;
}

.detail-value:hover .edit-hint {
    display: block;
}

.form-help-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 5px;
}

.required-field {
    color: #dc3545;
}

/* 保存指示器 */
.save-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #28a745;
    color: white;
    padding: 10px 20px;
    border-radius: 6px;
    display: none;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* 布尔值字段样式 */
.boolean-value {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.boolean-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.boolean-true {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.boolean-false {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .form-section {
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .detail-value {
        padding: 10px 12px;
        min-height: 40px;
    }
}

/* 右侧栏固定样式 */
.sticky-sidebar {
    position: sticky;
    top: 20px;
    max-height: calc(100vh - 40px);
    overflow-y: auto;
}

/* 快速操作面板在右侧栏的样式优化 */
.quick-actions-panel {
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
}

.quick-actions-panel .card-header {
    border-radius: 10px 10px 0 0;
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

/* 右侧栏按钮样式优化 */
.quick-actions-panel .btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.quick-actions-panel .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* 响应式设计 */
@media (max-width: 991.98px) {
    .sticky-sidebar {
        position: relative;
        top: auto;
        max-height: none;
        margin-top: 20px;
    }
    
    .quick-actions-panel .d-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 10px;
    }
}

@media (max-width: 767.98px) {
    .quick-actions-panel .d-grid {
        grid-template-columns: 1fr;
        gap: 8px;
    }
}

/* 议题信息面板样式 */
.issue-info-panel {
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.issue-info-panel .card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    border-radius: 8px 8px 0 0;
}

.info-item {
    text-align: center;
}

.info-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 10px;
}

.info-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
}

.info-label {
    font-size: 0.8rem;
    color: #6c757d;
}

.field-completeness {
    margin-top: 20px;
}

.completeness-header {
    margin-bottom: 10px;
}

.completeness-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: #495057;
}

.completeness-value {
    font-size: 0.9rem;
    font-weight: 600;
    color: #007bff;
}

.progress {
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    border-radius: 4px;
    transition: width 0.6s ease-in-out;
}

/* 议题信息面板样式 */
.issue-info-panel {
    border: 1px solid #e9ecef;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.issue-info-panel .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    border-radius: 10px 10px 0 0;
}

/* 信息项样式 */
.info-item {
    text-align: center;
    padding: 10px 5px;
}

.info-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.info-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 4px;
    line-height: 1.2;
}

.info-label {
    font-size: 0.75rem;
    color: #6c757d;
    font-weight: 500;
}

/* 字段完整度样式 */
.field-completeness {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.completeness-header {
    margin-bottom: 8px;
}

.completeness-label {
    font-size: 0.8rem;
    color: #495057;
    font-weight: 600;
}

.completeness-value {
    font-size: 0.75rem;
    color: #007bff;
    font-weight: 600;
}

.progress {
    border-radius: 10px;
    background-color: #e9ecef;
    overflow: hidden;
}

.progress-bar {
    border-radius: 10px;
    transition: width 0.6s ease;
    background: linear-gradient(90deg, #007bff 0%, #0056b3 100%);
}

/* 响应式优化 */
@media (max-width: 991.98px) {
    .info-item {
        padding: 8px 3px;
    }
    
    .info-icon {
        width: 35px;
        height: 35px;
    }
    
    .info-value {
        font-size: 1.25rem;
    }
    
    .info-label {
        font-size: 0.7rem;
    }
}

/* 确保状态标签在一行内对齐 */
.card-body .row .col-md-3 {
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 2.5rem;
}

@media (min-width: 768px) {
    .card-body .row .col-md-3 {
        display: flex;
        flex-direction: row;
        align-items: center;
        min-height: auto;
    }
}

/* 最近更新字段的动画效果 */
.field-recently-updated {
    background-color: rgba(40, 167, 69, 0.1) !important;
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.15rem rgba(40, 167, 69, 0.25) !important;
    animation: updatePulse 3s ease-in-out;
}

@keyframes updatePulse {
    0% {
        background-color: rgba(40, 167, 69, 0.3);
        transform: scale(1.02);
    }
    50% {
        background-color: rgba(40, 167, 69, 0.2);
        transform: scale(1.01);
    }
    100% {
        background-color: rgba(40, 167, 69, 0.1);
        transform: scale(1);
    }
}

/* 可编辑元素悬停效果 */
.editable-hover {
    background-color: rgba(0, 123, 255, 0.1) !important;
    border-color: #007bff !important;
    cursor: pointer !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2) !important;
    transition: all 0.2s ease-in-out;
}

.editable {
    transition: all 0.2s ease-in-out;
    cursor: pointer;
}

/* 只读字段样式 */
.readonly {
    background-color: #f8f9fa !important;
    color: #6c757d !important;
    cursor: not-allowed !important;
    opacity: 0.8;
    border: 1px solid #e9ecef !important;
}

.readonly:hover {
    background-color: #f8f9fa !important;
    transform: none !important;
    box-shadow: none !important;
}

/* 权限提示样式 */
.permission-denied {
    position: relative;
}

.permission-denied::after {
    content: "🔒";
    position: absolute;
    top: 2px;
    right: 2px;
    font-size: 12px;
    opacity: 0.6;
}

/* 编辑状态指示 */
.editing {
    background-color: #fff3cd !important;
    border-color: #ffc107 !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25) !important;
}

/* 在 extra_css 块中添加评论样式 */

/* 评论区域样式 */
.comments-section {
    padding: 20px;
}

.comments-list {
    max-height: 600px;
    overflow-y: auto;
}

.comment-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    background: #fff;
    transition: all 0.2s ease;
    text-align: left !important;       /* 强制父容器左对齐 */
}

.comment-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-color: #007bff;
}

.comment-header {
    border-bottom: 1px solid #f8f9fa;
    padding-bottom: 8px;
    margin-bottom: 12px;
}

.comment-author {
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
}

.comment-time {
    margin-top: 4px;
    font-size: 0.85rem;
}

/* 评论内容样式 - 确保所有情况下都左对齐 */
.comment-content {
    color: #212529;
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
    padding: 0 !important;
    margin: 0 !important;
    text-align: left !important;
    text-indent: 0 !important;
    display: block !important;          /* 强制块级显示 */
    width: 100% !important;             /* 确保占满宽度 */
    box-sizing: border-box;             /* 边框盒模型 */
    position: relative !important;      /* 相对定位 */
    left: 0 !important;                 /* 强制左对齐 */
    transform: none !important;         /* 清除任何变换 */
}

/* 确保评论内容的父容器也是左对齐 */
.comment-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    background: #fff;
    transition: all 0.2s ease;
    text-align: left !important;       /* 强制父容器左对齐 */
}

/* 评论内容内的所有元素都左对齐 */
.comment-content * {
    text-align: left !important;
    margin-left: 0 !important;
    padding-left: 0 !important;
    text-indent: 0 !important;
    position: relative !important;
    left: 0 !important;
}

/* 特别处理评论内容的第一行，去除任何可能的缩进 */
.comment-content::first-line {
    text-indent: 0 !important;
    margin-left: 0 !important;
    padding-left: 0 !important;
}

/* 处理不同类型的文本内容 */
.comment-content p,
.comment-content div,
.comment-content span {
    text-align: left !important;
    margin: 0 !important;
    padding: 0 !important;
    display: block;
    width: 100%;
}

/* 确保换行后的文本也左对齐 */
.comment-content br + * {
    text-align: left !important;
    margin-left: 0 !important;
}

/* 修复单行文本的对齐问题 */
.comment-content:empty::before {
    content: "";
    display: block;
    width: 100%;
}

/* 新评论的特殊样式也要确保左对齐 */
.new-comment .comment-content {
    text-align: left !important;
    padding: 0 !important;
    margin: 0 !important;
}

.comment-actions .dropdown-toggle {
    border: none;
    background: transparent;
    color: #6c757d;
}

.comment-actions .dropdown-toggle:hover,
.comment-actions .dropdown-toggle:focus {
    color: #495057;
    background: #f8f9fa;
}

.comment-edit-form {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

/* 编辑表单的文本框也要左对齐 */
.comment-edit-form textarea {
    text-align: left;
    padding-left: 12px;           /* 保持合理的内边距 */
}

.no-comments {
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px dashed #dee2e6;
}

.no-comments i {
    color: #dee2e6;
}

.add-comment-form .card {
    border: 1px solid #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.add-comment-form .card-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-bottom: none;
}

.add-comment-form textarea {
    resize: vertical;
    min-height: 100px;
    text-align: left;             /* 确保文本框内容左对齐 */
    padding-left: 12px;           /* 保持合理的内边距 */
}

.add-comment-form textarea:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 评论数量徽章 */
#comments-count {
    font-size: 0.75rem;
    min-width: 20px;
    border-radius: 10px;
}

/* 新评论的动画效果 */
.new-comment {
    animation: newCommentSlide 0.5s ease-out;
    border-left: 4px solid #28a745;
    background: linear-gradient(90deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.05) 50%, #fff 100%);
}

@keyframes newCommentSlide {
    0% {
        opacity: 0;
        transform: translateY(-20px);
        background: rgba(40, 167, 69, 0.2);
    }
    50% {
        opacity: 0.8;
        transform: translateY(-5px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 提交按钮加载状态 */
#addCommentForm button[disabled] {
    opacity: 0.7;
    cursor: not-allowed;
}

#addCommentForm button[disabled]:hover {
    background-color: #007bff !important;
    border-color: #007bff !important;
}

/* 刚刚发布标签的动画 */
.badge.bg-success {
    animation: badgePulse 2s ease-in-out infinite;
}

@keyframes badgePulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}

/* 响应式调整 */
@media (max-width: 768px) {
    .comments-section {
        padding: 15px;
    }
    
    .comment-item {
        padding: 12px;
    }
    
    .comment-content {
        padding: 0 !important;      /* 在小屏幕上也确保无内边距 */
    }
    
    .comment-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .comment-actions {
        margin-top: 8px;
    }
}

/* 添加@功能样式 - 美化版本 */
.mention-container {
    position: relative;
}

.mention-dropdown {
    position: absolute;
    z-index: 1050;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e3f2fd;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08);
    max-height: 240px;
    overflow-y: auto;
    width: 320px;
    max-width: 90vw;
    top: 100%;
    left: 0;
    backdrop-filter: blur(10px);
    animation: mentionDropdownSlide 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    /* 隐藏滚动条但保留滚动功能 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE/Edge */
}

/* 隐藏WebKit浏览器的滚动条 */
.mention-dropdown::-webkit-scrollbar {
    display: none;
}

@keyframes mentionDropdownSlide {
    0% {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.mention-list {
    padding: 6px 0;
    margin: 0;
}

.mention-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    overflow: hidden;
}

.mention-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(25, 118, 210, 0.1), transparent);
    transition: left 0.5s ease;
}

.mention-item:hover::before {
    left: 100%;
}

.mention-item:hover,
.mention-item.active {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border-left: 4px solid #1976d2;
    padding-left: 12px;
    transform: translateX(4px);
    box-shadow: 0 4px 12px rgba(25, 118, 210, 0.15);
}

.mention-item:last-child {
    border-bottom: none;
}

.mention-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 10px;
    object-fit: cover;
}

.mention-user-info {
    flex: 1;
}

.mention-username {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

.mention-role {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 2px;
}

/* 美化部门信息样式 */
.mention-department {
    font-size: 0.7rem;
    color: #8892b0;
    font-weight: 500;
    padding: 1px 6px;
    background: rgba(139, 146, 176, 0.1);
    border-radius: 8px;
    display: inline-block;
    margin-top: 2px;
    transition: all 0.3s ease;
}

.mention-item:hover .mention-department,
.mention-item.active .mention-department {
    background: rgba(25, 118, 210, 0.2);
    color: #1976d2;
    transform: translateX(2px);
}

/* @提及标签样式 - 美化版本 */
.mention-tag {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    color: #1976d2;
    padding: 2px 4px;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    margin: 0 1px;
    border: 1px solid #bbdefb;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(25, 118, 210, 0.15);
}

.mention-tag::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.mention-tag:hover::before {
    left: 100%;
}

.mention-tag:hover {
    background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
    color: white;
    border-color: #1976d2;
    text-decoration: none;
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 6px 20px rgba(25, 118, 210, 0.3);
}

/* 评论中的@提及样式 - 美化版本 */
.comment-content .mention-tag {
    background: linear-gradient(135deg, #e8f4fd 0%, #f3e5f5 100%);
    color: #1565c0;
    border: 1px solid #90caf9;
    font-size: 0.85rem;
    padding: 1px 4px;
    margin: 0 1px;
    box-shadow: 0 1px 4px rgba(21, 101, 192, 0.2);
}

.comment-content .mention-tag:hover {
    background: linear-gradient(135deg, #1565c0 0%, #0d47a1 100%);
    color: white;
    border-color: #1565c0;
    transform: translateY(-1px) scale(1.03);
    box-shadow: 0 4px 12px rgba(21, 101, 192, 0.4);
}

/* 响应式调整 - 美化版本 */
@media (max-width: 768px) {
    .mention-dropdown {
        max-height: 180px;
        border-radius: 10px;
        box-shadow: 0 6px 24px rgba(0, 0, 0, 0.15);
    }
    
    .mention-avatar-container {
        width: 36px;
        height: 36px;
        margin-right: 12px;
    }
    
    .mention-avatar,
    .mention-avatar-fallback {
        border-width: 2px;
    }
    
    .mention-item {
        padding: 10px 14px;
    }
    
    .mention-username {
        font-size: 0.9rem;
    }
    
    .mention-role {
        font-size: 0.7rem;
        padding: 1px 6px;
    }
    
    .mention-department {
        font-size: 0.65rem;
        padding: 0px 5px;
    }
    
    .mention-tag {
        padding: 1px 3px;
        font-size: 0.8rem;
        margin: 0 1px;
    }
    
    .comment-content .mention-tag {
        font-size: 0.8rem;
        padding: 1px 3px;
    }
}

/* 修改@提及功能的头像样式 */

/* 美化头像样式 */
.mention-avatar-container {
    width: 40px;
    height: 40px;
    margin-right: 14px;
    position: relative;
    flex-shrink: 0;
}

.mention-avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #e3f2fd;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.mention-avatar-fallback {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1rem;
    border: 3px solid #e3f2fd;
    text-transform: uppercase;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.mention-avatar-fallback::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.5s ease;
}

/* 角色图标样式 */
.role-icon {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.role-icon i {
    font-size: 1.2rem !important;
    color: white !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    z-index: 1 !important;
    position: relative !important;
}

.mention-item:hover .mention-avatar,
.mention-item.active .mention-avatar {
    border-color: #1976d2;
    transform: scale(1.05);
    box-shadow: 0 4px 16px rgba(25, 118, 210, 0.3);
}

.mention-item:hover .mention-avatar-fallback,
.mention-item.active .mention-avatar-fallback {
    background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
    border-color: #1976d2;
    transform: scale(1.05);
    box-shadow: 0 4px 16px rgba(25, 118, 210, 0.3);
}

.mention-item:hover .mention-avatar-fallback::before,
.mention-item.active .mention-avatar-fallback::before {
    transform: translateX(100%);
}

/* 美化用户信息样式 */
.mention-user-info {
    flex: 1;
    min-width: 0;
    padding-left: 2px;
}

.mention-username {
    font-weight: 700;
    color: #2c3e50;
    font-size: 0.95rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 2px;
    transition: color 0.3s ease;
}

.mention-item:hover .mention-username,
.mention-item.active .mention-username {
    color: #1976d2;
}

.mention-role {
    font-size: 0.75rem;
    color: #7b809a;
    margin-top: 1px;
    margin-bottom: 1px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500;
    display: inline-block;
    background: linear-gradient(135deg, #e8f4fd 0%, #f3e5f5 100%);
    padding: 2px 8px;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.mention-item:hover .mention-role,
.mention-item.active .mention-role {
    background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
    color: white;
    transform: translateX(2px);
}

.mention-department {
    font-size: 0.75rem;
    color: #6c757d;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .mention-dropdown {
        max-height: 150px;
    }
    
    .mention-avatar-container {
        width: 28px;
        height: 28px;
        margin-right: 8px;
    }
    
    .mention-avatar-fallback {
        font-size: 0.8rem;
    }
    
    .mention-item {
        padding: 6px 10px;
    }
}

/* 头像加载动画 */
.mention-avatar-container {
    transition: all 0.2s ease;
}

.mention-item:hover .mention-avatar-container {
    transform: scale(1.05);
}

/* 确保头像容器始终保持圆形 */
.mention-avatar,
.mention-avatar-fallback {
    box-sizing: border-box;
}

/* 添加状态更新动画CSS */
.status-updated {
    animation: statusUpdate 2s ease-in-out;
    box-shadow: 0 0 15px rgba(0, 123, 255, 0.6) !important;
}

@keyframes statusUpdate {
    0% {
        transform: scale(1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
    }
    25% {
        transform: scale(1.05);
        box-shadow: 0 0 15px rgba(40, 167, 69, 0.6);
    }
    75% {
        transform: scale(1.02);
        box-shadow: 0 0 10px rgba(40, 167, 69, 0.4);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
    }
}

/* 议题状态进度条样式 */
.progress-container {
    width: 100%;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 10px 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    animation: fadeInUp 0.8s ease-out forwards;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.progress-container:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.progress-title {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    text-align: center;
}

.progress-wrapper {
    position: relative;
    width: 100%;
}

.progress-bar-container {
    position: relative;
    width: 100%;
    height: 40px;
}

.progress-bar {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 8px;
    background: linear-gradient(90deg, #e9ecef 0%, #f8f9fa 50%, #e9ecef 100%);
    border-radius: 4px;
    transform: translateY(-50%);
    overflow: hidden;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 4px,
        rgba(255, 255, 255, 0.1) 4px,
        rgba(255, 255, 255, 0.1) 8px
    );
    background-size: 16px 16px;
    animation: backgroundStripes 2s linear infinite;
}

@keyframes backgroundStripes {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 16px 16px;
    }
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
    border-radius: 4px;
    transition: width 1.2s cubic-bezier(0.4, 0, 0.2, 1);
    width: 0%;
    position: relative;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2), 0 0 8px rgba(40, 167, 69, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.6) 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);
    border-radius: 4px 4px 0 0;
    z-index: 2;
}

.progress-fill::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 3px,
        rgba(255, 255, 255, 0.3) 3px,
        rgba(255, 255, 255, 0.3) 6px
    );
    background-size: 12px 12px;
    animation: progressStripes 1.5s linear infinite;
    z-index: 3;
    border-radius: 4px;
}

@keyframes progressStripes {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 12px 12px;
    }
}

.progress-steps {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    transform: translateY(-50%);
    z-index: 3;
}

.step {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 2;
    animation: stepFadeIn 0.6s ease-out forwards;
    opacity: 0;
    transform: translateY(10px);
}

.step:nth-child(1) { animation-delay: 0.1s; }
.step:nth-child(2) { animation-delay: 0.2s; }
.step:nth-child(3) { animation-delay: 0.3s; }
.step:nth-child(4) { animation-delay: 0.4s; }
.step:nth-child(5) { animation-delay: 0.5s; }
.step:nth-child(6) { animation-delay: 0.6s; }

@keyframes stepFadeIn {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.step:first-child {
    margin-left: 0;
}

.step:last-child {
    margin-right: 0;
}

.step-circle {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: linear-gradient(145deg, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%);
    border: 2px solid #e9ecef;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    margin-bottom: 8px;
    position: relative;
    z-index: 10;
    cursor: pointer;
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.12),
        0 2px 4px rgba(0, 0, 0, 0.08),
        inset 0 1px 2px rgba(255, 255, 255, 0.8),
        inset 0 -1px 2px rgba(0, 0, 0, 0.1);
}

.step-circle:hover {
    transform: scale(1.2) translateZ(0);
    box-shadow: 
        0 8px 16px rgba(0, 0, 0, 0.18),
        0 4px 8px rgba(0, 0, 0, 0.12),
        inset 0 1px 3px rgba(255, 255, 255, 0.9),
        inset 0 -1px 3px rgba(0, 0, 0, 0.15);
}

/* 为step-circle添加立体光晕效果 */
.step-circle::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(233, 236, 239, 0.4) 0%, transparent 70%);
    transform: translate(-50%, -50%);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: -1;
    opacity: 0;
}

.step-circle:hover::before {
    width: 24px;
    height: 24px;
    opacity: 1;
}



.step-label {
    font-size: 0.7rem;
    color: #6c757d;
    text-align: center;
    line-height: 1.2;
    max-width: 60px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    white-space: nowrap;
    opacity: 0.8;
}

.step:hover .step-label {
    transform: translateX(-50%) translateY(-2px);
    opacity: 1;
    font-weight: 600;
}

.step.completed .step-label {
    animation: labelFadeIn 0.6s ease-out forwards;
}

.step.current .step-label {
    animation: labelBounce 2s infinite;
}

@keyframes labelFadeIn {
    0% {
        opacity: 0;
        transform: translateX(-50%) translateY(5px);
    }
    100% {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

@keyframes labelBounce {
    0%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    50% {
        transform: translateX(-50%) translateY(-2px);
    }
}

/* 已完成的步骤样式 */
.step.completed .step-circle {
    transform: scale(1.3) translateZ(0);
    box-shadow: 
        0 6px 16px rgba(0, 0, 0, 0.25),
        0 3px 8px rgba(0, 0, 0, 0.15),
        inset 0 2px 4px rgba(255, 255, 255, 0.9),
        inset 0 -2px 4px rgba(0, 0, 0, 0.2);
    animation: completedGlow 3s ease-in-out infinite;
}

/* 已完成步骤的立体光晕效果 */
.step.completed .step-circle::before {
    width: 28px;
    height: 28px;
    opacity: 0.8;
    background: radial-gradient(circle, currentColor 0%, transparent 70%);
    animation: completedHalo 3s ease-in-out infinite;
}



/* 当前步骤样式 */
.step.current .step-circle {
    transform: scale(1.5) translateZ(0);
    box-shadow: 
        0 8px 20px rgba(0, 0, 0, 0.35),
        0 4px 12px rgba(0, 0, 0, 0.25),
        inset 0 3px 6px rgba(255, 255, 255, 1),
        inset 0 -3px 6px rgba(0, 0, 0, 0.25);
    animation: currentStepPulse 2s infinite;
}

/* 当前步骤的立体光晕效果 */
.step.current .step-circle::before {
    width: 32px;
    height: 32px;
    opacity: 1;
    background: radial-gradient(circle, currentColor 0%, transparent 70%);
    animation: currentHalo 2s ease-in-out infinite;
}

.step.current .step-circle::after {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border-radius: 50%;
    background: transparent;
    border: 1px solid currentColor;
    animation: currentRipple 2s infinite;
    z-index: -2;
    opacity: 0.3;
}

@keyframes currentStepPulse {
    0%, 100% {
        box-shadow: 
            0 8px 20px rgba(0, 0, 0, 0.35),
            0 4px 12px rgba(0, 0, 0, 0.25),
            inset 0 3px 6px rgba(255, 255, 255, 1),
            inset 0 -3px 6px rgba(0, 0, 0, 0.25);
        transform: scale(1.5) translateZ(0);
    }
    50% {
        box-shadow: 
            0 12px 28px rgba(0, 0, 0, 0.45),
            0 6px 16px rgba(0, 0, 0, 0.35),
            inset 0 4px 8px rgba(255, 255, 255, 1),
            inset 0 -4px 8px rgba(0, 0, 0, 0.35);
        transform: scale(1.6) translateZ(0);
    }
}

@keyframes completedGlow {
    0%, 100% {
        box-shadow: 
            0 6px 16px rgba(0, 0, 0, 0.25),
            0 3px 8px rgba(0, 0, 0, 0.15),
            inset 0 2px 4px rgba(255, 255, 255, 0.9),
            inset 0 -2px 4px rgba(0, 0, 0, 0.2);
    }
    50% {
        box-shadow: 
            0 8px 20px rgba(0, 0, 0, 0.35),
            0 4px 12px rgba(0, 0, 0, 0.25),
            inset 0 3px 6px rgba(255, 255, 255, 1),
            inset 0 -3px 6px rgba(0, 0, 0, 0.3);
    }
}

@keyframes currentRipple {
    0% {
        transform: scale(1);
        opacity: 0.3;
    }
    100% {
        transform: scale(2);
        opacity: 0;
    }
}

@keyframes completedHalo {
    0%, 100% {
        opacity: 0.6;
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        opacity: 0.9;
        transform: translate(-50%, -50%) scale(1.1);
    }
}

@keyframes currentHalo {
    0%, 100% {
        opacity: 0.8;
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.2);
    }
}



/* 未完成的步骤样式 */
.step.pending .step-circle {
    background: #f8f9fa;
}

/* 状态更新动画效果 */
.status-updated {
    animation: statusUpdatePulse 2s ease-in-out;
}

@keyframes statusUpdatePulse {
    0% {
        transform: scale(1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 4px 16px rgba(0, 123, 255, 0.3);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .progress-container {
        padding: 8px 15px;
    }
    
    .step-circle {
        width: 14px;
        height: 14px;
        border-width: 2px;
    }
    
    .step-circle::before {
        max-width: 20px;
        max-height: 20px;
    }
    
    .step.completed .step-circle::before {
        width: 22px;
        height: 22px;
    }
    
    .step.current .step-circle::before {
        width: 24px;
        height: 24px;
    }
    
    .step-label {
        font-size: 0.6rem;
        max-width: 60px;
    }
    
    .progress-bar-container {
        height: 35px;
    }
    
    .progress-bar {
        height: 6px;
    }
}

@media (max-width: 576px) {
    .progress-container {
        padding: 6px 10px;
    }
    
    .step-circle {
        width: 12px;
        height: 12px;
        border-width: 2px;
    }
    
    .step-circle::before {
        max-width: 18px;
        max-height: 18px;
    }
    
    .step.completed .step-circle::before {
        width: 20px;
        height: 20px;
    }
    
    .step.current .step-circle::before {
        width: 22px;
        height: 22px;
    }
    
    .step-label {
        font-size: 0.55rem;
        max-width: 50px;
    }
    
    .progress-bar-container {
        height: 30px;
    }
    
    .progress-bar {
        height: 5px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
            <div class="row">
        <!-- 主要内容区域 - 左侧 -->
        <div class="col-lg-9 col-xl-10">
    <!-- 顶部标题和基本信息 -->
    <div class="row mb-1">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-clipboard-list me-2"></i>
                                    {{ issue.project_number or '无项目编号' }} - {{ (issue.problem_description[:50] + '...' if issue.problem_description and issue.problem_description|length > 50 else issue.problem_description) or '无问题描述' }}
                        </h4>
                        <div>
                                    <a href="{{ url_for('issues.list_issues') }}" class="btn btn-light btn-sm">
                                <i class="fas fa-arrow-left me-2"></i>返回列表
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <strong>议题状态：</strong>
                            {% if issue.issue_status %}
                                <span class="status-badge-enhanced" 
                                      style="background-color: {{ issue.issue_status.background_color }}; 
                                             color: {{ issue.issue_status.text_color }};">
                                    <i class="fas fa-flag"></i>
                                    {{ issue.issue_status.chinese_label }}
                                </span>
                            {% else %}
                                <span class="badge bg-secondary">未设置</span>
                            {% endif %}
                        </div>
                        <div class="col-md-3">
                            <strong>维护状态：</strong>
                            {% if issue.maintenance_status %}
                                {% if issue.maintenance_status.value == '待维护' %}
                                    <span class="badge bg-warning text-dark">{{ issue.maintenance_status.value }}</span>
                                {% elif issue.maintenance_status.value == '已维护' %}
                                    <span class="badge bg-success">{{ issue.maintenance_status.value }}</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ issue.maintenance_status.value }}</span>
                                {% endif %}
                            {% else %}
                                <span class="badge bg-secondary">未设置</span>
                            {% endif %}
                        </div>
                        <div class="col-md-3">
                            <strong>创建时间：</strong>
                            {{ issue.created_at.strftime('%Y-%m-%d %H:%M') if issue.created_at else '未设置' }}
                        </div>
                        <div class="col-md-3">
                            <strong>最后更新：</strong>
                            {{ issue.updated_at.strftime('%Y-%m-%d %H:%M') if issue.updated_at else '未设置' }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 议题状态进度条 -->
    <div class="row mb-4" style="margin-top: -20px;">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <!-- 已上线完成度 -->
                        <div class="col-md-6 mb-3">
                            <div class="progress-container">
                                <h6 class="progress-title">已上线完成度</h6>
                                <div class="progress-wrapper">
                                    <div class="progress-bar-container">
                                        <div class="progress-bar" id="online-progress-bar">
                                            <div class="progress-fill" data-progress="0"></div>
                                        </div>
                                        <div class="progress-steps" id="online-steps">
                                            <div class="step" data-step="1">
                                                <div class="step-circle"></div>
                                                <div class="step-label">已上Pre-Anlauf</div>
                                            </div>
                                            <div class="step" data-step="2">
                                                <div class="step-circle"></div>
                                                <div class="step-label">分析中</div>
                                            </div>
                                            <div class="step" data-step="3">
                                                <div class="step-circle"></div>
                                                <div class="step-label">分析完成</div>
                                            </div>
                                            <div class="step" data-step="4">
                                                <div class="step-circle"></div>
                                                <div class="step-label">措施制定</div>
                                            </div>
                                            <div class="step" data-step="5">
                                                <div class="step-circle"></div>
                                                <div class="step-label">措施落实</div>
                                            </div>
                                            <div class="step" data-step="6">
                                                <div class="step-circle"></div>
                                                <div class="step-label">继续跟踪</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 未上线完成度 -->
                        <div class="col-md-6 mb-3">
                            <div class="progress-container">
                                <h6 class="progress-title">未上线完成度</h6>
                                <div class="progress-wrapper">
                                    <div class="progress-bar-container">
                                        <div class="progress-bar" id="offline-progress-bar">
                                            <div class="progress-fill" data-progress="0"></div>
                                        </div>
                                        <div class="progress-steps" id="offline-steps">
                                            <div class="step" data-step="1">
                                                <div class="step-circle"></div>
                                                <div class="step-label">确认中</div>
                                            </div>
                                            <div class="step" data-step="2">
                                                <div class="step-circle"></div>
                                                <div class="step-label">继续跟踪</div>
                                            </div>
                                            <div class="step" data-step="3">
                                                <div class="step-circle"></div>
                                                <div class="step-label">无效抱怨</div>
                                            </div>
                                            <div class="step" data-step="4">
                                                <div class="step-circle"></div>
                                                <div class="step-label">已定义措施</div>
                                            </div>
                                            <div class="step" data-step="5">
                                                <div class="step-circle"></div>
                                                <div class="step-label">措施实施</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 议题信息选项卡 -->
    <div class="card">
        <ul class="nav nav-tabs" id="issueDetailTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" 
                        type="button" role="tab" aria-controls="basic" aria-selected="true">
                    <i class="fas fa-info-circle me-2"></i>基本信息
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="vehicle-tab" data-bs-toggle="tab" data-bs-target="#vehicle" 
                        type="button" role="tab" aria-controls="vehicle" aria-selected="false">
                    <i class="fas fa-car me-2"></i>车辆信息
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="problem-tab" data-bs-toggle="tab" data-bs-target="#problem" 
                        type="button" role="tab" aria-controls="problem" aria-selected="false">
                    <i class="fas fa-exclamation-triangle me-2"></i>问题信息
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="process-tab" data-bs-toggle="tab" data-bs-target="#process" 
                        type="button" role="tab" aria-controls="process" aria-selected="false">
                    <i class="fas fa-cogs me-2"></i>流程信息
                </button>
            </li>
            <!-- 新增自定义信息选项卡 -->
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="custom-tab" data-bs-toggle="tab" data-bs-target="#custom" 
                        type="button" role="tab" aria-controls="custom" aria-selected="false">
                    <i class="fas fa-tags me-2"></i>自定义信息
                </button>
            </li>
                    <!-- 新增评论选项卡 -->
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="comments-tab" data-bs-toggle="tab" data-bs-target="#comments" 
                                type="button" role="tab" aria-controls="comments" aria-selected="false">
                            <i class="fas fa-comments me-2"></i>评论
                            <!-- 使用过滤后的评论数 -->
                            <span class="badge bg-primary ms-1" id="comments-count">{{ issue.get_active_comments_count() }}</span>
                        </button>
                    </li>
        </ul>
        <div class="tab-content" id="issueDetailTabContent">
            <!-- 基本信息标签页 -->
            <div class="tab-pane fade show active" id="basic" role="tabpanel" aria-labelledby="basic-tab">
                <div class="form-section">
                    <h5><i class="fas fa-info-circle me-2"></i>基本信息</h5>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="detail-field">
                                <label class="{{ get_label_class('project_number') }}">项目编号 {{ get_update_marker('project_number') }}</label>
                                <div class="detail-value {{ is_field_editable('project_number') }}{{ ' empty' if not issue.project_number }} {{ is_field_updated('project_number') }}"
                                     data-field="project_number" data-type="text" title="{{ get_edit_permission_title('project_number') }}">
                                    {{ issue.project_number or '未设置' }}</div>
                                <div class="form-help-text">议题所属项目的编号</div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="detail-field">
                                <label class="{{ get_label_class('sequence_number') }}">序号 {{ get_update_marker('sequence_number') }}</label>
                                <div class="detail-value {{ is_field_editable('sequence_number') }}{{ ' empty' if not issue.sequence_number }} {{ is_field_updated('sequence_number') }}"
                                     data-field="sequence_number" data-type="number" title="{{ get_edit_permission_title('sequence_number') }}">
                                    {{ issue.sequence_number or '未设置' }}</div>
                                <div class="form-help-text">议题的序号</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="detail-field">
                                <label class="{{ get_label_class('anlauf_number') }}">Anlauf编号 {{ get_update_marker('anlauf_number') }}</label>
                                <div class="detail-value {{ is_field_editable('anlauf_number') }}{{ ' empty' if not issue.anlauf_number }} {{ is_field_updated('anlauf_number') }}" 
                                     data-field="anlauf_number" data-type="text" data-maxlength="8" title="{{ get_edit_permission_title('anlauf_number') }}">
                                    {{ issue.anlauf_number or '未设置' }}</div>
                                <div class="form-help-text">8位数字的Anlauf编号</div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="detail-field">
                                <label class="{{ get_label_class('created_at') }}">创建日期 {{ get_update_marker('created_at') }}</label>
                                <div class="detail-value {{ is_field_editable('created_at') }}{{ ' empty' if not issue.created_at }} {{ is_field_updated('created_at') }}" 
                                     data-field="created_at" data-type="datetime" title="{{ get_edit_permission_title('created_at') }}">
                                    {{ issue.created_at.strftime('%Y-%m-%d %H:%M:%S') if issue.created_at else '未设置' }}</div>
                                <div class="form-help-text">议题创建的日期和时间</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="detail-field">
                                <label class="{{ get_label_class('problem_report_week') }}">问题上报周次 {{ get_update_marker('problem_report_week') }}</label>
                                <div class="detail-value {{ is_field_editable('problem_report_week') }}{{ ' empty' if not issue.problem_report_week }} {{ is_field_updated('problem_report_week') }}" 
                                     data-field="problem_report_week" data-type="number" title="{{ get_edit_permission_title('problem_report_week') }}">
                                    {{ issue.problem_report_week or '未设置' }}</div>
                                <div class="form-help-text">问题上报的周次 {% if field_permissions is defined and field_permissions and not field_permissions.get('problem_report_week', False) %}<small class="text-muted">(仅项目负责人可编辑)</small>{% endif %}</div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="detail-field">
                                <label class="{{ get_label_class('problem_report_date') }}">问题上报日期 {{ get_update_marker('problem_report_date') }}</label>
                                <div class="detail-value {{ is_field_editable('problem_report_date') }}{{ ' empty' if not issue.problem_report_date }} {{ is_field_updated('problem_report_date') }}" 
                                     data-field="problem_report_date" data-type="datetime" title="{{ get_edit_permission_title('problem_report_date') }}">
                                    {{ issue.problem_report_date.strftime('%Y-%m-%d %H:%M:%S') if issue.problem_report_date else '未设置' }}</div>
                                <div class="form-help-text">问题上报的日期和时间 {% if field_permissions is defined and field_permissions and not field_permissions.get('problem_report_date', False) %}<small class="text-muted">(仅项目负责人可编辑)</small>{% endif %}</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="detail-field">
                                <label class="{{ get_label_class('department') }}">部门 {{ get_update_marker('department') }}</label>
                                <div class="detail-value {{ is_field_editable('department') }}{{ ' empty' if not issue.department }} {{ is_field_updated('department') }}" 
                                     data-field="department" data-type="text" title="{{ get_edit_permission_title('department') }}">
                                    {{ issue.department or '未设置' }}</div>
                                <div class="form-help-text">负责部门 {% if field_permissions is defined and field_permissions and not field_permissions.get('department', False) and current_user.role.value == 'PROJECT_MANAGER' %}<small class="text-muted">(项目负责人受限字段)</small>{% endif %}</div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="detail-field">
                                <label class="{{ get_label_class('engineer') }}">工程师 {{ get_update_marker('engineer') }}</label>
                                <div class="detail-value {{ is_field_editable('engineer') }}{{ ' empty' if not issue.engineer }} {{ is_field_updated('engineer') }}" 
                                     data-field="engineer" data-type="text" title="{{ get_edit_permission_title('engineer') }}">
                                    {{ issue.engineer or '未设置' }}</div>
                                <div class="form-help-text">负责工程师 {% if field_permissions is defined and field_permissions and not field_permissions.get('engineer', False) and current_user.role.value == 'PROJECT_MANAGER' %}<small class="text-muted">(项目负责人受限字段)</small>{% endif %}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 车辆信息标签页 -->
            <div class="tab-pane fade" id="vehicle" role="tabpanel" aria-labelledby="vehicle-tab">
                <div class="form-section">
                    <h5><i class="fas fa-car me-2"></i>车辆信息</h5>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="detail-field">
                                <label class="{{ get_label_class('vin') }}">VIN {{ get_update_marker('vin') }}</label>
                                <div class="detail-value {{ is_field_editable('vin') }}{{ ' empty' if not issue.vin }} {{ is_field_updated('vin') }}" 
                                     data-field="vin" data-type="text" data-maxlength="17" title="{{ get_edit_permission_title('vin') }}">
                                    {{ issue.vin or '未设置' }}</div>
                                <div class="form-help-text">车辆识别代码，17位字符</div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="detail-field">
                                <label class="{{ get_label_class('vehicle_status') }}">车辆状态 {{ get_update_marker('vehicle_status') }}</label>
                                <div class="detail-value {{ is_field_editable('vehicle_status') }}{{ ' empty' if not issue.vehicle_status }} {{ is_field_updated('vehicle_status') }}"
                                     data-field="vehicle_status" data-type="select"
                                     data-options='{"PRE_SALE_PDI":"售前PDI","TEST_DRIVE":"试驾车","DISPLAY":"展车","CUSTOMER_CAR":"客户车"}' title="{{ get_edit_permission_title('vehicle_status') }}">
                                    {% if issue.vehicle_status %}
                                        <span class="badge bg-info">{{ issue.vehicle_status.chinese_label }}</span>
                                    {% else %}
                                        未设置
                                    {% endif %}</div>
                                <div class="form-help-text">车辆当前状态 {% if field_permissions is defined and field_permissions and not field_permissions.get('vehicle_status', False) and current_user.role.value == 'PROJECT_MANAGER' %}<small class="text-muted">(项目负责人受限字段)</small>{% endif %} {% if field_permissions is defined and field_permissions and not field_permissions.get('vehicle_status', False) and current_user.role.value != "ADMIN" %}<small class="text-muted">(仅管理员可编辑)</small>{% endif %}</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="detail-field">
                                <label class="{{ get_label_class('mileage') }}">里程 {{ get_update_marker('mileage') }}</label>
                                <div class="detail-value {{ is_field_editable('mileage') }}{{ ' empty' if not issue.mileage }} {{ is_field_updated('mileage') }}" 
                                     data-field="mileage" data-type="number" data-step="0.01" title="{{ get_edit_permission_title('mileage') }}">
                                    {% if issue.mileage %}
                                        {{ "%.2f" | format(issue.mileage) }} km
                                    {% else %}
                                        未设置
                                    {% endif %}</div>
                                <div class="form-help-text">车辆行驶里程（公里）</div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="detail-field">
                                <label class="{{ get_label_class('is_rework_vehicle') }}">是否返工车辆 {{ get_update_marker('is_rework_vehicle') }}</label>
                                <div class="detail-value {{ is_field_editable('is_rework_vehicle') }}{{ ' empty' if issue.is_rework_vehicle is none }} {{ is_field_updated('is_rework_vehicle') }}" 
                                     data-field="is_rework_vehicle" data-type="boolean" title="{{ get_edit_permission_title('is_rework_vehicle') }}">
                                    <div class="boolean-value">
                                        {% if issue.is_rework_vehicle is not none %}
                                            {% if issue.is_rework_vehicle %}
                                                <span class="boolean-badge boolean-true">
                                                    <i class="fas fa-check me-1"></i>是
                                                </span>
                                            {% else %}
                                                <span class="boolean-badge boolean-false">
                                                    <i class="fas fa-times me-1"></i>否
                                                </span>
                                            {% endif %}
                                        {% else %}
                                            未设置
                                        {% endif %}
                                    </div></div>
                                <div class="form-help-text">标识是否为返工车辆 {% if field_permissions is defined and field_permissions and not field_permissions.get('is_rework_vehicle', False) and current_user.role.value == 'PROJECT_MANAGER' %}<small class="text-muted">(项目负责人受限字段)</small>{% endif %} {% if field_permissions is defined and field_permissions and not field_permissions.get('is_rework_vehicle', False) and current_user.role.value != "ADMIN" %}<small class="text-muted">(仅管理员可编辑)</small>{% endif %}</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="detail-field">
                                <label class="{{ get_label_class('kdnr') }}">KDNR {{ get_update_marker('kdnr') }}</label>
                                <div class="detail-value {{ is_field_editable('kdnr') }}{{ ' empty' if not issue.kdnr }} {{ is_field_updated('kdnr') }}" 
                                     data-field="kdnr" data-type="text" data-maxlength="4" title="{{ get_edit_permission_title('kdnr') }}">
                                    {{ issue.kdnr or '未设置' }}</div>
                                <div class="form-help-text">4位字符串的KDNR代码 {% if field_permissions is defined and field_permissions and not field_permissions.get('kdnr', False) and current_user.role.value == 'PROJECT_MANAGER' %}<small class="text-muted">(项目负责人受限字段)</small>{% endif %} {% if field_permissions is defined and field_permissions and not field_permissions.get('kdnr', False) and current_user.role.value != "ADMIN" %}<small class="text-muted">(仅管理员可编辑)</small>{% endif %}</div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="detail-field">
                                <label class="{{ get_label_class('banr') }}">BANR {{ get_update_marker('banr') }}</label>
                                <div class="detail-value {{ is_field_editable('banr') }}{{ ' empty' if not issue.banr }} {{ is_field_updated('banr') }}" 
                                     data-field="banr" data-type="text" title="{{ get_edit_permission_title('banr') }}">
                                    {{ issue.banr or '未设置' }}</div>
                                <div class="form-help-text">BANR代码 {% if field_permissions is defined and field_permissions and not field_permissions.get('banr', False) and current_user.role.value == 'PROJECT_MANAGER' %}<small class="text-muted">(项目负责人受限字段)</small>{% endif %} {% if field_permissions is defined and field_permissions and not field_permissions.get('banr', False) and current_user.role.value != "ADMIN" %}<small class="text-muted">(仅管理员可编辑)</small>{% endif %}</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="detail-field">
                                <label class="{{ get_label_class('breakdown') }}">抛锚 {{ get_update_marker('breakdown') }}</label>
                                <div class="detail-value {{ is_field_editable('breakdown') }}{{ ' empty' if issue.breakdown is none }} {{ is_field_updated('breakdown') }}" 
                                     data-field="breakdown" data-type="boolean" title="{{ get_edit_permission_title('breakdown') }}">
                                    <div class="boolean-value">
                                        {% if issue.breakdown is not none %}
                                            {% if issue.breakdown %}
                                                <span class="boolean-badge boolean-true">
                                                    <i class="fas fa-check me-1"></i>是
                                                </span>
                                            {% else %}
                                                <span class="boolean-badge boolean-false">
                                                    <i class="fas fa-times me-1"></i>否
                                                </span>
                                            {% endif %}
                                        {% else %}
                                            未设置
                                        {% endif %}
                                    </div></div>
                                <div class="form-help-text">是否发生抛锚 {% if field_permissions is defined and field_permissions and not field_permissions.get('breakdown', False) and current_user.role.value == 'PROJECT_MANAGER' %}<small class="text-muted">(项目负责人受限字段)</small>{% endif %} {% if field_permissions is defined and field_permissions and not field_permissions.get('breakdown', False) and current_user.role.value != "ADMIN" %}<small class="text-muted">(仅管理员可编辑)</small>{% endif %}</div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="detail-field">
                                <label class="{{ get_label_class('repair_station_info') }}">维修站号+中文名 {{ get_update_marker('repair_station_info') }}</label>
                                <div class="detail-value {{ is_field_editable('repair_station_info') }}{{ ' empty' if not issue.repair_station_info }} {{ is_field_updated('repair_station_info') }}" 
                                     data-field="repair_station_info" data-type="text" title="{{ get_edit_permission_title('repair_station_info') }}">
                                    {{ issue.repair_station_info or '未设置' }}</div>
                                <div class="form-help-text">维修站的编号和中文名称</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 问题信息标签页 -->
            <div class="tab-pane fade" id="problem" role="tabpanel" aria-labelledby="problem-tab">
                <div class="form-section">
                    <h5><i class="fas fa-exclamation-triangle me-2"></i>问题信息</h5>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="detail-field">
                                <label class="{{ get_label_class('complaint_category') }}">抱怨类别 {{ get_update_marker('complaint_category') }}</label>
                                <div class="detail-value {{ is_field_editable('complaint_category') }}{{ ' empty' if not issue.complaint_category }} {{ is_field_updated('complaint_category') }}" 
                                     data-field="complaint_category" data-type="select" 
                                     data-options='{"PDI":"PDI","AFTER_SALES":"售后"}' title="{{ get_edit_permission_title('complaint_category') }}">
                                    {% if issue.complaint_category %}
                                        <span class="badge bg-warning text-dark">{{ issue.complaint_category.chinese_label }}</span>
                                    {% else %}
                                        未设置
                                    {% endif %}</div>
                                <div class="form-help-text">抱怨类别：PDI或售后 {% if field_permissions is defined and field_permissions and not field_permissions.get('complaint_category', False) and current_user.role.value == "MANAGER" %}<small class="text-muted">(经理受限字段)</small>{% endif %}</div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="detail-field">
                                <label class="{{ get_label_class('issue_status') }}">议题状态 {{ get_update_marker('issue_status') }}</label>
                                <div class="detail-value {{ is_field_editable('issue_status') }}{{ ' empty' if not issue.issue_status }} {{ is_field_updated('issue_status') }}"
                                     data-field="issue_status" data-type="select" title="{{ get_edit_permission_title('issue_status') }}"
                                     data-options='{"PRE_ANLAUF":"已上Pre-Anlauf","MEETING_ANALYZING":"已上会，分析中","MEETING_ANALYSIS_COMPLETE":"已上会，分析完成","MEETING_MEASURES_FORMULATING":"已上会，措施制定","MEETING_MEASURES_IMPLEMENTING":"已上会，措施落实","MEETING_CONTINUE_TRACKING":"已上会，继续跟踪","NO_MEETING_CONFIRMING":"未上会，确认中","NO_MEETING_CONTINUE_TRACKING":"未上会，继续跟踪","NO_MEETING_INVALID_COMPLAINT":"未上会，无效抱怨","NO_MEETING_MEASURES_DEFINED":"未上会，已定义措施","NO_MEETING_MEASURES_IMPLEMENTING":"未上会，措施实施"}'>
                                    {% if issue.issue_status %}
                                        <span class="status-badge-enhanced"
                                              style="background-color: {{ issue.issue_status.background_color }};
                                                     color: {{ issue.issue_status.text_color }};">
                                            <i class="fas fa-flag"></i>
                                            {{ issue.issue_status.chinese_label }}
                                        </span>
                                    {% else %}
                                        未设置
                                    {% endif %}</div>
                                <div class="form-help-text">当前议题状态 {% if field_permissions is defined and field_permissions and not field_permissions.get('issue_status', False) %}<small class="text-muted">(仅项目负责人可编辑)</small>{% endif %}</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12 mb-3">
                            <div class="detail-field">
                                <label class="{{ get_label_class('problem_description') }}">问题描述 {{ get_update_marker('problem_description') }}</label>
                                <div class="detail-value {{ is_field_editable('problem_description') }}{{ ' empty' if not issue.problem_description }} {{ is_field_updated('problem_description') }}" 
                                     data-field="problem_description" data-type="textarea" title="{{ get_edit_permission_title('problem_description') }}">
                                    {{ issue.problem_description or '未填写' }}</div>
                                <div class="form-help-text">详细的问题描述 {% if field_permissions is defined and field_permissions and not field_permissions.get('problem_description', False) and current_user.role.value == 'PROJECT_MANAGER' %}<small class="text-muted">(项目负责人受限字段)</small>{% endif %}</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12 mb-3">
                            <div class="detail-field">
                                <label class="{{ get_label_class('after_sales_fault_description') }}">售后故障描述 {{ get_update_marker('after_sales_fault_description') }}</label>
                                <div class="detail-value {{ is_field_editable('after_sales_fault_description') }}{{ ' empty' if not issue.after_sales_fault_description }} {{ is_field_updated('after_sales_fault_description') }}" 
                                     data-field="after_sales_fault_description" data-type="textarea" title="{{ get_edit_permission_title('after_sales_fault_description') }}">
                                    {{ issue.after_sales_fault_description or '未填写' }}</div>
                                <div class="form-help-text">售后故障的详细描述</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12 mb-3">
                            <div class="detail-field">
                                <label class="{{ get_label_class('analysis_measures') }}">分析/措施 {{ get_update_marker('analysis_measures') }}</label>
                                <div class="detail-value {{ is_field_editable('analysis_measures') }}{{ ' empty' if not issue.analysis_measures }} {{ is_field_updated('analysis_measures') }}" 
                                     data-field="analysis_measures" data-type="textarea" title="{{ get_edit_permission_title('analysis_measures') }}">
                                    {{ issue.analysis_measures or '未填写' }}</div>
                                <div class="form-help-text">问题分析和解决措施 {% if field_permissions is defined and field_permissions and not field_permissions.get('analysis_measures', False) and current_user.role.value == 'PROJECT_MANAGER' %}<small class="text-muted">(项目负责人受限字段)</small>{% endif %}</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="detail-field">
                                <label class="{{ get_label_class('problem_type') }}">问题类型 {{ get_update_marker('problem_type') }}</label>
                                <div class="detail-value {{ is_field_editable('problem_type') }}{{ ' empty' if not issue.problem_type }} {{ is_field_updated('problem_type') }}" 
                                     data-field="problem_type" data-type="text" title="{{ get_edit_permission_title('problem_type') }}">
                                    {{ issue.problem_type or '未设置' }}</div>
                                <div class="form-help-text">问题分类类型 {% if field_permissions is defined and field_permissions and not field_permissions.get('problem_type', False) and current_user.role.value == 'PROJECT_MANAGER' %}<small class="text-muted">(项目负责人受限字段)</small>{% endif %}</div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="detail-field">
                                <label class="{{ get_label_class('fault_parts_status') }}">故障件状态 {{ get_update_marker('fault_parts_status') }}</label>
                                <div class="detail-value {{ is_field_editable('fault_parts_status') }}{{ ' empty' if not issue.fault_parts_status }} {{ is_field_updated('fault_parts_status') }}" 
                                     data-field="fault_parts_status" data-type="text" title="{{ get_edit_permission_title('fault_parts_status') }}">
                                    {{ issue.fault_parts_status or '未设置' }}</div>
                                <div class="form-help-text">故障件的处理状态 {% if field_permissions is defined and field_permissions and not field_permissions.get('fault_parts_status', False) and current_user.role.value == 'PROJECT_MANAGER' %}<small class="text-muted">(项目负责人受限字段)</small>{% endif %}</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="detail-field">
                                <label class="{{ get_label_class('warehouse_received_fault_parts') }}">仓库收到故障件 {{ get_update_marker('warehouse_received_fault_parts') }}</label>
                                <div class="detail-value {{ is_field_editable('warehouse_received_fault_parts') }}{{ ' empty' if issue.warehouse_received_fault_parts is none }} {{ is_field_updated('warehouse_received_fault_parts') }}" 
                                     data-field="warehouse_received_fault_parts" data-type="boolean" title="{{ get_edit_permission_title('warehouse_received_fault_parts') }}">
                                    <div class="boolean-value">
                                        {% if issue.warehouse_received_fault_parts is not none %}
                                            {% if issue.warehouse_received_fault_parts %}
                                                <span class="boolean-badge boolean-true">
                                                    <i class="fas fa-check me-1"></i>是
                                                </span>
                                            {% else %}
                                                <span class="boolean-badge boolean-false">
                                                    <i class="fas fa-times me-1"></i>否
                                                </span>
                                            {% endif %}
                                        {% else %}
                                            未设置
                                        {% endif %}
                                    </div></div>
                                <div class="form-help-text">仓库是否已收到故障件 {% if field_permissions is defined and field_permissions and not field_permissions.get('warehouse_received_fault_parts', False) and current_user.role.value == 'PROJECT_MANAGER' %}<small class="text-muted">(项目负责人受限字段)</small>{% endif %} {% if field_permissions is defined and field_permissions and not field_permissions.get('warehouse_received_fault_parts', False) and current_user.role.value != "ADMIN" %}<small class="text-muted">(仅管理员可编辑)</small>{% endif %}</div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="detail-field">
                                <label class="{{ get_label_class('mark') }}">标记 {{ get_update_marker('mark') }}</label>
                                <div class="detail-value {{ is_field_editable('mark') }}{{ ' empty' if not issue.mark }} {{ is_field_updated('mark') }}"
                                     data-field="mark" data-type="text" data-maxlength="100" title="{{ get_edit_permission_title('mark') }}">
                                    {{ issue.mark or '未设置' }}</div>
                                <div class="form-help-text">议题标记（支持文本，最大100字符）{% if field_permissions is defined and field_permissions and not field_permissions.get('mark', False) and current_user.role.value == 'PROJECT_MANAGER' %}<small class="text-muted">(项目负责人受限字段)</small>{% endif %}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 流程信息标签页 -->
            <div class="tab-pane fade" id="process" role="tabpanel" aria-labelledby="process-tab">
                <div class="form-section">
                    <h5><i class="fas fa-cogs me-2"></i>流程信息</h5>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="detail-field">
                                <label class="{{ get_label_class('review_status') }}">审核状态 {{ get_update_marker('review_status') }}</label>
                                <div class="detail-value {{ is_field_editable('review_status') }}{{ ' empty' if not issue.review_status }} {{ is_field_updated('review_status') }}" 
                                     data-field="review_status" data-type="text" title="{{ get_edit_permission_title('review_status') }}">
                                    {{ issue.review_status or '未设置' }}</div>
                                <div class="form-help-text">当前审核状态</div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="detail-field">
                                <label class="{{ get_label_class('engineer_first_review_time') }}">工程师首次审核时间 {{ get_update_marker('engineer_first_review_time') }}</label>
                                <div class="detail-value {{ is_field_editable('engineer_first_review_time') }}{{ ' empty' if not issue.engineer_first_review_time }} {{ is_field_updated('engineer_first_review_time') }}" 
                                     data-field="engineer_first_review_time" data-type="datetime" title="{{ get_edit_permission_title('engineer_first_review_time') }}">
                                    {{ issue.engineer_first_review_time.strftime('%Y-%m-%d %H:%M:%S') if issue.engineer_first_review_time else '未设置' }}</div>
                                <div class="form-help-text">工程师首次审核的时间</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="detail-field">
                                <label class="{{ get_label_class('engineer_first_review_week') }}">工程师首次审核周次 {{ get_update_marker('engineer_first_review_week') }}</label>
                                <div class="detail-value {{ is_field_editable('engineer_first_review_week') }}{{ ' empty' if not issue.engineer_first_review_week }} {{ is_field_updated('engineer_first_review_week') }}" 
                                     data-field="engineer_first_review_week" data-type="number" title="{{ get_edit_permission_title('engineer_first_review_week') }}">
                                    {{ issue.engineer_first_review_week or '未设置' }}</div>
                                <div class="form-help-text">工程师首次审核的周次</div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="detail-field">
                                <label class="{{ get_label_class('zp8_delivery_date') }}">ZP8报交日期 {{ get_update_marker('zp8_delivery_date') }}</label>
                                <div class="detail-value {{ is_field_editable('zp8_delivery_date') }}{{ ' empty' if not issue.zp8_delivery_date }} {{ is_field_updated('zp8_delivery_date') }}" 
                                     data-field="zp8_delivery_date" data-type="datetime" title="{{ get_edit_permission_title('zp8_delivery_date') }}">
                                    {{ issue.zp8_delivery_date.strftime('%Y-%m-%d %H:%M:%S') if issue.zp8_delivery_date else '未设置' }}</div>
                                <div class="form-help-text">ZP8报交的日期</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="detail-field">
                                <label class="{{ get_label_class('meeting_topic_name') }}">上会议题名 {{ get_update_marker('meeting_topic_name') }}</label>
                                <div class="detail-value {{ is_field_editable('meeting_topic_name') }}{{ ' empty' if not issue.meeting_topic_name }} {{ is_field_updated('meeting_topic_name') }}" 
                                     data-field="meeting_topic_name" data-type="text" title="{{ get_edit_permission_title('meeting_topic_name') }}">
                                    {{ issue.meeting_topic_name or '未设置' }}</div>
                                <div class="form-help-text">上会时的议题名称 {% if field_permissions is defined and field_permissions and not field_permissions.get('meeting_topic_name', False) and current_user.role.value == 'PROJECT_MANAGER' %}<small class="text-muted">(项目负责人受限字段)</small>{% endif %} {% if field_permissions is defined and field_permissions and not field_permissions.get('meeting_topic_name', False) and current_user.role.value != "ADMIN" %}<small class="text-muted">(仅管理员可编辑)</small>{% endif %}</div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <!-- 空列 用于布局平衡 -->
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="detail-field">
                                <label class="{{ get_label_class('problem_in_meeting') }}">问题是否上会 {{ get_update_marker('problem_in_meeting') }}</label>
                                <div class="detail-value {{ is_field_editable('problem_in_meeting') }}{{ ' empty' if issue.problem_in_meeting is none }} {{ is_field_updated('problem_in_meeting') }}"
                                     data-field="problem_in_meeting" data-type="boolean" title="{{ get_edit_permission_title('problem_in_meeting') }}">
                                    <div class="boolean-value">
                                        {% if issue.problem_in_meeting is not none %}
                                            {% if issue.problem_in_meeting %}
                                                <span class="boolean-badge boolean-true">
                                                    <i class="fas fa-check me-1"></i>是
                                                </span>
                                            {% else %}
                                                <span class="boolean-badge boolean-false">
                                                    <i class="fas fa-times me-1"></i>否
                                                </span>
                                            {% endif %}
                                        {% else %}
                                            未设置
                                        {% endif %}
                                    </div></div>
                                <div class="form-help-text">问题是否已上会讨论 {% if field_permissions is defined and field_permissions and not field_permissions.get('problem_in_meeting', False) %}<small class="text-muted">(仅项目负责人可编辑)</small>{% endif %}</div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="detail-field">
                                <label class="{{ get_label_class('complaint_in_meeting') }}">抱怨是否上会 {{ get_update_marker('complaint_in_meeting') }}</label>
                                <div class="detail-value {{ is_field_editable('complaint_in_meeting') }}{{ ' empty' if issue.complaint_in_meeting is none }} {{ is_field_updated('complaint_in_meeting') }}"
                                     data-field="complaint_in_meeting" data-type="boolean" title="{{ get_edit_permission_title('complaint_in_meeting') }}">
                                    <div class="boolean-value">
                                        {% if issue.complaint_in_meeting is not none %}
                                            {% if issue.complaint_in_meeting %}
                                                <span class="boolean-badge boolean-true">
                                                    <i class="fas fa-check me-1"></i>是
                                                </span>
                                            {% else %}
                                                <span class="boolean-badge boolean-false">
                                                    <i class="fas fa-times me-1"></i>否
                                                </span>
                                            {% endif %}
                                        {% else %}
                                            未设置
                                        {% endif %}
                                    </div></div>
                                <div class="form-help-text">抱怨是否已上会讨论 {% if field_permissions is defined and field_permissions and not field_permissions.get('complaint_in_meeting', False) %}<small class="text-muted">(仅项目负责人可编辑)</small>{% endif %}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
                    
                    <!-- 自定义信息选项卡 -->
                    <div class="tab-pane fade" id="custom" role="tabpanel" aria-labelledby="custom-tab">
                        <div class="row">
                            <!-- 标签管理 -->
                            <div class="col-12 mb-4">
                                <div class="card">
                                    <div class="card-header bg-secondary text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-tags me-2"></i>议题标签
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <!-- 现有标签显示 -->
                                        <div class="mb-3">
                                            <div class="d-flex flex-wrap gap-2" id="current-tags">
                                                {% for tag in issue.tags_relationship %}
                                                    <span class="badge" style="background-color: {{ tag.color }}; color: white;" 
                                                          data-tag-id="{{ tag.id }}">
                                                        {{ tag.name }}
                                                        <button type="button" class="btn-close btn-close-white btn-sm ms-1" 
                                                                onclick="removeTag({{ tag.id }})" aria-label="删除标签"></button>
                                                    </span>
                                                {% endfor %}
                                                <span class="text-muted" id="no-tags-message" {% if issue.tags_relationship.count() > 0 %}style="display: none;"{% endif %}>
                                                    暂无标签
                                                </span>
                                            </div>
                                        </div>
                                        
                                        <!-- 添加标签 -->
                                        <div class="mb-3">
                                            <label class="form-label">添加标签</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="new-tag-input" 
                                                       placeholder="输入标签名称..." list="tag-suggestions">
                                                <datalist id="tag-suggestions">
                                                    <!-- 动态填充建议标签 -->
                                                </datalist>
                                                <button id="add-tag-btn" class="btn btn-outline-primary" type="button" onclick="addTag()">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                            </div>
                                        </div>
                                        
                                        <!-- 标签颜色选择 -->
                                        <div class="mb-3">
                                            <label class="form-label">标签颜色</label>
                                            <div class="d-flex gap-2">
                                                <button type="button" class="btn btn-sm color-picker-btn" 
                                                        style="background-color: #007bff;" data-color="#007bff"></button>
                                                <button type="button" class="btn btn-sm color-picker-btn" 
                                                        style="background-color: #28a745;" data-color="#28a745"></button>
                                                <button type="button" class="btn btn-sm color-picker-btn" 
                                                        style="background-color: #dc3545;" data-color="#dc3545"></button>
                                                <button type="button" class="btn btn-sm color-picker-btn" 
                                                        style="background-color: #ffc107;" data-color="#ffc107"></button>
                                                <button type="button" class="btn btn-sm color-picker-btn" 
                                                        style="background-color: #17a2b8;" data-color="#17a2b8"></button>
                                                <button type="button" class="btn btn-sm color-picker-btn" 
                                                        style="background-color: #6c757d;" data-color="#6c757d"></button>
                                                <button type="button" class="btn btn-sm color-picker-btn" 
                                                        style="background-color: #e83e8c;" data-color="#e83e8c"></button>
                                            </div>
                                        </div>
                                        
                                        <!-- 常用标签 -->
                                        <div class="mb-3">
                                            <label class="form-label">常用标签</label>
                                            <div class="d-flex flex-wrap gap-2" id="popular-tags">
                                                <!-- 动态填充热门标签 -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 附件管理 -->
                            <div class="col-12 mb-4">
                                <div class="card">
                                    <div class="card-header bg-secondary text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-paperclip me-2"></i>附件管理
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <!-- 文件上传区域 -->
                                        <div class="mb-3">
                                            <label class="form-label">上传附件</label>
                                            <div class="upload-area" id="upload-area">
                                                <input type="file" class="form-control" id="file-input" multiple 
                                                       accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.csv,.jpg,.jpeg,.png,.gif,.msg,.eml,.zip,.rar">
                                                <div class="upload-placeholder text-center py-4">
                                                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-2"></i>
                                                    <p class="text-muted">点击选择文件或拖拽文件到此处</p>
                                                    <small class="text-muted">
                                                        支持：PDF、Word、Excel、PowerPoint、图片、Outlook邮件等
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                        
                                                                <!-- 上传进度 -->
                        <div class="mb-3" id="upload-progress" style="display: none;">
                            <div class="progress position-relative">
                                <div class="progress-bar progress-bar-striped progress-bar-animated bg-primary" role="progressbar" style="width: 0%; transition: width 0.3s ease; background: linear-gradient(45deg, #007bff, #0056b3);"></div>
                            </div>
                            <div class="progress-text text-center mt-2 small text-muted"></div>
                        </div>
                                        
                                        <!-- 现有附件列表 -->
                                        <div class="mb-3">
                                            <label class="form-label">现有附件</label>
                                            <div class="attachments-list" id="attachments-list">
                                                <!-- 动态填充附件列表 -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 评论选项卡 -->
                    <div class="tab-pane fade" id="comments" role="tabpanel" aria-labelledby="comments-tab">
                        <div class="comments-section">
                            <!-- 评论列表 -->
                            <div class="comments-list mb-4" id="commentsList">
                                {% if comments %}
                                    {% for comment in comments %}
                                    <div class="comment-item" data-comment-id="{{ comment.id }}">
                                        <div class="comment-header d-flex justify-content-between align-items-start mb-2">
                                            <div class="comment-author-info">
                                                <div class="comment-author">
                                                    <i class="fas fa-user-circle me-2"></i>
                                                    <strong>{{ comment.author }}</strong>
                                                    {% if comment.is_internal %}
                                                        <span class="badge bg-warning text-dark ms-2">内部</span>
                                                    {% endif %}
                                                    {% if comment.is_edited %}
                                                        <span class="badge bg-info ms-2">已编辑</span>
                                                    {% endif %}
        </div>
                                                <div class="comment-time text-muted small">
                                                    <i class="fas fa-clock me-1"></i>
                                                    {{ comment.created_at.strftime('%Y-%m-%d %H:%M:%S') }}
                                                    {% if comment.is_edited and comment.updated_at %}
                                                        <span class="ms-2">(编辑于 {{ comment.updated_at.strftime('%Y-%m-%d %H:%M:%S') }})</span>
                                                    {% endif %}
                                                </div>
                                            </div>
                                            <div class="comment-actions">
                                                {% if current_user.username == comment.author or current_user.is_admin %}
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" 
                                                            data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li><a class="dropdown-item" href="#" onclick="editComment({{ comment.id }})">
                                                            <i class="fas fa-edit me-2"></i>编辑</a></li>
                                                        <li><a class="dropdown-item text-danger" href="#" onclick="deleteComment({{ comment.id }})">
                                                            <i class="fas fa-trash me-2"></i>删除</a></li>
                                                    </ul>
                                                </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                        <div class="comment-content" id="comment-content-{{ comment.id }}">{{ comment.content | safe }}</div>
                                        <!-- 编辑表单（隐藏） -->
                                        <div class="comment-edit-form d-none" id="comment-edit-{{ comment.id }}">
                                            <div class="mb-3">
                                                <textarea class="form-control" rows="3" id="comment-edit-content-{{ comment.id }}">{{ comment.content }}</textarea>
                                            </div>
                                            <div class="mb-3 form-check">
                                                <input type="checkbox" class="form-check-input" id="comment-edit-internal-{{ comment.id }}" 
                                                       {% if comment.is_internal %}checked{% endif %}>
                                                <label class="form-check-label" for="comment-edit-internal-{{ comment.id }}">
                                                    内部评论（仅内部人员可见）
                                                </label>
                                            </div>
                                            <div class="d-flex gap-2">
                                                <button class="btn btn-primary btn-sm" onclick="saveCommentEdit({{ comment.id }})">
                                                    <i class="fas fa-save me-1"></i>保存
                                                </button>
                                                <button class="btn btn-secondary btn-sm" onclick="cancelCommentEdit({{ comment.id }})">
                                                    <i class="fas fa-times me-1"></i>取消
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                {% else %}
                                    <div class="no-comments text-center text-muted py-4">
                                        <i class="fas fa-comments fa-3x mb-3"></i>
                                        <p>暂无评论，快来添加第一条评论吧！</p>
                                    </div>
                                {% endif %}
                            </div>
                            
                            <!-- 添加评论表单 -->
                            <div class="add-comment-form">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-plus-circle me-2"></i>添加评论
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <form id="addCommentForm">
                                            <div class="mb-3">
                                                <label for="commentContent" class="form-label">评论内容</label>
                                                <div class="mention-container position-relative">
                                                    <textarea class="form-control mention-textarea" id="commentContent" rows="4" 
                                                              placeholder="请输入您的评论... 输入@可提及其他用户"></textarea>
                                                    <!-- @提及下拉框 -->
                                                    <div class="mention-dropdown d-none" id="mentionDropdown">
                                                        <div class="mention-list" id="mentionList">
                                                            <!-- 用户列表将在这里动态生成 -->
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mb-3 form-check">
                                                <input type="checkbox" class="form-check-input" id="isInternal">
                                                <label class="form-check-label" for="isInternal">
                                                    内部评论（仅内部人员可见）
                                                </label>
                                            </div>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">
                                                    <i class="fas fa-info-circle me-1"></i>
                                                    支持@提及用户和基本文本格式
                                                </small>
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-paper-plane me-2"></i>发布评论
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧快速操作栏 -->
        <div class="col-lg-3 col-xl-2">
            <div class="sticky-sidebar">
                <!-- 快速操作面板 -->
                <div class="card quick-actions-panel mb-4">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>快速操作
                        </h6>
                    </div>
                    <div class="card-body p-3">
                        <div class="row g-2">
                            <div class="col-4">
                                <button class="btn btn-warning btn-sm w-100 text-center" onclick="updateMaintenanceStatus('PENDING')" style="white-space: nowrap; font-size: 0.75rem; padding: 0.25rem 0.2rem;">
                                    <i class="fas fa-clock me-1" style="font-size: 0.7rem;"></i>待维护
                                </button>
                            </div>
                            <div class="col-4">
                                <button class="btn btn-success btn-sm w-100 text-center" onclick="updateMaintenanceStatus('COMPLETED')" style="white-space: nowrap; font-size: 0.75rem; padding: 0.25rem 0.2rem;">
                                    <i class="fas fa-check me-1" style="font-size: 0.7rem;"></i>已维护
                                </button>
                            </div>
                            <div class="col-4">
                                <button class="btn btn-secondary btn-sm w-100 text-center" onclick="updateMaintenanceStatus('ON_HOLD')" style="white-space: nowrap; font-size: 0.75rem; padding: 0.25rem 0.2rem;">
                                    <i class="fas fa-pause me-1" style="font-size: 0.7rem;"></i>挂起
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 议题详情卡片 -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>议题详情
                        </h6>
                    </div>
                    <div class="card-body p-3">
                        <!-- 圆形图标统计区域 -->
                        <div class="circular-stats-container mb-4">
                            <div class="row">
                                <!-- 上报天数圆形统计 -->
                                <div class="col-6">
                                    <div class="circular-stat-item text-center">
                                        <div class="circular-icon" id="days-since-report-circle">
                                            <div class="circular-content">
                                                <div class="circular-number">
                                                    {{ days_since_report_new }}
                                                </div>
                                                <div class="circular-label">
                                                    {% if days_since_report_new == 0 %}
                                                        今天
                                                    {% elif days_since_report_new == 1 %}
                                                        天前
                                                    {% else %}
                                                        天前
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="circular-title mt-2">
                                            <i class="fas fa-calendar-alt me-1"></i>
                                            上报时间
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 字段更新圆形统计 -->
                                <div class="col-6">
                                    <div class="circular-stat-item text-center">
                                        <div class="circular-icon" id="updates-count-circle">
                                            <div class="circular-content">
                                                <div class="circular-number">
                                                    {{ updated_fields_stats.updated_count }}
                                                </div>
                                                <div class="circular-label">
                                                    {% if updated_fields_stats.has_updates %}
                                                        个更新
                                                    {% else %}
                                                        无更新
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="circular-title mt-2">
                                            <i class="fas fa-sync-alt me-1"></i>
                                            字段更新
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 字段完成度 -->
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span class="detail-label">
                                    <i class="fas fa-tasks me-1"></i>
                                    字段完成度
                                </span>
                                <span class="detail-value">
                                    <span class="badge bg-primary">{{ field_completion_stats.completion_percentage }}%</span>
                                </span>
                            </div>
                            <!-- 已填写统计文字 -->
                            <div class="mb-2">
                                <small class="text-muted">
                                    已填写 {{ field_completion_stats.filled_count }} / {{ field_completion_stats.total_count }} 个重要字段
                                </small>
                            </div>
                            <!-- 进度条 -->
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" 
                                     style="width: {{ field_completion_stats.completion_percentage }}%; background: linear-gradient(45deg, #007bff, #0056b3);" 
                                     aria-valuenow="{{ field_completion_stats.completion_percentage }}" 
                                     aria-valuemin="0" aria-valuemax="100">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作提示面板 -->
                <div class="card">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">
                            <i class="fas fa-lightbulb me-2"></i>操作提示
                        </h6>
                    </div>
                    <div class="card-body p-3">
                        <div class="text-muted small">
                            <p class="mb-2"><i class="fas fa-mouse me-1"></i> 双击字段可直接编辑</p>
                            <p class="mb-2"><i class="fas fa-save me-1"></i> 编辑后自动保存</p>
                            <p class="mb-0"><i class="fas fa-circle text-danger me-1"></i> 红色标记表示更新字段</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 保存指示器 -->
<div class="save-indicator" id="saveIndicator">
    <i class="fas fa-check me-2"></i>保存成功
</div>
{% endblock %}

{% block extra_js %}
<script>
// 全局变量定义
const ISSUE_ID = {{ issue.id }};

// 在线编辑功能
$(document).ready(function() {
    console.log('双击编辑功能已初始化，议题ID:', ISSUE_ID);
    
    // 双击编辑功能 - 只对可编辑字段启用
    $('.editable').dblclick(function() {
        console.log('触发双击编辑');
        const $this = $(this);

        // 检查是否为只读字段
        if ($this.hasClass('readonly')) {
            console.log('字段为只读，跳过编辑');
            return;
        }

        const field = $this.data('field');
        const type = $this.data('type');
        // 🔥 修复：更智能地获取当前值，避免包含"双击编辑"等额外文字
        let currentValue = $this.text().trim();
        
        // 清理可能存在的额外提示文字
        if (currentValue.includes('双击编辑')) {
            currentValue = currentValue.replace(/\s*双击编辑\s*/g, '').trim();
        }
        
        // 对于特殊类型的字段，尝试获取更准确的原始值
        if (type === 'select' || type === 'boolean') {
            // 对于选择类型，从badge或其他标签中提取实际值
            const badges = $this.find('.badge, .status-badge-enhanced, .boolean-badge');
            if (badges.length > 0) {
                currentValue = badges.last().text().trim();
            }
        } else if (type === 'number' && currentValue.includes('km')) {
            // 对于里程数字段，只保留数字部分
            currentValue = currentValue.replace(/\s*km\s*/g, '').trim();
        }
        
        const options = $this.data('options');
        
        console.log('编辑字段:', field, '类型:', type, '当前值:', currentValue);
        
        if ($this.hasClass('editing')) {
            console.log('已在编辑状态，跳过');
            return;
        }
        
        $this.addClass('editing');
        let input;
        
        if (type === 'textarea') {
            input = $('<textarea class="edit-textarea"></textarea>');
            if (currentValue !== '未填写' && currentValue !== '未设置') {
                input.val(currentValue);
            }
        } else if (type === 'select' && options) {
            input = $('<select class="edit-select"></select>');
            input.append('<option value="">请选择...</option>');
            Object.entries(options).forEach(([key, value]) => {
                const option = $(`<option value="${key}">${value}</option>`);
                if (currentValue.includes(value)) {
                    option.prop('selected', true);
                }
                input.append(option);
            });
        } else if (type === 'boolean') {
            input = $('<select class="edit-select"></select>');
            input.append('<option value="">请选择...</option>');
            input.append('<option value="true">是</option>');
            input.append('<option value="false">否</option>');
            if (currentValue.includes('是')) {
                input.val('true');
            } else if (currentValue.includes('否')) {
                input.val('false');
            }
        } else {
            input = $('<input class="edit-input">');
            input.attr('type', type === 'number' ? 'number' : type === 'datetime' ? 'datetime-local' : 'text');
            if (type === 'number' && $this.data('step')) {
                input.attr('step', $this.data('step'));
            }
            if ($this.data('maxlength')) {
                input.attr('maxlength', $this.data('maxlength'));
            }
            if (currentValue !== '未填写' && currentValue !== '未设置') {
                if (type === 'datetime' && currentValue !== '未设置') {
                    // 转换日期时间格式
                    try {
                    const dateValue = new Date(currentValue.replace(' ', 'T'));
                    const isoString = dateValue.toISOString().slice(0, 16);
                    input.val(isoString);
                    } catch (e) {
                        console.warn('日期格式转换失败:', currentValue);
                        input.val('');
                    }
                } else if (type === 'number' && currentValue.includes('km')) {
                    input.val(currentValue.replace(' km', ''));
                } else {
                    input.val(currentValue);
                }
            }
        }
        
        $this.html(input);
        input.focus().select();
        
        // 保存函数
        function saveField() {
            const newValue = input.val();
            console.log('保存字段:', field, '新值:', newValue);
            
            $.ajax({
                url: `{{ url_for('issues.update_field', issue_id=issue.id) }}`,
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                data: JSON.stringify({
                    field: field,
                    value: newValue
                }),
                success: function(response) {
                    console.log('保存响应:', response);
                    if (response.success) {
                        let displayValue = newValue || '未设置';
                        
                        // 根据字段类型格式化显示值
                        if (type === 'select' && options) {
                            if (newValue && options[newValue]) {
                                if (field === 'issue_status') {
                                    // 获取状态颜色信息
                                    const statusColors = {
                                        'PRE_ANLAUF': { bg: '#FCE4D6', text: '#333333' },
                                        'MEETING_ANALYZING': { bg: '#FF0000', text: '#ffffff' },
                                        'MEETING_ANALYSIS_COMPLETE': { bg: '#FFC000', text: '#333333' },
                                        'MEETING_MEASURES_FORMULATING': { bg: '#FFFF00', text: '#333333' },
                                        'MEETING_MEASURES_IMPLEMENTING': { bg: '#00B050', text: '#ffffff' },
                                        'MEETING_CONTINUE_TRACKING': { bg: '#00B0F0', text: '#ffffff' },
                                        'NO_MEETING_CONFIRMING': { bg: '#ED7D31', text: '#ffffff' },
                                        'NO_MEETING_CONTINUE_TRACKING': { bg: '#C7A1E3', text: '#333333' },
                                        'NO_MEETING_INVALID_COMPLAINT': { bg: '#808080', text: '#ffffff' },
                                        'NO_MEETING_MEASURES_DEFINED': { bg: '#66FFFF', text: '#333333' },
                                        'NO_MEETING_MEASURES_IMPLEMENTING': { bg: '#BF8F00', text: '#ffffff' }
                                    };
                                    const colors = statusColors[newValue] || { bg: '#808080', text: '#ffffff' };
                                    displayValue = `<span class="status-badge-enhanced" style="background-color: ${colors.bg}; color: ${colors.text};"><i class="fas fa-flag"></i> ${options[newValue]}</span>`;
                                    
                                    // 🔥 关键修复：同时更新页面顶部的议题状态显示
                                    updateTopIssueStatus(newValue, options[newValue], colors);
                                    
                                } else if (field === 'vehicle_status') {
                                    displayValue = `<span class="badge bg-info">${options[newValue]}</span>`;
                                } else if (field === 'complaint_category') {
                                    displayValue = `<span class="badge bg-warning text-dark">${options[newValue]}</span>`;
                                } else {
                                    displayValue = options[newValue];
                                }
                            }
                        } else if (type === 'boolean') {
                            if (newValue === 'true') {
                                displayValue = '<div class="boolean-value"><span class="boolean-badge boolean-true"><i class="fas fa-check me-1"></i>是</span></div>';
                            } else if (newValue === 'false') {
                                displayValue = '<div class="boolean-value"><span class="boolean-badge boolean-false"><i class="fas fa-times me-1"></i>否</span></div>';
                            } else {
                                displayValue = '未设置';
                            }
                        } else if (type === 'number' && field === 'mileage' && newValue) {
                            displayValue = `${parseFloat(newValue).toFixed(2)} km`;
                        } else if (type === 'datetime' && newValue) {
                            try {
                            const date = new Date(newValue);
                                displayValue = date.toLocaleString('zh-CN', {
                                    year: 'numeric',
                                    month: '2-digit',
                                    day: '2-digit',
                                    hour: '2-digit',
                                    minute: '2-digit',
                                    second: '2-digit'
                                });
                            } catch (e) {
                                displayValue = newValue;
                            }
                        }
                        
                        $this.html(displayValue);
                        $this.removeClass('editing empty');
                        
                        // 如果值为空，添加empty类
                        if (!newValue || newValue === '未设置') {
                            $this.addClass('empty');
                        }
                        
                        // 显示保存成功提示
                        showSaveIndicator();
                        
                        // 更新最后修改时间
                        updateLastModifiedTime();
                        
                    } else {
                        alert('保存失败：' + (response.message || response.error || '未知错误'));
                        $this.removeClass('editing');
                        // 🔥 修复：错误恢复时也使用清理后的值
                        let restoreValue = currentValue || '未设置';
                        if (restoreValue.includes('双击编辑')) {
                            restoreValue = restoreValue.replace(/\s*双击编辑\s*/g, '').trim();
                        }
                        $this.text(restoreValue);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX错误:', xhr, status, error);
                    let errorMessage = '保存失败，请重试';
                    
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.error) {
                            errorMessage += '。错误信息：' + response.error;
                        } else if (response.message) {
                            errorMessage += '。错误信息：' + response.message;
                        }
                    } catch (e) {
                        if (xhr.status === 404) {
                            errorMessage = '请求的接口不存在，请检查URL路径';
                        } else if (xhr.status === 500) {
                            errorMessage = '服务器内部错误，请联系管理员';
                        } else if (xhr.status === 400) {
                            errorMessage = '请求参数错误，请检查输入的数据';
                        } else if (xhr.status === 0) {
                            errorMessage = '网络连接失败，请检查网络连接';
                        }
                    }
                    
                    alert(errorMessage);
                    $this.removeClass('editing');
                    // 🔥 修复：网络错误恢复时也使用清理后的值
                    let restoreValue = currentValue || '未设置';
                    if (restoreValue.includes('双击编辑')) {
                        restoreValue = restoreValue.replace(/\s*双击编辑\s*/g, '').trim();
                    }
                    $this.text(restoreValue);
                }
            });
        }
        
        // 取消编辑函数
        function cancelEdit() {
            $this.removeClass('editing');
            // 🔥 修复：恢复时使用清理后的值，不包含"双击编辑"等额外文字
            let restoreValue = currentValue || '未设置';
            
            // 再次确保没有额外的提示文字
            if (restoreValue.includes('双击编辑')) {
                restoreValue = restoreValue.replace(/\s*双击编辑\s*/g, '').trim();
            }
            
            $this.text(restoreValue);
        }
        
        // 绑定事件
        input.blur(function() {
            // 延迟执行，避免与其他事件冲突
            setTimeout(saveField, 100);
        });
        
        input.keydown(function(e) {
            if (e.key === 'Enter' && type !== 'textarea') {
                e.preventDefault();
                saveField();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                cancelEdit();
            }
        });
    });
    
    // 添加视觉反馈 - 只对可编辑字段启用
    $('.editable').hover(
        function() {
            if (!$(this).hasClass('readonly')) {
                $(this).addClass('editable-hover');
            }
        },
        function() {
            $(this).removeClass('editable-hover');
        }
    );
});

// 更新页面相关元素（不刷新整页）
function updatePageElements(field, newValue) {
    // 更新顶部状态信息
    if (field === 'issue_status') {
        // 可以选择性地更新顶部的议题状态显示
        console.log('议题状态已更新:', newValue);
    }
    
    // 更新字段完整度进度条
    updateFieldCompleteness();
    
    // 更新最后更新时间显示
    updateLastModifiedTime();
}

// 更新字段完整度（已删除议题信息面板，此函数不再需要）
function updateFieldCompleteness() {
    // 此函数不再需要，因为议题信息面板已被删除
    console.log('字段完整度功能已删除');
}

// 更新最后修改时间
function updateLastModifiedTime() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    
    const timeString = `${year}/${month}/${day} ${hours}:${minutes}`;
    
    // 更新顶部的最后更新时间
    $('.card-body .row .col-md-3').eq(3).html(`<strong>最后更新：</strong>${timeString}`);
}

// 标记字段为已更新
function markFieldAsUpdated($element) {
    // 添加更新标记样式
    $element.addClass('field-recently-updated');
    
    // 3秒后移除标记
    setTimeout(() => {
        $element.removeClass('field-recently-updated');
    }, 3000);
}

// 显示保存成功指示器
function showSaveIndicator() {
    const indicator = $('#saveIndicator');
    if (indicator.length) {
    indicator.fadeIn(300);
    setTimeout(() => {
        indicator.fadeOut(300);
    }, 2000);
    } else {
        // 如果指示器不存在，创建一个临时提示
        const tempIndicator = $('<div class="alert alert-success" style="position: fixed; top: 20px; right: 20px; z-index: 9999; padding: 10px 20px; border-radius: 6px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);"><i class="fas fa-check me-2"></i>保存成功</div>');
        $('body').append(tempIndicator);
        setTimeout(() => {
            tempIndicator.fadeOut(300, function() {
                $(this).remove();
            });
        }, 2000);
    }
}

// 快速操作 - 更新维护状态
function updateMaintenanceStatus(status) {
    if (!confirm('确定要更新维护状态吗？')) return;
    
    console.log('更新维护状态:', status);
    
    $.ajax({
        url: `{{ url_for('issues.update_maintenance_status', issue_id=issue.id) }}`,
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        data: JSON.stringify({
            status: status
        }),
        success: function(response) {
            console.log('维护状态更新响应:', response);
            if (response.success) {
                showSaveIndicator();
                
                // 🔥 更新顶部维护状态显示（实时更新）
                updateTopMaintenanceStatus(status, response.new_status);
                
                // 更新最后修改时间
                updateLastModifiedTime();
            } else {
                alert('更新失败：' + (response.message || response.error || '未知错误'));
            }
        },
        error: function(xhr, status, error) {
            console.error('维护状态更新错误:', xhr, status, error);
            let errorMessage = '更新失败，请重试';
            
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMessage += '。错误信息：' + xhr.responseJSON.error;
            } else if (xhr.status === 404) {
                errorMessage = '请求的接口不存在';
            } else if (xhr.status === 500) {
                errorMessage = '服务器内部错误';
            }
            
            alert(errorMessage);
        }
    });
}

// 🔥 新增函数：更新顶部维护状态显示
function updateTopMaintenanceStatus(statusName, statusValue) {
    // 找到顶部维护状态显示区域
    const topMaintenanceContainer = $('.card-body .row .col-md-3').eq(1);
    
    if (topMaintenanceContainer.length > 0) {
        let badgeClass = 'bg-secondary';
        let textClass = '';
        
        // 根据状态值设置样式
        if (statusValue === '待维护') {
            badgeClass = 'bg-warning';
            textClass = 'text-dark';
        } else if (statusValue === '已维护') {
            badgeClass = 'bg-success';
        }
        
        // 创建新的维护状态HTML
        const newStatusHtml = `
            <strong>维护状态：</strong>
            <span class="badge ${badgeClass} ${textClass}">${statusValue}</span>
        `;
        
        // 更新顶部维护状态显示
        topMaintenanceContainer.html(newStatusHtml);
        
        // 添加更新动画效果
        topMaintenanceContainer.find('.badge').addClass('status-updated');
        setTimeout(() => {
            topMaintenanceContainer.find('.badge').removeClass('status-updated');
        }, 2000);
    }
}

// 调试信息
console.log('议题详情页面JavaScript已加载');
console.log('可编辑元素数量:', $('.editable').length);

// 在现有的 extra_js 块中添加评论相关功能

// 评论相关功能
$(document).ready(function() {
    // 页面加载时恢复选项卡状态
    restoreActiveTab();
    
    // 监听选项卡切换，保存当前状态
    $('#issueDetailTabs button[data-bs-toggle="tab"]').on('shown.bs.tab', function (e) {
        const activeTab = e.target.getAttribute('data-bs-target').replace('#', '');
        localStorage.setItem('issueDetailActiveTab', activeTab);
    });
});

// 保存当前活跃的选项卡
function saveActiveTab() {
    const activeTab = $('#issueDetailTabs .nav-link.active').attr('data-bs-target');
    if (activeTab) {
        localStorage.setItem('issueDetailActiveTab', activeTab.replace('#', ''));
    }
}

// 恢复选项卡状态
function restoreActiveTab() {
    const savedTab = localStorage.getItem('issueDetailActiveTab');
    if (savedTab) {
        // 移除所有active状态
        $('#issueDetailTabs .nav-link').removeClass('active');
        $('.tab-pane').removeClass('show active');
        
        // 激活保存的选项卡
        $(`#issueDetailTabs button[data-bs-target="#${savedTab}"]`).addClass('active');
        $(`#${savedTab}`).addClass('show active');
        
        // 清除保存的状态（可选）
        // localStorage.removeItem('issueDetailActiveTab');
    }
}

// 添加评论（修改版，不刷新页面）
function addComment(content, isInternal) {
    // 禁用提交按钮，防止重复提交
    const $submitBtn = $('#addCommentForm button[type="submit"]');
    const originalText = $submitBtn.html();
    $submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>发布中...');
    
    $.ajax({
        url: `{{ url_for('issues.add_comment', issue_id=issue.id) }}`,
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        data: JSON.stringify({
            content: content,
            is_internal: isInternal
        }),
        success: function(response) {
            if (response.success && response.comment) {
                // 清空表单
                $('#commentContent').val('');
                $('#isInternal').prop('checked', false);
                
                // 显示成功提示
                showSaveIndicator('评论添加成功');
                
                // 使用返回的真实评论数据动态添加评论
                addCommentToList(response.comment);
                
                // 更新评论数量
                updateCommentsCount();
                
                // 恢复提交按钮
                $submitBtn.prop('disabled', false).html(originalText);
                
            } else {
                alert('添加评论失败：' + (response.error || '未知错误'));
                $submitBtn.prop('disabled', false).html(originalText);
            }
        },
        error: function(xhr, status, error) {
            console.error('添加评论错误:', xhr, status, error);
            let errorMessage = '添加评论失败，请重试';
            
            try {
                const response = JSON.parse(xhr.responseText);
                if (response.error) {
                    errorMessage += '。错误信息：' + response.error;
                }
            } catch (e) {
                if (xhr.status === 403) {
                    errorMessage = '您没有权限添加评论';
                } else if (xhr.status === 404) {
                    errorMessage = '议题不存在';
                }
            }
            
            alert(errorMessage);
            $submitBtn.prop('disabled', false).html(originalText);
        }
    });
}

// 修改动态添加评论到列表的函数
function addCommentToList(commentData) {
    // 移除"暂无评论"提示（如果存在）
    $('.no-comments').remove();
    
    // 清理评论内容，确保左对齐
    let cleanedContent = commentData.content;
    // 移除前导空格和缩进
    cleanedContent = cleanedContent.replace(/^[\s\u00A0]+/gm, ''); // 移除行首的空格和非断空格
    cleanedContent = cleanedContent.replace(/^\s+/g, ''); // 移除开头的空格
    cleanedContent = cleanedContent.trim(); // 移除首尾空格
    
    // 处理评论内容，确保换行正确且无多余缩进
    const formattedContent = cleanedContent.replace(/\n/g, '<br>').trim();
    
    // 创建新评论HTML，使用真实的评论ID
    const newCommentHtml = `
        <div class="comment-item new-comment" data-comment-id="${commentData.id}">
            <div class="comment-header d-flex justify-content-between align-items-start mb-2">
                <div class="comment-author-info">
                    <div class="comment-author">
                        <i class="fas fa-user-circle me-2"></i>
                        <strong>${commentData.author}</strong>
                        ${commentData.is_internal ? '<span class="badge bg-warning text-dark ms-2">内部</span>' : ''}
                        <span class="badge bg-success ms-2">刚刚发布</span>
                    </div>
                    <div class="comment-time text-muted small">
                        <i class="fas fa-clock me-1"></i>
                        ${commentData.created_at}
                    </div>
                </div>
                <div class="comment-actions">
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" 
                                data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="editComment(${commentData.id})">
                                <i class="fas fa-edit me-2"></i>编辑</a></li>
                            <li><a class="dropdown-item text-danger" href="#" onclick="deleteComment(${commentData.id})">
                                <i class="fas fa-trash me-2"></i>删除</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="comment-content" id="comment-content-${commentData.id}">
                ${formattedContent}
            </div>
            <!-- 编辑表单（隐藏） -->
            <div class="comment-edit-form d-none" id="comment-edit-${commentData.id}">
                <div class="mb-3">
                    <textarea class="form-control" rows="3" id="comment-edit-content-${commentData.id}">${commentData.content}</textarea>
                </div>
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="comment-edit-internal-${commentData.id}" 
                           ${commentData.is_internal ? 'checked' : ''}>
                    <label class="form-check-label" for="comment-edit-internal-${commentData.id}">
                        内部评论（仅内部人员可见）
                    </label>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-primary btn-sm" onclick="saveCommentEdit(${commentData.id})">
                        <i class="fas fa-save me-1"></i>保存
                    </button>
                    <button class="btn btn-secondary btn-sm" onclick="cancelCommentEdit(${commentData.id})">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                </div>
            </div>
        </div>
    `;
    
    // 将新评论添加到列表顶部
    $('#commentsList').prepend(newCommentHtml);
    
    // 对新添加的评论内容进行清理，确保左对齐
    const $newCommentContent = $('.new-comment .comment-content');
    let newContent = $newCommentContent.html();
    // 清理前导空格和缩进
    newContent = newContent.replace(/^[\s\u00A0]+/gm, ''); // 移除行首的空格和非断空格
    newContent = newContent.replace(/^\s+/g, ''); // 移除开头的空格
    newContent = newContent.trim(); // 移除首尾空格
    $newCommentContent.html(newContent);
    
    // 添加新评论的动画效果
    $('.new-comment').hide().fadeIn(500);
    
    // 3秒后移除"刚刚发布"标签
    setTimeout(() => {
        $('.new-comment .badge.bg-success').fadeOut(300, function() {
            $(this).remove();
        });
        $('.new-comment').removeClass('new-comment');
    }, 3000);
}

// 编辑评论（保持选项卡状态）
function editComment(commentId) {
    saveActiveTab(); // 保存当前选项卡
    
    // 隐藏评论内容，显示编辑表单
    $(`#comment-content-${commentId}`).addClass('d-none');
    $(`#comment-edit-${commentId}`).removeClass('d-none');
}

// 取消编辑评论
function cancelCommentEdit(commentId) {
    // 显示评论内容，隐藏编辑表单
    $(`#comment-content-${commentId}`).removeClass('d-none');
    $(`#comment-edit-${commentId}`).addClass('d-none');
}

// 保存评论编辑（修改版，尽量不刷新页面）
function saveCommentEdit(commentId) {
    const content = $(`#comment-edit-content-${commentId}`).val().trim();
    const isInternal = $(`#comment-edit-internal-${commentId}`).is(':checked');
    
    if (!content) {
        alert('评论内容不能为空');
        return;
    }
    
    // 禁用保存按钮
    const $saveBtn = $(event.target);
    $saveBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>保存中...');
    
    $.ajax({
        url: `/issues/${ISSUE_ID}/comments/${commentId}`,
        method: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify({
            content: content,
            is_internal: isInternal
        }),
        success: function(response) {
            if (response.success) {
                // 更新评论内容显示
                const processedContent = processMentionInComment(content);
                $(`#comment-content-${commentId}`).html(processedContent);
                
                // 更新内部标签显示
                const $commentHeader = $(`.comment-item[data-comment-id="${commentId}"] .comment-author`);
                $commentHeader.find('.badge').remove();
                if (isInternal) {
                    $commentHeader.append('<span class="badge bg-warning text-dark ms-2">内部</span>');
                }
                
                // 隐藏编辑表单，显示内容
                cancelCommentEdit(commentId);
                
                showNotification('评论编辑成功', 'success');
            } else {
                alert('编辑失败：' + (response.error || response.message));
            }
        },
        error: function(xhr, status, error) {
            console.error('编辑评论失败:', xhr.responseText);
            alert('编辑失败，请重试');
        },
        complete: function() {
            $saveBtn.prop('disabled', false).html('<i class="fas fa-save me-1"></i>保存');
        }
    });
}

// 删除评论（保持选项卡状态）
function deleteComment(commentId) {
    if (!commentId || commentId === 'new') {
        alert('无法删除此评论：评论ID无效');
        return;
    }
    
    if (!confirm('确定要删除这条评论吗？此操作不可撤销。')) {
        return;
    }
    
    saveActiveTab(); // 保存当前选项卡
    
    $.ajax({
        url: `{{ url_for('issues.delete_comment', issue_id=issue.id, comment_id=0) }}`.replace('0', commentId),
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json'
        },
        success: function(response) {
            if (response.success) {
                showSaveIndicator('评论删除成功');
                
                // 动画移除评论元素
                $(`.comment-item[data-comment-id="${commentId}"]`).fadeOut(300, function() {
                    $(this).remove();
                    
                    // 使用服务器返回的实际评论数更新显示
                    if (response.active_comments_count !== undefined) {
                        $('#comments-count').text(response.active_comments_count);
                        
                        // 更新右侧面板的评论数
                        $('.info-value').each(function() {
                            const $parent = $(this).parent();
                            if ($parent.find('.info-label').text().includes('评论数')) {
                                $(this).text(response.active_comments_count);
                            }
                        });
                        
                        // 如果没有评论了，显示空状态
                        if (response.active_comments_count === 0) {
                            $('#commentsList').html(`
                                <div class="no-comments text-center text-muted py-4">
                                    <i class="fas fa-comments fa-3x mb-3"></i>
                                    <p>暂无评论，快来添加第一条评论吧！</p>
                                </div>
                            `);
                        }
                    } else {
                        // 如果服务器没有返回评论数，则本地计算
                        updateCommentsCount();
                        
                        // 检查是否还有评论，如果没有则显示空状态
                        if ($('.comment-item').length === 0) {
                            $('#commentsList').html(`
                                <div class="no-comments text-center text-muted py-4">
                                    <i class="fas fa-comments fa-3x mb-3"></i>
                                    <p>暂无评论，快来添加第一条评论吧！</p>
                                </div>
                            `);
                        }
                    }
                });
            } else {
                alert('删除评论失败：' + (response.error || '未知错误'));
            }
        },
        error: function(xhr, status, error) {
            console.error('删除评论错误:', xhr, status, error);
            let errorMessage = '删除评论失败，请重试';
            
            try {
                const response = JSON.parse(xhr.responseText);
                if (response.error) {
                    errorMessage += '。错误信息：' + response.error;
                } else if (response.message) {
                    errorMessage += '。错误信息：' + response.message;
                }
            } catch (e) {
                if (xhr.status === 403) {
                    errorMessage = '您没有权限删除此评论';
                } else if (xhr.status === 404) {
                    errorMessage = '评论不存在或已被删除';
                } else if (xhr.status === 500) {
                    errorMessage = '服务器内部错误，请稍后重试';
                }
            }
            
            alert(errorMessage);
        }
    });
}

// 更新评论数量（保持选项卡状态）
function updateCommentsCount() {
    // 只计算页面上实际存在的评论（不包括已删除的）
    const count = $('.comment-item').length;
    $('#comments-count').text(count);
    
    // 同时更新右侧面板的评论数
    $('.info-value').each(function() {
        const $parent = $(this).parent();
        if ($parent.find('.info-label').text().includes('评论数')) {
            $(this).text(count);
        }
    });
}

// 增强保存指示器，支持自定义消息
function showSaveIndicator(message = '保存成功') {
    const indicator = $('#saveIndicator');
    if (indicator.length) {
        indicator.html(`<i class="fas fa-check me-2"></i>${message}`).fadeIn(300);
        setTimeout(() => {
            indicator.fadeOut(300);
        }, 2000);
    } else {
        // 如果指示器不存在，创建一个临时提示
        const tempIndicator = $(`<div class="alert alert-success" style="position: fixed; top: 20px; right: 20px; z-index: 9999; padding: 10px 20px; border-radius: 6px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);"><i class="fas fa-check me-2"></i>${message}</div>`);
        $('body').append(tempIndicator);
        setTimeout(() => {
            tempIndicator.fadeOut(300, function() {
                $(this).remove();
            });
        }, 2000);
    }
}

// 在extra_js块中添加@功能的JavaScript

// @提及功能相关变量
let mentionUsers = [];
let mentionDropdownVisible = false;
let currentMentionQuery = '';
let selectedMentionIndex = -1;

// 页面加载时获取用户列表
$(document).ready(function() {
    loadMentionUsers();
    initializeMentionFunctionality();
});

// 加载可提及的用户列表
function loadMentionUsers() {
    $.ajax({
        url: `/issues/api/users`,
        method: 'GET',
        timeout: 10000,  // 10秒超时
        success: function(response) {
            if (response.success) {
                mentionUsers = response.users;
                console.log('用户列表加载成功:', mentionUsers.length, '个用户');
                
                // 为每个用户生成头像颜色
                mentionUsers.forEach(user => {
                    if (!user.avatar_url) {
                        user.fallbackColor = generateAvatarColor(user.username);
                    }
                });
            } else {
                console.error('获取用户列表失败:', response.message);
                showNotification('获取用户列表失败，@功能可能无法正常使用', 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('获取用户列表请求失败:', error);
            if (status === 'timeout') {
                showNotification('获取用户列表超时，请检查网络连接', 'error');
            } else {
                showNotification('无法加载用户列表，@功能暂时不可用', 'error');
            }
        }
    });
}

// 为用户名生成一致的头像颜色
function generateAvatarColor(username) {
    const colors = [
        'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
        'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
        'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
        'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
        'linear-gradient(135deg, #ff8a80 0%, #ea4c89 100%)'
    ];
    
    // 基于用户名生成一致的颜色索引
    let hash = 0;
    for (let i = 0; i < username.length; i++) {
        const char = username.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // 转换为32位整数
    }
    
    return colors[Math.abs(hash) % colors.length];
}

// 显示通知函数
function showNotification(message, type = 'info') {
    // 这里可以使用Bootstrap的Toast或其他通知组件
    console.log(`[${type.toUpperCase()}] ${message}`);
}

// 初始化@提及功能
function initializeMentionFunctionality() {
    const $textarea = $('#commentContent');
    const $dropdown = $('#mentionDropdown');
    const $mentionList = $('#mentionList');
    
    // 监听输入事件
    $textarea.on('input', function(e) {
        handleMentionInput(e);
    });
    
    // 监听键盘事件
    $textarea.on('keydown', function(e) {
        handleMentionKeydown(e);
    });
    
    // 点击其他地方隐藏下拉框
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.mention-container').length) {
            hideMentionDropdown();
        }
    });
}

// 处理@提及输入
function handleMentionInput(e) {
    const textarea = e.target;
    const value = textarea.value;
    const cursorPos = textarea.selectionStart;
    
    // 查找最近的@符号位置
    let atPos = -1;
    for (let i = cursorPos - 1; i >= 0; i--) {
        if (value[i] === '@') {
            // 检查@前面是否是空格或行首
            if (i === 0 || /\s/.test(value[i - 1])) {
                atPos = i;
                break;
            }
        } else if (/\s/.test(value[i])) {
            // 遇到空格就停止
            break;
        }
    }
    
    if (atPos !== -1) {
        // 提取@后面的查询字符串
        const query = value.substring(atPos + 1, cursorPos);
        
        // 只在查询字符串中没有空格时显示下拉框
        if (!/\s/.test(query)) {
            currentMentionQuery = query;
            showMentionDropdown(query);
            return;
        }
    }
    
    // 隐藏下拉框
    hideMentionDropdown();
}

// 处理@提及键盘事件
function handleMentionKeydown(e) {
    if (!mentionDropdownVisible) return;
    
    const $items = $('#mentionList .mention-item');
    
    switch (e.key) {
        case 'ArrowDown':
            e.preventDefault();
            selectedMentionIndex = Math.min(selectedMentionIndex + 1, $items.length - 1);
            updateMentionSelection();
            break;
            
        case 'ArrowUp':
            e.preventDefault();
            selectedMentionIndex = Math.max(selectedMentionIndex - 1, 0);
            updateMentionSelection();
            break;
            
        case 'Enter':
        case 'Tab':
            e.preventDefault();
            if (selectedMentionIndex >= 0 && selectedMentionIndex < $items.length) {
                selectMentionUser(selectedMentionIndex);
            }
            break;
            
        case 'Escape':
            e.preventDefault();
            hideMentionDropdown();
            break;
    }
}

// 获取基于角色的图标和颜色
function getRoleIcon(role) {
    const roleMap = {
        '管理员': { icon: 'fas fa-crown', color: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)' },
        '工程师': { icon: 'fas fa-cogs', color: 'linear-gradient(135deg, #4834d4 0%, #686de0 100%)' },
        '项目经理': { icon: 'fas fa-project-diagram', color: 'linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%)' },
        '质量工程师': { icon: 'fas fa-clipboard-check', color: 'linear-gradient(135deg, #26de81 0%, #20bf6b 100%)' },
        '测试工程师': { icon: 'fas fa-bug', color: 'linear-gradient(135deg, #fed330 0%, #f39c12 100%)' },
        '系统管理员': { icon: 'fas fa-server', color: 'linear-gradient(135deg, #a55eea 0%, #8b5cf6 100%)' },
        '部门经理': { icon: 'fas fa-users', color: 'linear-gradient(135deg, #fd79a8 0%, #e84393 100%)' },
        '技术专家': { icon: 'fas fa-user-tie', color: 'linear-gradient(135deg, #00b894 0%, #00cec9 100%)' },
        '分析师': { icon: 'fas fa-chart-line', color: 'linear-gradient(135deg, #fdcb6e 0%, #f39c12 100%)' },
        '开发者': { icon: 'fas fa-code', color: 'linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%)' },
        'IT部门': { icon: 'fas fa-laptop-code', color: 'linear-gradient(135deg, #74b9ff 0%, #0984e3 100%)' }
    };
    
    // 优先匹配完整角色名
    if (roleMap[role]) {
        return roleMap[role];
    }
    
    // 模糊匹配
    const roleKeys = Object.keys(roleMap);
    for (let key of roleKeys) {
        if (role.includes(key) || key.includes(role)) {
            return roleMap[key];
        }
    }
    
    // 默认图标
    return { icon: 'fas fa-user', color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' };
}

// 计算@符号的位置并调整下拉框位置
function adjustMentionDropdownPosition() {
    const textarea = document.getElementById('commentContent');
    const dropdown = document.getElementById('mentionDropdown');
    
    if (!textarea || !dropdown) return;
    
    const textareaRect = textarea.getBoundingClientRect();
    const cursorPos = textarea.selectionStart;
    
    // 创建隐藏的span来测量@符号的位置
    const testSpan = document.createElement('span');
    testSpan.style.position = 'absolute';
    testSpan.style.visibility = 'hidden';
    testSpan.style.whiteSpace = 'pre';
    testSpan.style.font = window.getComputedStyle(textarea).font;
    testSpan.style.fontSize = window.getComputedStyle(textarea).fontSize;
    testSpan.style.fontFamily = window.getComputedStyle(textarea).fontFamily;
    testSpan.style.padding = window.getComputedStyle(textarea).padding;
    testSpan.style.border = window.getComputedStyle(textarea).border;
    testSpan.style.lineHeight = window.getComputedStyle(textarea).lineHeight;
    
    // 获取@符号前的文本
    const textBeforeCursor = textarea.value.substring(0, cursorPos);
    const lastAtIndex = textBeforeCursor.lastIndexOf('@');
    
    if (lastAtIndex !== -1) {
        const textBeforeAt = textBeforeCursor.substring(0, lastAtIndex);
        testSpan.textContent = textBeforeAt;
        
        document.body.appendChild(testSpan);
        const spanRect = testSpan.getBoundingClientRect();
        document.body.removeChild(testSpan);
        
        // 计算@符号的相对位置
        const atPositionX = spanRect.width + 10; // 加上一些偏移
        const atPositionY = 0;
        
        // 设置下拉框位置
        dropdown.style.left = `${atPositionX}px`;
        dropdown.style.top = `${atPositionY + 35}px`; // 相对于textarea的偏移
    }
}

// 显示@提及下拉框
function showMentionDropdown(query) {
    const filteredUsers = mentionUsers.filter(user => 
        user.username.toLowerCase().includes(query.toLowerCase()) ||
        user.role.toLowerCase().includes(query.toLowerCase()) ||
        (user.department && user.department.toLowerCase().includes(query.toLowerCase()))
    );
    
    if (filteredUsers.length === 0) {
        hideMentionDropdown();
        return;
    }
    
    const $mentionList = $('#mentionList');
    $mentionList.empty();
    
    filteredUsers.forEach((user, index) => {
        const roleInfo = getRoleIcon(user.role);
        
        const avatarHtml = user.avatar_url ? 
            `<img src="${user.avatar_url}" alt="${user.username}" class="mention-avatar" 
                  onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
             <div class="mention-avatar-fallback role-icon" style="display:none; background: ${roleInfo.color};">
                 <i class="${roleInfo.icon}"></i>
             </div>` :
            `<div class="mention-avatar-fallback role-icon" style="background: ${roleInfo.color};">
                 <i class="${roleInfo.icon}"></i>
             </div>`;
        
        const $item = $(`
            <div class="mention-item" data-index="${index}" data-user-id="${user.id}">
                <div class="mention-avatar-container">
                    ${avatarHtml}
                </div>
                <div class="mention-user-info">
                    <div class="mention-username">@${user.username}</div>
                    <div class="mention-role">${user.role}</div>
                    <div class="mention-department">${user.department || ''}</div>
                </div>
            </div>
        `);
        
        $item.on('click', function() {
            selectMentionUser(index);
        });
        
        $mentionList.append($item);
    });
    
    selectedMentionIndex = 0;
    updateMentionSelection();
    
    $('#mentionDropdown').removeClass('d-none');
    mentionDropdownVisible = true;
    
    // 调整下拉框位置
    adjustMentionDropdownPosition();
}

// 隐藏@提及下拉框
function hideMentionDropdown() {
    $('#mentionDropdown').addClass('d-none');
    mentionDropdownVisible = false;
    selectedMentionIndex = -1;
}

// 更新@提及选择状态
function updateMentionSelection() {
    $('#mentionList .mention-item').removeClass('active');
    if (selectedMentionIndex >= 0) {
        $(`#mentionList .mention-item[data-index="${selectedMentionIndex}"]`).addClass('active');
    }
}

// 选择@提及用户
function selectMentionUser(index) {
    const $textarea = $('#commentContent');
    const textarea = $textarea[0];
    const value = textarea.value;
    const cursorPos = textarea.selectionStart;
    
    // 查找@符号位置
    let atPos = -1;
    for (let i = cursorPos - 1; i >= 0; i--) {
        if (value[i] === '@') {
            if (i === 0 || /\s/.test(value[i - 1])) {
                atPos = i;
                break;
            }
        } else if (/\s/.test(value[i])) {
            break;
        }
    }
    
    if (atPos !== -1) {
        const filteredUsers = mentionUsers.filter(user => 
            user.username.toLowerCase().includes(currentMentionQuery.toLowerCase()) ||
            user.role.toLowerCase().includes(currentMentionQuery.toLowerCase()) ||
            (user.department && user.department.toLowerCase().includes(currentMentionQuery.toLowerCase()))
        );
        
        if (index < filteredUsers.length) {
            const user = filteredUsers[index];
            const mentionText = `@${user.username} `;
            
            // 替换文本
            const newValue = value.substring(0, atPos) + mentionText + value.substring(cursorPos);
            textarea.value = newValue;
            
            // 设置光标位置
            const newCursorPos = atPos + mentionText.length;
            textarea.setSelectionRange(newCursorPos, newCursorPos);
            
            // 触发input事件以便其他功能知道内容已更改
            $textarea.trigger('input');
        }
    }
    
    hideMentionDropdown();
    $textarea.focus();
}

// 处理评论内容，将@提及转换为带样式的标签
function processMentionInComment(content) {
    // 将@用户名转换为带样式的span标签
    return content.replace(/@(\w+)/g, '<span class="mention-tag" data-username="$1">@$1</span>');
}

// 为现有评论也应用@提及样式（页面加载时调用）
function processExistingComments() {
    $('.comment-content').each(function() {
        const $this = $(this);
        let content = $this.html();
        
        // 清理前导空格和缩进
        content = content.replace(/^[\s\u00A0]+/gm, ''); // 移除行首的空格和非断空格
        content = content.replace(/^\s+/g, ''); // 移除开头的空格
        content = content.trim(); // 移除首尾空格
        
        // 如果内容中包含@但还没有mention-tag类，则处理它
        if (content.includes('@') && !content.includes('mention-tag')) {
            content = processMentionInComment(content);
        }
        
        // 更新处理后的内容
        $this.html(content);
    });
}

// 页面加载完成后处理现有评论
$(document).ready(function() {
    processExistingComments();
    initializeProgressBars();
});

// 初始化进度条
function initializeProgressBars() {
    const currentStatus = '{{ issue.issue_status.value if issue.issue_status else "NO_MEETING_CONFIRMING" }}';
    
    // 已上线状态映射
    const onlineStates = {
        'PRE_ANLAUF': 1,
        'MEETING_ANALYZING': 2,
        'MEETING_ANALYSIS_COMPLETE': 3,
        'MEETING_MEASURES_FORMULATING': 4,
        'MEETING_MEASURES_IMPLEMENTING': 5,
        'MEETING_CONTINUE_TRACKING': 6
    };
    
    // 未上线状态映射
    const offlineStates = {
        'NO_MEETING_CONFIRMING': 1,
        'NO_MEETING_CONTINUE_TRACKING': 2,
        'NO_MEETING_INVALID_COMPLAINT': 3,
        'NO_MEETING_MEASURES_DEFINED': 4,
        'NO_MEETING_MEASURES_IMPLEMENTING': 5
    };
    
    // 更新已上线进度条
    if (onlineStates[currentStatus]) {
        updateProgressBar('online', onlineStates[currentStatus], 6);
    } else {
        updateProgressBar('online', 0, 6);
    }
    
    // 更新未上线进度条
    if (offlineStates[currentStatus]) {
        updateProgressBar('offline', offlineStates[currentStatus], 5);
    } else {
        updateProgressBar('offline', 0, 5);
    }
}

// 更新进度条状态
function updateProgressBar(type, currentStep, totalSteps, statusOverride = null) {
    const $steps = $(`#${type}-steps .step`);
    const $progressFill = $(`#${type}-progress-bar .progress-fill`);
    
    // 议题状态颜色映射
    const statusColors = {
        'PRE_ANLAUF': '#FCE4D6',
        'MEETING_ANALYZING': '#FF0000',
        'MEETING_ANALYSIS_COMPLETE': '#FFC000',
        'MEETING_MEASURES_FORMULATING': '#FFFF00',
        'MEETING_MEASURES_IMPLEMENTING': '#00B050',
        'MEETING_CONTINUE_TRACKING': '#00B0F0',
        'NO_MEETING_CONFIRMING': '#ED7D31',
        'NO_MEETING_CONTINUE_TRACKING': '#C7A1E3',
        'NO_MEETING_INVALID_COMPLAINT': '#808080',
        'NO_MEETING_MEASURES_DEFINED': '#66FFFF',
        'NO_MEETING_MEASURES_IMPLEMENTING': '#BF8F00'
    };
    
    // 为每个进度条类型定义步骤对应的状态
    const stepStatusMapping = {
        'online': [
            'PRE_ANLAUF',
            'MEETING_ANALYZING', 
            'MEETING_ANALYSIS_COMPLETE',
            'MEETING_MEASURES_FORMULATING',
            'MEETING_MEASURES_IMPLEMENTING',
            'MEETING_CONTINUE_TRACKING'
        ],
        'offline': [
            'NO_MEETING_CONFIRMING',
            'NO_MEETING_CONTINUE_TRACKING',
            'NO_MEETING_INVALID_COMPLAINT', 
            'NO_MEETING_MEASURES_DEFINED',
            'NO_MEETING_MEASURES_IMPLEMENTING'
        ]
    };
    
    // 使用传入的状态覆盖或默认模板状态
    const currentStatus = statusOverride || '{{ issue.issue_status.value if issue.issue_status else "NO_MEETING_CONFIRMING" }}';
    const stepStatuses = stepStatusMapping[type] || [];
    
    // 计算进度百分比
    const progressPercentage = currentStep > 0 ? ((currentStep - 1) / (totalSteps - 1)) * 100 : 0;
    
    // 获取当前议题状态的颜色（不使用渐变）
    const currentStepStatus = stepStatuses[currentStep - 1];
    const currentStepColor = statusColors[currentStepStatus] || '#e9ecef';
    
    // 更新进度条填充和颜色（使用当前状态的颜色，不使用渐变）
    $progressFill.css({
        'width': progressPercentage + '%',
        'background': currentStepColor
    });
    
    // 更新步骤状态
    $steps.each(function(index) {
        const stepNumber = index + 1;
        const $step = $(this);
        const $circle = $step.find('.step-circle');
        const $label = $step.find('.step-label');
        const stepStatus = stepStatuses[index];
        const stepColor = statusColors[stepStatus] || '#f8f9fa';
        
        $step.removeClass('completed current pending');
        
        if (stepNumber < currentStep) {
            $step.addClass('completed');
            $circle.css({
                'background': `linear-gradient(145deg, ${stepColor} 0%, ${stepColor}dd 50%, ${stepColor}bb 100%)`,
                'border': `2px solid ${stepColor}`,
                'color': stepColor
            });
            $label.css({
                'color': '#333333',
                'font-weight': '600'
            });
        } else if (stepNumber === currentStep) {
            $step.addClass('current');
            $circle.css({
                'background': `linear-gradient(145deg, ${stepColor} 0%, ${stepColor}dd 50%, ${stepColor}bb 100%)`,
                'border': `2px solid ${stepColor}`,
                'color': stepColor
            });
            $label.css({
                'color': '#333333',
                'font-weight': '600'
            });
        } else {
            $step.addClass('pending');
            $circle.css({
                'background': 'linear-gradient(145deg, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%)',
                'border': `2px solid ${stepColor}`,
                'color': stepColor
            });
            $label.css({
                'color': '#6c757d',
                'font-weight': '400'
            });
        }
    });
}

// 添加实时验证和字符计数

$(document).ready(function() {
    const $textarea = $('#commentContent');
    const maxLength = 1000; // 设置最大字符数
    
    // 添加字符计数器
    $textarea.after(`
        <div class="form-text d-flex justify-content-between mt-1">
            <span class="text-muted">输入@可提及其他用户</span>
            <span class="char-count text-muted">
                <span id="currentLength">0</span>/<span id="maxLength">${maxLength}</span>
            </span>
        </div>
    `);
    
    // 实时更新字符计数
    $textarea.on('input', function() {
        const currentLength = $(this).val().length;
        $('#currentLength').text(currentLength);
        
        // 超出限制时改变颜色
        if (currentLength > maxLength) {
            $('.char-count').removeClass('text-muted').addClass('text-danger');
        } else if (currentLength > maxLength * 0.9) {
            $('.char-count').removeClass('text-muted text-danger').addClass('text-warning');
        } else {
            $('.char-count').removeClass('text-warning text-danger').addClass('text-muted');
        }
    });
    
    // 表单提交处理 - 完整实现
    $('#addCommentForm').submit(function(e) {
        e.preventDefault();
        
        const content = $('#commentContent').val().trim();
        const isInternal = $('#isInternal').is(':checked');
        
        // 验证
        if (!content) {
            alert('请输入评论内容');
            $('#commentContent').focus();
            return false;
        }
        
        if (content.length > maxLength) {
            alert(`评论内容不能超过${maxLength}个字符`);
            $('#commentContent').focus();
            return false;
        }
        
        // 禁用提交按钮，防止重复提交
        const $submitBtn = $(this).find('button[type="submit"]');
        const originalText = $submitBtn.html();
        $submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>发布中...');
        
        // 发送AJAX请求
        $.ajax({
            url: `/issues/${ISSUE_ID}/comments`,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                content: content,
                is_internal: isInternal
            }),
            success: function(response) {
                console.log('服务器响应:', response);
                
                if (response.success) {
                    // 清空表单
                    $('#commentContent').val('');
                    $('#isInternal').prop('checked', false);
                    $('#currentLength').text('0');
                    $('.char-count').removeClass('text-warning text-danger').addClass('text-muted');
                    
                    // 动态添加新评论
                    if (response.comment) {
                        addCommentToList(response.comment);
                        updateCommentsCount();
                        showNotification('评论发布成功', 'success');
                    } else {
                        // 如果没有返回comment数据，刷新页面但保持在评论选项卡
                        saveActiveTab();
                        location.reload();
                    }
                } else {
                    alert('评论发布失败：' + (response.error || response.message || '未知错误'));
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX错误详情:', {
                    status: xhr.status,
                    statusText: xhr.statusText,
                    responseText: xhr.responseText,
                    error: error
                });
                
                let errorMessage = '评论发布失败';
                
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.error) {
                        errorMessage += '：' + response.error;
                    }
                } catch (e) {
                    switch (xhr.status) {
                        case 400:
                            errorMessage += '：请求数据格式错误';
                            break;
                        case 403:
                            errorMessage += '：没有权限添加评论';
                            break;
                        case 404:
                            errorMessage += '：议题不存在';
                            break;
                        case 500:
                            errorMessage += '：服务器内部错误';
                            break;
                        case 0:
                            errorMessage += '：网络连接失败';
                            break;
                        default:
                            errorMessage += '：' + xhr.statusText;
                    }
                }
                
                alert(errorMessage + '，请重试');
            },
            complete: function() {
                // 恢复提交按钮
                $submitBtn.prop('disabled', false).html(originalText);
            }
        });
    });
});

// 动态添加评论到列表
function addCommentToList(commentData) {
    const $commentsList = $('#commentsList');
    
    // 移除"暂无评论"提示（如果存在）
    $commentsList.find('.no-comments').remove();
    
    // 清理评论内容，确保左对齐
    let cleanedContent = commentData.content;
    // 移除前导空格和缩进
    cleanedContent = cleanedContent.replace(/^[\s\u00A0]+/gm, ''); // 移除行首的空格和非断空格
    cleanedContent = cleanedContent.replace(/^\s+/g, ''); // 移除开头的空格
    cleanedContent = cleanedContent.trim(); // 移除首尾空格
    
    // 处理评论内容中的@提及
    const processedContent = processMentionInComment(cleanedContent);
    
    // 创建新评论HTML
    const $newComment = $(`
        <div class="comment-item" data-comment-id="${commentData.id}">
            <div class="comment-header d-flex justify-content-between align-items-start mb-2">
                <div class="comment-author-info">
                    <div class="comment-author">
                        <i class="fas fa-user-circle me-2"></i>
                        <strong>${commentData.author}</strong>
                        ${commentData.is_internal ? '<span class="badge bg-warning text-dark ms-2">内部</span>' : ''}
                        <span class="badge bg-success ms-2">刚刚发布</span>
                    </div>
                    <div class="comment-time text-muted small">
                        <i class="fas fa-clock me-1"></i>
                        ${commentData.created_at}
                    </div>
                </div>
                <div class="comment-actions">
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" 
                                data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="editComment(${commentData.id})">
                                <i class="fas fa-edit me-2"></i>编辑</a></li>
                            <li><a class="dropdown-item text-danger" href="#" onclick="deleteComment(${commentData.id})">
                                <i class="fas fa-trash me-2"></i>删除</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="comment-content" id="comment-content-${commentData.id}">
                ${processedContent}
            </div>
            <!-- 编辑表单（隐藏） -->
            <div class="comment-edit-form d-none" id="comment-edit-${commentData.id}">
                <div class="mb-3">
                    <textarea class="form-control" rows="3" id="comment-edit-content-${commentData.id}">${commentData.content}</textarea>
                </div>
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="comment-edit-internal-${commentData.id}" 
                           ${commentData.is_internal ? 'checked' : ''}>
                    <label class="form-check-label" for="comment-edit-internal-${commentData.id}">
                        内部评论（仅内部人员可见）
                    </label>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-primary btn-sm" onclick="saveCommentEdit(${commentData.id})">
                        <i class="fas fa-save me-1"></i>保存
                    </button>
                    <button class="btn btn-secondary btn-sm" onclick="cancelCommentEdit(${commentData.id})">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                </div>
            </div>
        </div>
    `);
    
    // 添加到评论列表末尾
    $commentsList.append($newComment);
    
    // 对新添加的评论内容进行清理，确保左对齐
    const $newCommentContent = $newComment.find('.comment-content');
    let newContent = $newCommentContent.html();
    // 清理前导空格和缩进
    newContent = newContent.replace(/^[\s\u00A0]+/gm, ''); // 移除行首的空格和非断空格
    newContent = newContent.replace(/^\s+/g, ''); // 移除开头的空格
    newContent = newContent.trim(); // 移除首尾空格
    $newCommentContent.html(newContent);
    
    // 滚动到新评论
    $newComment[0].scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    
    // 3秒后移除"刚刚发布"标签
    setTimeout(() => {
        $newComment.find('.badge.bg-success').fadeOut(300, function() {
            $(this).remove();
        });
    }, 3000);
}

// 更新评论数显示
function updateCommentsCount() {
    const currentCount = parseInt($('#comments-count').text()) || 0;
    $('#comments-count').text(currentCount + 1);
}

// 显示通知消息
function showNotification(message, type = 'info') {
    const alertClass = type === 'success' ? 'alert-success' : 
                      type === 'error' ? 'alert-danger' : 'alert-info';
    
    const $notification = $(`
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            <i class="fas ${type === 'success' ? 'fa-check' : type === 'error' ? 'fa-exclamation-triangle' : 'fa-info'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);
    
    $('body').append($notification);
    
    // 3秒后自动消失
    setTimeout(() => {
        $notification.alert('close');
    }, 3000);
}

// 保存和恢复选项卡状态
function saveActiveTab() {
    const activeTab = $('#issueDetailTabs .nav-link.active').attr('data-bs-target');
    if (activeTab) {
        localStorage.setItem('issueDetailActiveTab', activeTab.replace('#', ''));
    }
}

function restoreActiveTab() {
    const savedTab = localStorage.getItem('issueDetailActiveTab');
    if (savedTab) {
        $('#issueDetailTabs .nav-link').removeClass('active');
        $('.tab-pane').removeClass('show active');
        
        $(`#issueDetailTabs button[data-bs-target="#${savedTab}"]`).addClass('active');
        $(`#${savedTab}`).addClass('show active');
    }
}

// 🔥 新增函数：更新页面顶部的议题状态显示
function updateTopIssueStatus(statusValue, statusLabel, colors) {
    // 找到顶部状态显示区域
    const topStatusContainer = $('.card-body .row .col-md-3').first();
    
    if (topStatusContainer.length > 0) {
        // 创建新的状态HTML
        const newStatusHtml = `
            <strong>议题状态：</strong>
            <span class="status-badge-enhanced" 
                  style="background-color: ${colors.bg}; color: ${colors.text};">
                <i class="fas fa-flag"></i>
                ${statusLabel}
            </span>
        `;
        
        // 更新顶部状态显示
        topStatusContainer.html(newStatusHtml);
        
        // 添加更新动画效果
        topStatusContainer.find('.status-badge-enhanced').addClass('status-updated');
        setTimeout(() => {
            topStatusContainer.find('.status-badge-enhanced').removeClass('status-updated');
        }, 2000);
    }
    
    // 🔥 新增：同时更新进度条显示
    updateProgressBarsWithStatus(statusValue);
}

// 🔥 新增函数：根据状态更新进度条
function updateProgressBarsWithStatus(statusValue) {
    // 已上线状态映射
    const onlineStates = {
        'PRE_ANLAUF': 1,
        'MEETING_ANALYZING': 2,
        'MEETING_ANALYSIS_COMPLETE': 3,
        'MEETING_MEASURES_FORMULATING': 4,
        'MEETING_MEASURES_IMPLEMENTING': 5,
        'MEETING_CONTINUE_TRACKING': 6
    };
    
    // 未上线状态映射
    const offlineStates = {
        'NO_MEETING_CONFIRMING': 1,
        'NO_MEETING_CONTINUE_TRACKING': 2,
        'NO_MEETING_INVALID_COMPLAINT': 3,
        'NO_MEETING_MEASURES_DEFINED': 4,
        'NO_MEETING_MEASURES_IMPLEMENTING': 5
    };
    
    // 更新已上线进度条
    if (onlineStates[statusValue]) {
        updateProgressBar('online', onlineStates[statusValue], 6, statusValue);
    } else {
        updateProgressBar('online', 0, 6, statusValue);
    }
    
    // 更新未上线进度条
    if (offlineStates[statusValue]) {
        updateProgressBar('offline', offlineStates[statusValue], 5, statusValue);
    } else {
        updateProgressBar('offline', 0, 5, statusValue);
    }
}

// 🔥 新增函数：计算天数差
function calculateDaysSinceReport(dateString) {
    if (!dateString || dateString === '未设置') {
        return 0;
    }
    
    try {
        const reportDate = new Date(dateString);
        const today = new Date();
        
        // 设置时间为当天开始，避免时间差影响
        today.setHours(0, 0, 0, 0);
        reportDate.setHours(0, 0, 0, 0);
        
        const timeDiff = today.getTime() - reportDate.getTime();
        const daysDiff = Math.floor(timeDiff / (1000 * 3600 * 24));
        
        return Math.max(0, daysDiff); // 确保返回非负数
    } catch (e) {
        console.error('日期解析错误:', e);
        return 0;
    }
}

// 🔥 新增函数：更新天数显示（圆形图标）
function updateDaysSinceReport(days) {
    const circleContainer = document.getElementById('days-since-report-circle');
    if (!circleContainer) return;
    
    // 更新数字
    const numberElement = circleContainer.querySelector('.circular-number');
    const labelElement = circleContainer.querySelector('.circular-label');
    
    if (numberElement && labelElement) {
        numberElement.textContent = days;
        
        if (days === 0) {
            labelElement.textContent = '今天';
        } else if (days === 1) {
            labelElement.textContent = '天前';
        } else {
            labelElement.textContent = '天前';
        }
    }
    
    // 根据天数设置不同颜色
    circleContainer.classList.remove('days-today', 'days-recent', 'days-old');
    
    if (days === 0) {
        circleContainer.classList.add('days-today');
    } else if (days <= 3) {
        circleContainer.classList.add('days-recent');
    } else {
        circleContainer.classList.add('days-old');
    }
    
    // 添加更新动画效果
    circleContainer.classList.add('updated');
    setTimeout(() => {
        circleContainer.classList.remove('updated');
    }, 1000);
}

// 🔥 新增函数：初始化圆形图标颜色
function initializeCircularIcons() {
    // 初始化上报天数圆形图标颜色
    const daysSinceReportCircle = document.getElementById('days-since-report-circle');
    if (daysSinceReportCircle) {
        const daysNumber = parseInt(daysSinceReportCircle.querySelector('.circular-number').textContent);
        
        daysSinceReportCircle.classList.remove('days-today', 'days-recent', 'days-old');
        
        if (daysNumber === 0) {
            daysSinceReportCircle.classList.add('days-today');
        } else if (daysNumber <= 3) {
            daysSinceReportCircle.classList.add('days-recent');
        } else {
            daysSinceReportCircle.classList.add('days-old');
        }
    }
    
    // 初始化字段更新圆形图标颜色
    const updatesCountCircle = document.getElementById('updates-count-circle');
    if (updatesCountCircle) {
        const updatesNumber = parseInt(updatesCountCircle.querySelector('.circular-number').textContent);
        
        updatesCountCircle.classList.remove('updates-has-updates', 'updates-no-updates');
        
        if (updatesNumber > 0) {
            updatesCountCircle.classList.add('updates-has-updates');
        } else {
            updatesCountCircle.classList.add('updates-no-updates');
        }
    }
}

// 🔥 新增功能：监听问题上报日期变化
$(document).ready(function() {
    // 初始化圆形图标颜色
    initializeCircularIcons();
    
    // 初始化自定义信息面板
    initializeCustomInfoPanel();
    
    // 监听问题上报日期字段的变化
    $(document).on('blur', '[data-field="problem_report_date"]', function() {
        const dateValue = $(this).text().trim();
        const days = calculateDaysSinceReport(dateValue);
        updateDaysSinceReport(days);
    });
    
    // 监听日期编辑器的变化（如果使用了日期选择器）
    $(document).on('change', '[data-field="problem_report_date"] input', function() {
        const dateValue = $(this).val();
        const days = calculateDaysSinceReport(dateValue);
        updateDaysSinceReport(days);
    });
});

// 🔥 自定义信息面板功能
let selectedTagColor = '#007bff';

function initializeCustomInfoPanel() {
    // 初始化颜色选择器
    $('.color-picker-btn').click(function() {
        $('.color-picker-btn').removeClass('selected');
        $(this).addClass('selected');
        selectedTagColor = $(this).data('color');
    });
    
    // 默认选择第一个颜色
    $('.color-picker-btn').first().addClass('selected');
    
    // 初始化文件上传区域
    initializeFileUpload();
    
    // 加载标签建议
    loadTagSuggestions();
    
    // 加载常用标签
    loadPopularTags();
    
    // 加载附件列表
    loadAttachmentsList();
}

function loadTagSuggestions() {
    $.get('/api/tags/suggestions', function(data) {
        const datalist = $('#tag-suggestions');
        datalist.empty();
        data.forEach(tag => {
            datalist.append(`<option value="${tag.name}">${tag.name}</option>`);
        });
    });
}

function loadPopularTags() {
    $.get('/api/tags/popular', function(data) {
        const container = $('#popular-tags');
        container.empty();
        data.forEach(tag => {
            const tagElement = $(`
                <span class="badge tag-suggestion" 
                      style="background-color: ${tag.color}; color: white; cursor: pointer;"
                      onclick="selectPopularTag('${tag.name}', '${tag.color}')">
                    ${tag.name}
                </span>
            `);
            container.append(tagElement);
        });
    });
}

function selectPopularTag(tagName, tagColor) {
    $('#new-tag-input').val(tagName);
    // 找到对应颜色的按钮并选中
    $('.color-picker-btn').each(function() {
        if ($(this).data('color') === tagColor) {
            $('.color-picker-btn').removeClass('selected');
            $(this).addClass('selected');
            selectedTagColor = tagColor;
        }
    });
}

function addTag() {
    const tagName = $('#new-tag-input').val().trim();
    if (!tagName) {
        showNotification('请输入标签名称', 'error');
        return;
    }
    
    // 检查是否已经存在
    const existingTag = $(`#current-tags [data-tag-id]:contains('${tagName}')`);
    if (existingTag.length > 0) {
        showNotification('该标签已存在', 'warning');
        return;
    }
    
    // 显示加载状态
    const $addBtn = $('#add-tag-btn');
    const originalText = $addBtn.html();
    $addBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 添加中...');
    
    // 发送请求添加标签
    $.post('/api/issues/{{ issue.id }}/tags', {
        name: tagName,
        color: selectedTagColor
    })
    .done(function(response) {
        if (response.success) {
            // 添加标签到界面
            const tagElement = $(`
                <span class="badge" style="background-color: ${response.tag.color}; color: white;" 
                      data-tag-id="${response.tag.id}">
                    ${response.tag.name}
                    <button type="button" class="btn-close btn-close-white btn-sm ms-1" 
                            onclick="removeTag(${response.tag.id})" aria-label="删除标签"></button>
                </span>
            `);
            $('#current-tags').append(tagElement);
            $('#no-tags-message').hide();
            $('#new-tag-input').val('');
            showNotification('标签添加成功', 'success');
        } else {
            showNotification('添加标签失败：' + (response.message || '未知错误'), 'error');
        }
    })
    .fail(function(xhr) {
        let errorMessage = '网络错误，请重试';
        try {
            const response = JSON.parse(xhr.responseText);
            if (response.message) {
                errorMessage = response.message;
            }
        } catch (e) {
            if (xhr.status === 401) {
                errorMessage = '请重新登录';
            } else if (xhr.status === 403) {
                errorMessage = '没有权限执行此操作';
            } else if (xhr.status === 500) {
                errorMessage = '服务器错误，请稍后重试';
            }
        }
        showNotification(errorMessage, 'error');
    })
    .always(function() {
        // 恢复按钮状态
        $addBtn.prop('disabled', false).html(originalText);
    });
}

function removeTag(tagId) {
    if (!confirm('确定要删除这个标签吗？')) {
        return;
    }
    
    // 找到标签元素并显示加载状态
    const $tagElement = $(`[data-tag-id="${tagId}"]`);
    const originalHtml = $tagElement.html();
    $tagElement.html('<i class="fas fa-spinner fa-spin"></i> 删除中...');
    
    $.ajax({
        url: '/api/issues/{{ issue.id }}/tags/' + tagId,
        type: 'DELETE',
        success: function(response) {
            if (response.success) {
                $tagElement.remove();
                // 检查是否还有标签
                if ($('#current-tags .badge').length === 0) {
                    $('#no-tags-message').show();
                }
                showNotification('标签删除成功', 'success');
            } else {
                $tagElement.html(originalHtml);
                showNotification('删除标签失败：' + (response.message || '未知错误'), 'error');
            }
        },
        error: function(xhr) {
            $tagElement.html(originalHtml);
            
            let errorMessage = '网络错误，请重试';
            try {
                const response = JSON.parse(xhr.responseText);
                if (response.message) {
                    errorMessage = response.message;
                }
            } catch (e) {
                if (xhr.status === 401) {
                    errorMessage = '请重新登录';
                } else if (xhr.status === 403) {
                    errorMessage = '没有权限删除标签';
                } else if (xhr.status === 500) {
                    errorMessage = '服务器错误，请稍后重试';
                }
            }
            showNotification(errorMessage, 'error');
        }
    });
}

function initializeFileUpload() {
    const uploadArea = $('#upload-area');
    const fileInput = $('#file-input');
    
    // 文件选择事件处理（移除重复的事件监听器）
    fileInput.on('change', function(e) {
        const files = e.target.files;
        if (files.length > 0) {
            uploadFiles(files);
        }
    });
    
    // 点击上传区域触发文件选择
    uploadArea.click(function() {
        fileInput.click();
    });
    
    // 拖拽上传
    uploadArea.on('dragover', function(e) {
        e.preventDefault();
        $(this).addClass('dragover');
    });
    
    uploadArea.on('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
    });
    
    uploadArea.on('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
        const files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            uploadFiles(files);
        }
    });
}

function uploadFiles(files) {
    const formData = new FormData();
    
    // 计算总文件大小
    let totalSize = 0;
    for (let i = 0; i < files.length; i++) {
        formData.append('files', files[i]);
        totalSize += files[i].size;
    }
    
    formData.append('issue_id', '{{ issue.id }}');
    
    // 显示上传进度和文件信息
    const uploadProgress = $('#upload-progress');
    const progressBar = uploadProgress.find('.progress-bar');
    const progressText = uploadProgress.find('.progress-text');
    
    // 显示上传信息
    uploadProgress.show();
    progressBar.css('width', '0%').addClass('progress-bar-animated progress-bar-striped bg-primary');
    progressText.text(`正在上传 ${files.length} 个文件...`);
    
    // 记录上传开始时间，确保最小显示时间
    const uploadStartTime = Date.now();
    const minDisplayTime = 1000; // 最小显示1秒
    
    $.ajax({
        url: '/api/issues/{{ issue.id }}/attachments',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        xhr: function() {
            const xhr = new window.XMLHttpRequest();
            xhr.upload.addEventListener('progress', function(e) {
                if (e.lengthComputable) {
                    const percentComplete = (e.loaded / e.total) * 100;
                    progressBar.css('width', percentComplete + '%');
                    
                    // 更新进度文本
                    const uploadedMB = (e.loaded / 1024 / 1024).toFixed(1);
                    const totalMB = (e.total / 1024 / 1024).toFixed(1);
                    progressText.text(
                        `上传中... ${percentComplete.toFixed(0)}% (${uploadedMB}MB / ${totalMB}MB)`
                    );
                }
            });
            return xhr;
        },
        success: function(response) {
            // 确保进度条达到100%
            progressBar.css('width', '100%').removeClass('progress-bar-animated progress-bar-striped').addClass('bg-success');
            uploadProgress.find('.progress-text').text('上传完成！');
            
            // 计算已经过去的时间
            const elapsedTime = Date.now() - uploadStartTime;
            const remainingTime = Math.max(0, minDisplayTime - elapsedTime);
            
            setTimeout(() => {
                uploadProgress.hide();
                progressBar.css('width', '0%').removeClass('bg-success bg-primary');
                uploadProgress.find('.progress-text').text('');
            }, remainingTime + 800); // 额外显示800ms让用户看到"完成"状态
            
            if (response.success) {
                showNotification('文件上传成功', 'success');
                // 清空文件输入框
                $('#file-input').val('');
                // 刷新附件列表
                setTimeout(() => {
                    loadAttachmentsList();
                }, 300);
            } else {
                showNotification('文件上传失败：' + (response.message || '未知错误'), 'error');
            }
        },
        error: function(xhr) {
            // 显示错误状态
            progressBar.removeClass('progress-bar-animated progress-bar-striped').addClass('bg-danger');
            uploadProgress.find('.progress-text').text('上传失败！');
            
            // 计算已经过去的时间
            const elapsedTime = Date.now() - uploadStartTime;
            const remainingTime = Math.max(0, minDisplayTime - elapsedTime);
            
            setTimeout(() => {
                uploadProgress.hide();
                progressBar.css('width', '0%').removeClass('bg-danger bg-primary');
                uploadProgress.find('.progress-text').text('');
            }, remainingTime + 1500); // 错误信息显示更长时间
            
            let errorMessage = '网络错误，请重试';
            try {
                const response = JSON.parse(xhr.responseText);
                if (response.message) {
                    errorMessage = response.message;
                }
            } catch (e) {
                if (xhr.status === 401) {
                    errorMessage = '请重新登录';
                } else if (xhr.status === 403) {
                    errorMessage = '没有权限上传文件';
                } else if (xhr.status === 413) {
                    errorMessage = '文件过大，请选择更小的文件';
                } else if (xhr.status === 500) {
                    errorMessage = '服务器错误，请稍后重试';
                }
            }
            showNotification(errorMessage, 'error');
        }
    });
}

function loadAttachmentsList() {
    console.log('开始加载附件列表...');
    
    $.get('/api/issues/{{ issue.id }}/attachments')
        .done(function(data) {
            console.log('API返回数据:', data);
            const container = $('#attachments-list');
            container.empty();
            
            if (!data || data.length === 0) {
                container.html('<p class="text-muted">暂无附件</p>');
                console.log('没有附件数据');
                return;
            }
            
            console.log(`找到 ${data.length} 个附件`);
            
            data.forEach((attachment, index) => {
                console.log(`处理附件 ${index + 1}:`, attachment);
                
                const attachmentElement = $(`
                    <div class="attachment-item d-flex align-items-center mb-2">
                        <div class="attachment-icon text-primary me-3">
                            <i class="${attachment.file_icon || 'fas fa-file'}"></i>
                        </div>
                        <div class="attachment-info flex-grow-1">
                            <div class="attachment-name fw-bold">${attachment.original_filename || '未知文件'}</div>
                            <div class="attachment-meta text-muted small">
                                ${attachment.file_size_formatted || '未知大小'} • ${attachment.uploader || '未知用户'} • ${attachment.upload_time || '未知时间'}
                            </div>
                        </div>
                        <div class="attachment-actions">
                            ${attachment.can_preview ? 
                                `<button class="btn btn-sm btn-outline-primary me-1" onclick="previewAttachment(${attachment.id})" title="预览">
                                    <i class="fas fa-eye"></i>
                                </button>` : ''}
                            <button class="btn btn-sm btn-outline-success me-1" onclick="downloadAttachment(${attachment.id})" title="下载">
                                <i class="fas fa-download"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteAttachment(${attachment.id})" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `);
                container.append(attachmentElement);
            });
            
            console.log('附件列表加载完成');
        })
        .fail(function(xhr, status, error) {
            console.error('加载附件列表失败:', {
                status: xhr.status,
                statusText: xhr.statusText,
                error: error,
                responseText: xhr.responseText
            });
            
            const container = $('#attachments-list');
            container.html(`
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    加载附件列表失败，请刷新页面重试
                </div>
            `);
            
            let errorMessage = '加载附件列表失败';
            if (xhr.status === 401) {
                errorMessage = '请重新登录';
            } else if (xhr.status === 403) {
                errorMessage = '没有权限查看附件';
            } else if (xhr.status === 500) {
                errorMessage = '服务器错误，请稍后重试';
            }
            
            showNotification(errorMessage, 'error');
        });
}

function previewAttachment(attachmentId) {
    console.log('预览附件:', attachmentId);
    
    // 首先检查附件是否存在和可预览
    $.get(`/api/attachments/${attachmentId}/preview`)
        .done(function() {
            // 如果成功，在新窗口打开预览
            window.open('/api/attachments/' + attachmentId + '/preview', '_blank');
        })
        .fail(function(xhr) {
            console.error('预览附件失败:', xhr);
            let errorMessage = '预览附件失败';
            
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            } else if (xhr.status === 404) {
                errorMessage = '附件不存在或文件已损坏';
            } else if (xhr.status === 400) {
                errorMessage = '该文件类型不支持预览';
            } else if (xhr.status === 401) {
                errorMessage = '请重新登录';
            }
            
            showNotification(errorMessage, 'error');
        });
}

function downloadAttachment(attachmentId) {
    window.location.href = '/api/attachments/' + attachmentId + '/download';
}

function deleteAttachment(attachmentId) {
    if (!confirm('确定要删除这个附件吗？')) {
        return;
    }
    
    $.ajax({
        url: '/api/attachments/' + attachmentId,
        type: 'DELETE',
        success: function(response) {
            if (response.success) {
                loadAttachmentsList(); // 刷新附件列表
            } else {
                alert('删除附件失败：' + response.message);
            }
        },
        error: function() {
            alert('网络错误，请重试');
        }
    });
}

// 支持Enter键添加标签
$('#new-tag-input').keypress(function(e) {
    if (e.which === 13) { // Enter键
        addTag();
    }
});
</script>
{% endblock %}
                                        