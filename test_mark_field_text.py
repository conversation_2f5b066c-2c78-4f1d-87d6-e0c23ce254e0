#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
标记字段文本类型测试脚本
测试标记字段从整数类型改为文本类型后的功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.issue import Issue
from app.models.user import User
from config.development import DevelopmentConfig

def test_mark_field_text():
    """测试标记字段的文本功能"""

    # 创建应用实例
    app = create_app('development')
    
    with app.app_context():
        print("开始测试标记字段文本功能...")

        # 1. 测试现有数据
        print("\n测试现有数据...")
        existing_issues = Issue.query.filter(Issue.mark.isnot(None)).limit(5).all()
        
        if existing_issues:
            print(f"找到 {len(existing_issues)} 个有标记的议题:")
            for issue in existing_issues:
                print(f"  - 议题 {issue.id}: 标记 = '{issue.mark}' (类型: {type(issue.mark).__name__})")
        else:
            print("  没有找到有标记的议题")
        
        # 2. 测试创建新的文本标记
        print("\n测试创建新的文本标记...")
        
        # 获取第一个议题进行测试
        test_issue = Issue.query.first()
        if test_issue:
            # 保存原始标记
            original_mark = test_issue.mark
            
            # 测试不同类型的文本标记
            test_marks = [
                "重要",
                "P1-紧急", 
                "Bug-修复中",
                "V2.1-测试",
                "已处理",
                "123-文本数字"
            ]
            
            for mark in test_marks:
                try:
                    test_issue.mark = mark
                    db.session.commit()
                    
                    # 重新查询验证
                    updated_issue = Issue.query.get(test_issue.id)
                    if updated_issue.mark == mark:
                        print(f"  成功保存文本标记: '{mark}'")
                    else:
                        print(f"  保存失败: 期望 '{mark}', 实际 '{updated_issue.mark}'")
                        
                except Exception as e:
                    print(f"  保存标记 '{mark}' 时出错: {e}")

            # 恢复原始标记
            test_issue.mark = original_mark
            db.session.commit()
            print(f"  已恢复原始标记: '{original_mark}'")
            
        else:
            print("  没有找到可测试的议题")
        
        # 3. 测试长度限制
        print("\n测试长度限制...")
        if test_issue:
            # 测试100字符的标记
            long_mark = "A" * 100
            very_long_mark = "B" * 150  # 超过限制
            
            try:
                test_issue.mark = long_mark
                db.session.commit()
                print(f"  100字符标记保存成功")

                test_issue.mark = very_long_mark
                db.session.commit()

                # 检查是否被截断
                updated_issue = Issue.query.get(test_issue.id)
                if len(updated_issue.mark) <= 100:
                    print(f"  超长标记被正确处理 (长度: {len(updated_issue.mark)})")
                else:
                    print(f"  超长标记未被截断 (长度: {len(updated_issue.mark)})")

            except Exception as e:
                print(f"  长度测试出错: {e}")
            
            # 恢复原始标记
            test_issue.mark = original_mark
            db.session.commit()
        
        # 4. 测试空值处理
        print("\n测试空值处理...")
        if test_issue:
            try:
                test_issue.mark = None
                db.session.commit()
                
                updated_issue = Issue.query.get(test_issue.id)
                if updated_issue.mark is None:
                    print("  空值处理正确")
                else:
                    print(f"  空值处理异常: {updated_issue.mark}")

                # 恢复原始标记
                test_issue.mark = original_mark
                db.session.commit()

            except Exception as e:
                print(f"  空值测试出错: {e}")

        print("\n标记字段文本功能测试完成!")

if __name__ == '__main__':
    test_mark_field_text()
