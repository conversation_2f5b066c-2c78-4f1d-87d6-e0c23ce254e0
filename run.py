#!/usr/bin/env python3
"""
Profi-Anlauf Issues Management System 应用启动脚本
主要用于开发环境的应用启动
"""

import os
from dotenv import load_dotenv

# 加载环境变量文件
load_dotenv('env')  # 加载项目根目录下的env文件

from app import create_app, db
from app.models import Issue, User, Project, Comment, Tag
from flask_migrate import upgrade

# 设置环境变量
os.environ.setdefault('FLASK_ENV', 'development')

def deploy():
    """部署应用程序"""
    # 创建应用实例
    app = create_app()
    
    with app.app_context():
        # 创建数据库表
        db.create_all()
        
        # 执行数据库迁移
        upgrade()
        
        # 创建系统标签
        from app.models.tag import create_system_tags
        create_system_tags()
        
        # 创建默认管理员用户（如果不存在）
        create_default_admin()


def create_default_admin():
    """创建默认管理员用户"""
    from app.models.enums import UserRole
    
    admin_username = os.environ.get('ADMIN_USERNAME', 'admin')
    admin_email = os.environ.get('ADMIN_EMAIL', '<EMAIL>')
    admin_password = os.environ.get('ADMIN_PASSWORD', 'admin123')
    
    # 检查管理员是否已存在
    admin = User.query.filter_by(username=admin_username).first()
    if admin is None:
        admin = User(
            username=admin_username,
            email=admin_email,
            full_name='系统管理员',
            role=UserRole.ADMIN,
            is_admin=True,
            department='IT部门',
            position='系统管理员'
        )
        admin.set_password(admin_password)
        db.session.add(admin)
        db.session.commit()
        print(f'创建默认管理员用户: {admin_username}')


# 创建应用实例
app = create_app()

if __name__ == '__main__':
    # 创建数据库表
    with app.app_context():
        db.create_all()
        print("数据库表已创建")
    
    # 启动开发服务器
    print("启动Profi-Anlauf Issues Management System...")
    
    # 尝试不同的端口，避免端口冲突
    import socket
    
    def is_port_available(host, port):
        """检查端口是否可用"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(1)
                result = s.connect_ex((host, port))
                return result != 0
        except:
            return False
    
    # 尝试的端口列表
    hosts_to_try = ['**************','*************', '127.0.0.1', '0.0.0.0']
    ports_to_try = [9999, 5000, 5001, 5002, 5003, 8000, 8080]
    
    app_started = False
    
    for host in hosts_to_try:
        for port in ports_to_try:
            if is_port_available(host, port):
                try:
                    print(f"尝试在 {host}:{port} 启动服务器...")
                    print(f"访问地址: http://{host}:{port}")
                    print("默认管理员账户: admin / admin123")
                    print("按 Ctrl+C 停止服务器")
                    
                    app.run(
                        host=host,
                        port=port,
                        debug=True,
                        threaded=True,
                        use_reloader=False  # 避免重载问题
                    )
                    app_started = True
                    break
                except OSError as e:
                    print(f"在 {host}:{port} 启动失败: {e}")
                    continue
            else:
                print(f"端口 {host}:{port} 已被占用")
        
        if app_started:
            break
    
    if not app_started:
        print("无法启动服务器，请检查网络配置或手动指定端口")
        print("你可以尝试以下命令:")
        print("python run.py")
        print("或者检查是否有其他Flask进程在运行")
        
        # 显示可能占用端口的进程（Windows）
        import subprocess
        try:
            result = subprocess.run(['netstat', '-an'], capture_output=True, text=True)
            print("\n当前网络连接状态:")
            for line in result.stdout.split('\n'):
                if ':5000' in line or ':5001' in line:
                    print(line)
        except:
            pass 