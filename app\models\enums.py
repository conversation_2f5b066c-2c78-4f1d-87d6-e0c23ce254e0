"""
枚举定义模块
定义系统中使用的所有枚举类型
"""

from enum import Enum


class IssueStatus(Enum):
    """议题状态枚举 - 带背景颜色"""
    PRE_ANLAUF = "PRE_ANLAUF"  # 已上Pre-Anlauf
    MEETING_ANALYZING = "MEETING_ANALYZING"  # 已上会，分析中
    MEETING_ANALYSIS_COMPLETE = "MEETING_ANALYSIS_COMPLETE"  # 已上会，分析完成
    MEETING_MEASURES_FORMULATING = "MEETING_MEASURES_FORMULATING"  # 已上会，措施制定
    MEETING_MEASURES_IMPLEMENTING = "MEETING_MEASURES_IMPLEMENTING"  # 已上会，措施落实
    MEETING_CONTINUE_TRACKING = "MEETING_CONTINUE_TRACKING"  # 已上会，继续跟踪
    NO_MEETING_CONFIRMING = "NO_MEETING_CONFIRMING"  # 未上会，确认中
    NO_MEETING_CONTINUE_TRACKING = "NO_MEETING_CONTINUE_TRACKING"  # 未上会，继续跟踪
    NO_MEETING_INVALID_COMPLAINT = "NO_MEETING_INVALID_COMPLAINT"  # 未上会，无效抱怨
    NO_MEETING_MEASURES_DEFINED = "NO_MEETING_MEASURES_DEFINED"  # 未上会，已定义措施
    NO_MEETING_MEASURES_IMPLEMENTING = "NO_MEETING_MEASURES_IMPLEMENTING"  # 未上会，措施实施
    
    def __str__(self):
        return self.value

    @property
    def chinese_label(self):
        """返回中文标签，用于前端显示"""
        chinese_labels = {
            'PRE_ANLAUF': '已上Pre-Anlauf',
            'MEETING_ANALYZING': '已上会，分析中',
            'MEETING_ANALYSIS_COMPLETE': '已上会，分析完成',
            'MEETING_MEASURES_FORMULATING': '已上会，措施制定',
            'MEETING_MEASURES_IMPLEMENTING': '已上会，措施落实',
            'MEETING_CONTINUE_TRACKING': '已上会，继续跟踪',
            'NO_MEETING_CONFIRMING': '未上会，确认中',
            'NO_MEETING_CONTINUE_TRACKING': '未上会，继续跟踪',
            'NO_MEETING_INVALID_COMPLAINT': '未上会，无效抱怨',
            'NO_MEETING_MEASURES_DEFINED': '未上会，已定义措施',
            'NO_MEETING_MEASURES_IMPLEMENTING': '未上会，措施实施',
        }
        return chinese_labels.get(self.value, self.value)

    @property
    def background_color(self):
        """返回背景颜色"""
        color_mapping = {
            'PRE_ANLAUF': '#FCE4D6',
            'MEETING_ANALYZING': '#FF0000',
            'MEETING_ANALYSIS_COMPLETE': '#FFC000',
            'MEETING_MEASURES_FORMULATING': '#FFFF00',
            'MEETING_MEASURES_IMPLEMENTING': '#00B050',
            'MEETING_CONTINUE_TRACKING': '#00B0F0',
            'NO_MEETING_CONFIRMING': '#ED7D31',
            'NO_MEETING_CONTINUE_TRACKING': '#C7A1E3',
            'NO_MEETING_INVALID_COMPLAINT': '#808080',
            'NO_MEETING_MEASURES_DEFINED': '#66FFFF',
            'NO_MEETING_MEASURES_IMPLEMENTING': '#BF8F00',
        }
        return color_mapping.get(self.value, '#808080')

    @property
    def text_color(self):
        """返回文字颜色，根据背景色自动选择"""
        dark_backgrounds = ['#FF0000', '#00B050', '#00B0F0', '#808080', '#BF8F00']
        bg_color = self.background_color
        return '#ffffff' if bg_color in dark_backgrounds else '#333333'


class ComplaintCategory(Enum):
    """抱怨类别枚举"""
    PDI = "PDI"
    AFTER_SALES = "AFTER_SALES"  # 售后
    
    def __str__(self):
        return self.value
    
    @property
    def chinese_label(self):
        """返回中文标签"""
        chinese_labels = {
            'PDI': 'PDI',
            'AFTER_SALES': '售后',
        }
        return chinese_labels.get(self.value, self.value)


class VehicleStatus(Enum):
    """车辆状态枚举"""
    PRE_SALE = "PRE_SALE"  # 售前车
    TEST_DRIVE = "TEST_DRIVE"  # 试驾车
    DISPLAY = "DISPLAY"  # 展车
    COMMERCIAL = "COMMERCIAL"  # 商品车
    CUSTOMER_CAR = "客户车"  # 客户车 - 兼容旧数据

    def __str__(self):
        return self.value

    @property
    def chinese_label(self):
        """返回中文标签"""
        chinese_labels = {
            'PRE_SALE': '售前车',
            'TEST_DRIVE': '试驾车',
            'DISPLAY': '展车',
            'COMMERCIAL': '商品车',
            '客户车': '客户车',  # 直接映射旧数据
        }
        return chinese_labels.get(self.value, self.value)


class Priority(Enum):
    """优先级枚举"""
    HIGH = "高"
    MEDIUM = "中"
    LOW = "低"
    URGENT = "紧急"
    
    def __str__(self):
        return self.value


class Severity(Enum):
    """严重度枚举"""
    LEVEL_1 = "1"
    LEVEL_2 = "2"
    LEVEL_3 = "3"
    LEVEL_4 = "4"
    LEVEL_5 = "5"
    
    def __str__(self):
        return self.value


class IssueType(Enum):
    """议题类型枚举"""
    BUG = "缺陷"
    FEATURE = "新功能"
    IMPROVEMENT = "改进"
    TASK = "任务"
    
    def __str__(self):
        return self.value


class MeetingModule(Enum):
    """会议模块枚举"""
    SOFTWARE = "软件"
    HARDWARE = "硬件"
    SYSTEM = "系统"
    INTEGRATION = "集成"
    TESTING = "测试"
    
    def __str__(self):
        return self.value


class ReviewStatus(Enum):
    """审核状态枚举"""
    PENDING = "待审核"
    APPROVED = "已通过"
    REJECTED = "已拒绝"
    RETURNED = "已退回"
    
    def __str__(self):
        return self.value


class UserRole(Enum):
    """用户角色枚举"""
    ADMIN = "管理员"
    PROJECT_MANAGER = "项目负责人"
    USER = "用户"
    # 保留原有角色，向后兼容
    MANAGER = "经理"
    ENGINEER = "工程师"
    OPERATOR = "操作员"
    VIEWER = "查看者"
    
    def __str__(self):
        return self.value


class UserStatus(Enum):
    """用户状态枚举"""
    PENDING = "待审核"
    ACTIVE = "已激活"
    INACTIVE = "已禁用"
    REJECTED = "已拒绝"
    
    def __str__(self):
        return self.value


class MaintenanceStatus(Enum):
    """维护状态枚举（快速操作）"""
    PENDING = "待维护"
    COMPLETED = "已维护"
    ON_HOLD = "挂起"
    
    def __str__(self):
        return self.value
    
    @property
    def chinese_label(self):
        """返回中文标签"""
        chinese_labels = {
            'PENDING': '待维护',
            'COMPLETED': '已维护',
            'ON_HOLD': '挂起',
        }
        return chinese_labels.get(self.name, self.value) 