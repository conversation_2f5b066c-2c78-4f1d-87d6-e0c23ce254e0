#!/usr/bin/env python
"""
检查导入的议题状态
验证议题状态是否正确导入和显示
"""

import os
import sys
from collections import Counter

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import Issue
from app.models.enums import IssueStatus

def check_imported_issue_status():
    """检查导入的议题状态"""
    print("检查导入的议题状态...")
    
    app = create_app()
    with app.app_context():
        try:
            # 获取最近导入的议题（按ID倒序）
            recent_issues = Issue.query.order_by(Issue.id.desc()).limit(20).all()
            
            print(f"\n找到 {len(recent_issues)} 个最近的议题")
            
            # 统计状态分布
            status_counter = Counter()
            raw_status_counter = Counter()
            
            print("\n最近导入的议题状态详情:")
            for issue in recent_issues:
                raw_status = issue._issue_status  # 原始存储值
                converted_status = issue.issue_status  # 转换后的枚举值
                
                raw_status_counter[raw_status] += 1
                if converted_status:
                    status_counter[converted_status.value] += 1
                    chinese_label = converted_status.chinese_label
                else:
                    status_counter['None'] += 1
                    chinese_label = 'None'
                
                print(f"  议题 {issue.anlauf_number}: 原始='{raw_status}' -> 转换='{converted_status.value if converted_status else 'None'}' ({chinese_label})")
            
            print(f"\n原始状态值统计:")
            for status, count in raw_status_counter.most_common():
                print(f"  '{status}': {count} 次")
            
            print(f"\n转换后状态统计:")
            for status, count in status_counter.most_common():
                if status != 'None':
                    try:
                        enum_status = IssueStatus(status)
                        print(f"  {status} ({enum_status.chinese_label}): {count} 次")
                    except ValueError:
                        print(f"  {status} (未知状态): {count} 次")
                else:
                    print(f"  None (转换失败): {count} 次")
            
            return True
            
        except Exception as e:
            print(f"检查失败: {e}")
            return False

def check_all_issue_status():
    """检查所有议题的状态分布"""
    print("\n检查所有议题的状态分布...")
    
    app = create_app()
    with app.app_context():
        try:
            # 获取所有议题
            all_issues = Issue.query.all()
            
            print(f"总共 {len(all_issues)} 个议题")
            
            # 统计状态分布
            status_counter = Counter()
            raw_status_counter = Counter()
            
            for issue in all_issues:
                raw_status = issue._issue_status
                converted_status = issue.issue_status
                
                raw_status_counter[raw_status] += 1
                if converted_status:
                    status_counter[converted_status.value] += 1
                else:
                    status_counter['None'] += 1
            
            print(f"\n所有议题的原始状态值统计:")
            for status, count in raw_status_counter.most_common():
                print(f"  '{status}': {count} 次")
            
            print(f"\n所有议题的转换后状态统计:")
            for status, count in status_counter.most_common():
                if status != 'None':
                    try:
                        enum_status = IssueStatus(status)
                        print(f"  {status} ({enum_status.chinese_label}): {count} 次")
                    except ValueError:
                        print(f"  {status} (未知状态): {count} 次")
                else:
                    print(f"  None (转换失败): {count} 次")
            
            return True
            
        except Exception as e:
            print(f"检查失败: {e}")
            return False

def test_specific_status_values():
    """测试特定的状态值"""
    print("\n测试特定状态值的转换...")
    
    app = create_app()
    with app.app_context():
        try:
            # 查找包含特定状态值的议题
            test_statuses = [
                '已上会，措施落实',
                '未上会，确认中',
                '已上会，分析中',
                '已上会，继续跟踪',
                '未上会，措施实施'
            ]
            
            for test_status in test_statuses:
                issues = Issue.query.filter(Issue._issue_status == test_status).limit(3).all()
                print(f"\n状态 '{test_status}' 的议题:")
                
                if issues:
                    for issue in issues:
                        converted = issue.issue_status
                        if converted:
                            print(f"  议题 {issue.anlauf_number}: {converted.value} ({converted.chinese_label})")
                        else:
                            print(f"  议题 {issue.anlauf_number}: 转换失败")
                else:
                    print(f"  没有找到状态为 '{test_status}' 的议题")
            
            return True
            
        except Exception as e:
            print(f"测试失败: {e}")
            return False

def main():
    """主函数"""
    print("议题状态导入检查")
    print("=" * 50)
    
    # 检查最近导入的议题
    recent_check = check_imported_issue_status()
    
    # 检查所有议题状态分布
    all_check = check_all_issue_status()
    
    # 测试特定状态值
    specific_check = test_specific_status_values()
    
    # 总结
    print("\n检查结果总结:")
    print(f"  最近议题检查: {'通过' if recent_check else '失败'}")
    print(f"  全部议题检查: {'通过' if all_check else '失败'}")
    print(f"  特定状态测试: {'通过' if specific_check else '失败'}")
    
    if recent_check and all_check and specific_check:
        print("\n所有检查通过！")
        return True
    else:
        print("\n部分检查失败，请查看详细信息。")
        return False

if __name__ == '__main__':
    main()
