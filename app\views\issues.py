"""
议题管理视图控制器 (修复版本)
包含议题的增删改查、状态管理、评论等核心功能
"""

from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash, current_app, session, send_file, Response
from datetime import datetime, timedelta
import pandas as pd
import io
from app import db
from app.models import Issue, Comment, Tag, Project, User, ProjectAssignment
from app.models.enums import (
    IssueStatus, UserRole, MaintenanceStatus, ComplaintCategory, 
    VehicleStatus, Priority, Severity, IssueType, ReviewStatus, MeetingModule, UserStatus
)
from app.utils.timezone_utils import beijing_now
from app.utils.email_utils import send_batch_emails_via_outlook, generate_issue_reporter_notification_email_body, generate_engineer_notification_email_body
from sqlalchemy import func, desc, asc, and_, or_, text, case, select, nulls_last

issues = Blueprint('issues', __name__)


def get_current_user():
    """获取当前登录用户"""
    current_user_id = session.get('user_id')
    if not current_user_id:
        return None
    return User.query.get(current_user_id)


def get_user_accessible_issues(current_user):
    """
    获取用户可访问的议题查询对象（根据用户角色进行权限控制）
    
    Args:
        current_user (User): 当前用户对象
        
    Returns:
        Query: 过滤后的议题查询对象
    """
    # 基础查询
    query = Issue.query
    
    # 根据用户角色进行权限控制
    if current_user.role == UserRole.ADMIN or current_user.is_admin:
        # 管理员可以看到所有议题
        pass
    elif current_user.role == UserRole.PROJECT_MANAGER:
        # 🔥 修改：项目负责人权限控制 - 查看项目分配的项目编号和工程师字段等于自己用户名的记录
        assigned_projects = ProjectAssignment.get_user_projects(current_user.id, active_only=True)
        if assigned_projects:
            # 可以查看：1. 分配的项目编号的议题  2. 工程师字段等于自己用户名的议题
            query = query.filter(
                db.or_(
                    Issue.project_number.in_(assigned_projects),  # 分配的项目议题
                    Issue.engineer == current_user.username       # 工程师字段等于自己用户名
                )
            )
        else:
            # 没有激活的项目分配，降级为普通用户权限
            query = query.filter(Issue.engineer == current_user.username)
    elif current_user.role == UserRole.MANAGER:
        # 🔥 修改：经理权限控制 - 查看部门字段等于自己部门的记录
        if current_user.department:
            query = query.filter(Issue.department == current_user.department)
        else:
            # 如果经理没有部门信息，降级为普通用户权限
            query = query.filter(Issue.engineer == current_user.username)
    else:
        # 🔥 修改：普通用户权限控制 - 查看工程师字段等于自己用户名的记录
        query = query.filter(Issue.engineer == current_user.username)
    
    return query


def login_required(f):
    """登录验证装饰器"""
    from functools import wraps
    @wraps(f)
    def decorated_function(*args, **kwargs):
        current_user = get_current_user()
        if not current_user:
            flash('请先登录', 'error')
            return redirect(url_for('user.login'))
        if current_user.status != UserStatus.ACTIVE:
            flash('您的账户尚未激活或已被禁用', 'error')
            return redirect(url_for('user.login'))
        return f(*args, **kwargs)
    return decorated_function


@issues.route('/')
@login_required
def list_issues():
    """议题列表页面"""
    current_user = get_current_user()
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config.get('ISSUES_PER_PAGE', 20)
    
    # 获取过滤参数（支持多选）
    status_filter = request.args.getlist('status')  # 议题状态（多选）
    maintenance_status_filter = request.args.getlist('maintenance_status')  # 维护状态（多选）
    search_query = request.args.get('q', '').strip()  # 关键字搜索
    project_number_filter = request.args.getlist('project_number')  # 项目编号筛选（多选）
    department_filter = request.args.getlist('department')  # 部门筛选（多选）
    engineer_filter = request.args.getlist('engineer')  # 工程师筛选（多选）
    complaint_category_filter = request.args.getlist('complaint_category')  # 抱怨类别筛选（多选）
    vehicle_status_filter = request.args.getlist('vehicle_status')  # 车辆状态筛选（多选）
    breakdown_filter = request.args.getlist('breakdown')  # 抛锚筛选（多选）
    problem_in_meeting_filter = request.args.getlist('problem_in_meeting')  # 问题是否上会筛选（多选）
    complaint_in_meeting_filter = request.args.getlist('complaint_in_meeting')  # 抱怨是否上会筛选（多选）
    kdnr_filter = request.args.getlist('kdnr')  # KDNR筛选（多选）
    banr_filter = request.args.getlist('banr')  # BANR筛选（多选）
    created_date_from = request.args.get('created_date_from', '').strip()  # 创建日期从
    created_date_to = request.args.get('created_date_to', '').strip()  # 创建日期到
    problem_report_week_filter = request.args.get('problem_report_week', '').strip()  # 问题上报周次筛选
    has_updates_filter = request.args.getlist('has_updates')  # 更新状态筛选（多选）
    
    # 🔄 新增：已上线/未上线议题筛选（来自仪表板链接）
    online_status_filter = request.args.get('online_status', '').strip()  # 上线状态筛选

    # 构建基础查询（根据用户权限）
    query = get_user_accessible_issues(current_user)
    
    # 🔄 新增：处理已上线/未上线议题筛选
    if online_status_filter:
        if online_status_filter == 'true':
            # 已上线议题：状态中文标签包含"已上"的状态
            online_statuses = [status for status in IssueStatus if "已上" in status.chinese_label]
            # 🔄 修复：使用实际的数据库字段 _issue_status 和状态值列表
            online_status_values = []
            for status in online_statuses:
                online_status_values.extend([status.value, status.chinese_label])
            query = query.filter(Issue._issue_status.in_(online_status_values))
        elif online_status_filter == 'false':
            # 未上线议题：状态中文标签包含"未上"的状态  
            offline_statuses = [status for status in IssueStatus if "未上" in status.chinese_label]
            # 🔄 修复：使用实际的数据库字段 _issue_status 和状态值列表
            offline_status_values = []
            for status in offline_statuses:
                offline_status_values.extend([status.value, status.chinese_label])
            query = query.filter(Issue._issue_status.in_(offline_status_values))
    
    # 应用筛选条件（支持多选）
    if status_filter:
        status_conditions = []
        for status_name in status_filter:
            try:
                status_enum = getattr(IssueStatus, status_name)
                status_conditions.append(Issue._issue_status == status_enum.value)
                status_conditions.append(Issue._issue_status == status_enum.chinese_label)
            except AttributeError:
                pass
        if status_conditions:
            query = query.filter(db.or_(*status_conditions))
    
    if maintenance_status_filter:
        maintenance_conditions = []
        for maintenance_status_name in maintenance_status_filter:
            try:
                maintenance_status_enum = getattr(MaintenanceStatus, maintenance_status_name)
                maintenance_conditions.append(Issue.maintenance_status == maintenance_status_enum)
            except AttributeError:
                pass
        if maintenance_conditions:
            query = query.filter(db.or_(*maintenance_conditions))
    
    if project_number_filter:
        query = query.filter(Issue.project_number.in_(project_number_filter))
    
    if department_filter and (current_user.role in [UserRole.ADMIN, UserRole.MANAGER] or current_user.is_admin):
        # 通过department字段或handler字段匹配部门（与列表显示逻辑一致）
        department_conditions = []
        for dept in department_filter:
            department_conditions.append(Issue.department == dept)
            department_conditions.append(db.and_(
                db.or_(Issue.department.is_(None), Issue.department == ''),
                Issue.handler == dept
            ))
        if department_conditions:
            query = query.filter(db.or_(*department_conditions))
    
    if engineer_filter:
        query = query.filter(Issue.engineer.in_(engineer_filter))
    
    if complaint_category_filter:
        category_conditions = []
        for category_name in complaint_category_filter:
            try:
                category_enum = getattr(ComplaintCategory, category_name)
                category_conditions.append(Issue.complaint_category == category_enum)
            except AttributeError:
                pass
        if category_conditions:
            query = query.filter(db.or_(*category_conditions))
    
    if vehicle_status_filter:
        vehicle_conditions = []
        for vehicle_status_name in vehicle_status_filter:
            try:
                vehicle_status_enum = getattr(VehicleStatus, vehicle_status_name)
                vehicle_conditions.append(Issue.vehicle_status == vehicle_status_enum)
            except AttributeError:
                pass
        if vehicle_conditions:
            query = query.filter(db.or_(*vehicle_conditions))
    
    if breakdown_filter:
        breakdown_conditions = []
        if 'true' in breakdown_filter:
            breakdown_conditions.append(Issue.breakdown == True)
        if 'false' in breakdown_filter:
            breakdown_conditions.append(Issue.breakdown == False)
        if breakdown_conditions:
            query = query.filter(db.or_(*breakdown_conditions))
    
    if problem_in_meeting_filter:
        problem_meeting_conditions = []
        if 'true' in problem_in_meeting_filter:
            problem_meeting_conditions.append(Issue.problem_in_meeting == True)
        if 'false' in problem_in_meeting_filter:
            problem_meeting_conditions.append(Issue.problem_in_meeting == False)
        if problem_meeting_conditions:
            query = query.filter(db.or_(*problem_meeting_conditions))
    
    if complaint_in_meeting_filter:
        complaint_meeting_conditions = []
        if 'true' in complaint_in_meeting_filter:
            complaint_meeting_conditions.append(Issue.complaint_in_meeting == True)
        if 'false' in complaint_in_meeting_filter:
            complaint_meeting_conditions.append(Issue.complaint_in_meeting == False)
        if complaint_meeting_conditions:
            query = query.filter(db.or_(*complaint_meeting_conditions))
    
    # KDNR筛选
    if kdnr_filter:
        query = query.filter(Issue.kdnr.in_(kdnr_filter))
    
    # BANR筛选（替代严重度筛选）
    if banr_filter:
        query = query.filter(Issue.banr.in_(banr_filter))
    
    # 日期范围筛选
    if created_date_from:
        try:
            from datetime import datetime
            date_from = datetime.strptime(created_date_from, '%Y-%m-%d').date()
            query = query.filter(func.date(Issue.created_at) >= date_from)
        except ValueError:
            pass
    
    if created_date_to:
        try:
            from datetime import datetime
            date_to = datetime.strptime(created_date_to, '%Y-%m-%d').date()
            query = query.filter(func.date(Issue.created_at) <= date_to)
        except ValueError:
            pass
    
    if problem_report_week_filter:
        try:
            week_num = int(problem_report_week_filter)
            query = query.filter(Issue.problem_report_week == week_num)
        except ValueError:
            pass
    
    # 🔄 新增：处理has_updates_filter的多选筛选
    if has_updates_filter:
        has_updates_conditions = []
        if 'true' in has_updates_filter:
            has_updates_conditions.append(Issue.has_updates == True)
        if 'false' in has_updates_filter:
            has_updates_conditions.append(Issue.has_updates == False)
        if has_updates_conditions:
            query = query.filter(db.or_(*has_updates_conditions))
    
    # 关键字搜索（放在最后）
    if search_query:
        search_pattern = f'%{search_query}%'
        query = query.filter(
            db.or_(
                Issue.anlauf_number.like(search_pattern),
                Issue.project_number.like(search_pattern),
                Issue.problem_description.like(search_pattern),
                Issue.engineer.like(search_pattern),
                Issue.kdnr.like(search_pattern),
                Issue.banr.like(search_pattern)
            )
        )
    
    # 排序：优先按序号升序排列，序号为空的排在最后，然后按创建时间倒序
    query = query.order_by(
        Issue.sequence_number.asc().nulls_last(),
        Issue.created_at.desc()
    )
    
    # 执行分页查询
    pagination = query.paginate(
        page=page, 
        per_page=per_page, 
        error_out=False
    )
    
    # 获取过滤选项数据
    filter_options = {
        'statuses': [(s.name, s.chinese_label) for s in IssueStatus],
        'maintenance_statuses': [(ms.name, ms.chinese_label) for ms in MaintenanceStatus],
        'unique_project_numbers': [item[0] for item in db.session.query(Issue.project_number).distinct().filter(
            Issue.project_number.isnot(None), 
            Issue.project_number != ''
        ).all()],
        # 🔄 修改：从实际议题中获取部门数据
        'unique_departments': list(set([
            item[0] for item in db.session.query(Issue.department).distinct().filter(
                Issue.department.isnot(None), 
                Issue.department != ''
            ).all()
        ] + [
            item[0] for item in db.session.query(Issue.handler).distinct().filter(
                Issue.handler.isnot(None), 
                Issue.handler != ''
            ).all()
        ])),
        'unique_engineers': [item[0] for item in db.session.query(Issue.engineer).distinct().filter(
            Issue.engineer.isnot(None), 
            Issue.engineer != ''
        ).all()],
        'unique_kdnrs': [item[0] for item in db.session.query(Issue.kdnr).distinct().filter(
            Issue.kdnr.isnot(None), 
            Issue.kdnr != ''
        ).all()],
        'unique_banrs': [item[0] for item in db.session.query(Issue.banr).distinct().filter(
            Issue.banr.isnot(None), 
            Issue.banr != ''
        ).all()],  # 新增BANR选项
        'complaint_categories': ComplaintCategory,
        'vehicle_statuses': VehicleStatus,
    }

    # 当前筛选条件（用于模板回显）
    current_filters = {
        'status': status_filter,
        'maintenance_status': maintenance_status_filter,
        'q': search_query,
        'project_number': project_number_filter,
        'department': department_filter,
        'engineer': engineer_filter,
        'complaint_category': complaint_category_filter,
        'vehicle_status': vehicle_status_filter,
        'breakdown': breakdown_filter,
        'problem_in_meeting': problem_in_meeting_filter,
        'complaint_in_meeting': complaint_in_meeting_filter,
        'kdnr': kdnr_filter,  # 新增KDNR筛选条件
        'banr': banr_filter,  # 新增BANR筛选条件
        'created_date_from': created_date_from,
        'created_date_to': created_date_to,
        'problem_report_week': problem_report_week_filter,
        'has_updates': has_updates_filter,
        'online_status': online_status_filter,  # 🔄 新增：记录上线状态筛选
    }
    
    return render_template('issues/list.html',
                         issues=pagination.items,
                         pagination=pagination,
                         filter_options=filter_options,
                         current_filters=current_filters,
                         current_user=current_user,
                         # 添加模板需要的枚举变量
                         issue_statuses=list(IssueStatus),
                         maintenance_statuses=list(MaintenanceStatus),
                         complaint_categories=list(ComplaintCategory),
                         vehicle_statuses=list(VehicleStatus),
                         # 添加AI配置
                         ai_provider=current_app.config.get('AI_PROVIDER', 'local'),
                         ai_enabled=current_app.config.get('AI_ENABLED', True))


@issues.route('/<int:issue_id>')
@login_required
def view_issue(issue_id):
    """查看议题详情"""
    current_user = get_current_user()
    issue = Issue.query.get_or_404(issue_id)
    
    # 获取有效的议题评论（过滤掉已删除的）
    comments = Comment.query.filter_by(
        issue_id=issue_id,
        is_deleted=False  # 只获取未删除的评论
    ).order_by(Comment.created_at.asc()).all()
    
    # 计算问题上报天数和显示信息
    days_since_report = 0
    report_info = {
        'days': 0,
        'label': '天前创建',
        'has_report_date': False
    }
    
    from datetime import date
    today = date.today()
    
    if issue.created_at:
        created_date = issue.created_at.date()
        days_since_report = (today - created_date).days
        report_info.update({
            'days': days_since_report,
            'label': '天前创建',
            'has_report_date': False
        })
    
    # 处理特殊情况
    if days_since_report < 0:
        report_info.update({
            'days': 0,
            'label': '日期异常'
        })
    elif days_since_report == 0:
        report_info['label'] = '今天创建'
    elif days_since_report == 1:
        report_info['label'] = '昨天创建'
    
    # 计算创建天数
    days_ago = 0
    if issue.created_at:
        from datetime import datetime
        days_ago = (datetime.now().date() - issue.created_at.date()).days
    
    # 获取议题统计信息，使用默认值以防出现异常
    try:
        field_completion_stats = issue.get_field_completion_stats()
    except Exception as e:
        field_completion_stats = {
            'filled_count': 0,
            'total_count': 0,
            'completion_percentage': 0
        }
    
    try:
        updated_fields_stats = issue.get_updated_fields_stats()
    except Exception as e:
        updated_fields_stats = {
            'updated_count': 0,
            'updated_fields': [],
            'has_updates': False,
            'last_update_time': None
        }
    
    try:
        days_since_report_new = issue.get_days_since_report()
    except Exception as e:
        days_since_report_new = 0
    
    # 获取字段编辑权限
    field_permissions = get_field_edit_permissions(current_user, issue)

    return render_template('issues/detail.html',
                         issue=issue,
                         comments=comments,
                         current_user=current_user,
                         days_since_report=days_since_report,
                         report_info=report_info,
                         days_ago=days_ago,
                         field_completion_stats=field_completion_stats,
                         updated_fields_stats=updated_fields_stats,
                         days_since_report_new=days_since_report_new,
                         field_permissions=field_permissions)


@issues.route('/create', methods=['GET', 'POST'])
@login_required
def create_issue():
    """创建新议题"""
    current_user = get_current_user()
    
    if request.method == 'GET':
        projects = Project.query.filter_by(archived=False).all()
        users = User.query.filter_by(is_active=True, status=UserStatus.ACTIVE).all()
        
        # 创建新议题时，默认所有字段都可编辑
        field_permissions = {field: True for field in [
            'project_number', 'sequence_number', 'anlauf_number', 'created_at',
            'problem_report_week', 'review_status', 'engineer_first_review_time',
            'engineer_first_review_week', 'vin', 'complaint_category',
            'kdnr', 'banr', 'breakdown', 'after_sales_fault_description',
            'mileage', 'zp8_delivery_date', 'department', 'engineer',
            'analysis_measures', 'warehouse_received_fault_parts',
            'repair_station_info', 'problem_report_date', 'vehicle_status',
            'is_rework_vehicle', 'issue_status', 'problem_description',
            'meeting_topic_name', 'mark', 'fault_parts_status', 'problem_type',
            'problem_in_meeting', 'complaint_in_meeting'
        ]}

        return render_template('issues/create.html',
                             projects=projects,
                             users=users,
                             current_user=current_user,
                             statuses=list(IssueStatus),
                             priorities=list(Priority),
                             field_permissions=field_permissions,
                             severities=list(Severity),
                             issue_types=list(IssueType),
                             vehicle_statuses=list(VehicleStatus),
                             meeting_modules=list(MeetingModule),
                             review_statuses=list(ReviewStatus),
                             complaint_categories=list(ComplaintCategory))
    
    try:
        # 验证必填字段
        required_fields = ['key_field']
        missing_fields = [field for field in required_fields if not request.form.get(field)]
        
        if missing_fields:
            flash(f'请填写所有必填字段: {", ".join(missing_fields)}', 'error')
            return redirect(url_for('issues.create_issue'))
        
        # 处理时间字段
        from datetime import datetime
        current_time = beijing_now()
        
        # 解析用户输入的时间字段
        created_at = current_time
        updated_at = current_time
        status_last_changed_time = current_time
        
        if request.form.get('created_at'):
            try:
                created_at = datetime.strptime(request.form['created_at'], '%Y-%m-%dT%H:%M')
            except ValueError:
                pass
                
        if request.form.get('updated_at'):
            try:
                updated_at = datetime.strptime(request.form['updated_at'], '%Y-%m-%dT%H:%M')
            except ValueError:
                pass
                
        if request.form.get('status_last_changed_time'):
            try:
                status_last_changed_time = datetime.strptime(request.form['status_last_changed_time'], '%Y-%m-%dT%H:%M')
            except ValueError:
                pass
        
        # 创建新议题
        issue = Issue(
            # 基础标识字段
            key_field=request.form['key_field'],
            summary=request.form.get('summary', ''),
            status=request.form.get('status', ''),  # 状态字段（文本字符串）
            
            # 基本信息
            project=request.form.get('project', ''),
            test_platform=request.form.get('test_platform', ''),
            test_category=request.form.get('test_category', ''),
            vehicle_vin=request.form.get('vehicle_vin', ''),
            
            # 人员信息
            handler=request.form.get('handler', ''),
            reporter=request.form.get('reporter', current_user.username),
            solution_responsible_person=request.form.get('solution_responsible_person', ''),
            
            # 问题描述
            problem_analysis=request.form.get('problem_analysis', ''),
            problem_remarks=request.form.get('problem_remarks', ''),
            
            # 状态信息
            issue_status=IssueStatus.NO_MEETING_CONFIRMING,
            maintenance_status=MaintenanceStatus.PENDING,
            severity=request.form.get('severity', '1'),
            
            # 时间信息
            created_at=created_at,
            updated_at=updated_at,
            status_last_changed_time=status_last_changed_time
        )
        
        db.session.add(issue)
        db.session.commit()
        
        flash('议题创建成功', 'success')
        return redirect(url_for('issues.view_issue', issue_id=issue.id))
        
    except Exception as e:
        db.session.rollback()
        flash(f'创建议题时发生错误: {str(e)}', 'error')
        return redirect(url_for('issues.create_issue'))


@issues.route('/<int:issue_id>/maintenance-status', methods=['POST'])
@login_required
def update_maintenance_status(issue_id):
    """更新议题维护状态"""
    current_user = get_current_user()
    issue = Issue.query.get_or_404(issue_id)
    
    try:
        data = request.get_json()
        if not data or 'status' not in data:
            return jsonify({'success': False, 'error': '缺少状态参数'}), 400
            
        new_status_name = data['status']
        new_status = getattr(MaintenanceStatus, new_status_name)
        
        old_status = issue.maintenance_status
        issue.maintenance_status = new_status
        issue.updated_at = beijing_now()
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'维护状态已更新为 {new_status.value}',
            'new_status': new_status.value
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': f'更新维护状态失败: {str(e)}'
        }), 400


def get_field_edit_permissions(user, issue):
    """
    获取用户对议题字段的编辑权限

    Args:
        user: 当前用户对象
        issue: 议题对象

    Returns:
        dict: 字段权限字典，key为字段名，value为是否可编辑
    """
    # 所有角色都不能编辑的字段
    readonly_fields = {
        'project_number',           # 项目编号
        'sequence_number',          # 序号
        'anlauf_number',           # Anlauf编号
        'created_at',              # 创建日期
        'vin',                     # VIN
        'mileage',                 # 里程
        'repair_station_info',     # 维修站号+中文名
        'after_sales_fault_description',  # 售后故障描述
        'review_status',           # 审核状态
        'engineer_first_review_time',      # 工程师首次审核时间
        'engineer_first_review_week',      # 工程师首次审核周次
        'zp8_delivery_date',       # ZP8报交日期
    }

    # 只能项目负责人和管理员编辑的字段
    project_manager_only_fields = {
        'problem_report_week',     # 问题上报周次
        'problem_report_date',     # 问题上报日期
        'issue_status',            # 议题状态
        'problem_in_meeting',      # 问题是否上会
        'complaint_in_meeting',    # 抱怨是否上会
    }

    # 项目负责人不能编辑的字段（除了只读字段外）
    project_manager_restricted_fields = {
        'department',              # 部门
        'engineer',                # 工程师
        'vehicle_status',          # 车辆状态
        'is_rework_vehicle',       # 是否返工车辆
        'kdnr',                    # KDNR
        'banr',                    # BANR
        'breakdown',               # 抛锚
        'problem_description',     # 问题描述
        'analysis_measures',       # 分析/措施
        'problem_type',            # 问题类型
        'fault_parts_status',      # 故障件状态
        'warehouse_received_fault_parts',  # 仓库收到故障件
        'mark',                    # 标记
        'meeting_topic_name',      # 上会议题名
    }

    # 检查用户是否为项目负责人
    is_project_manager = (user.role == UserRole.PROJECT_MANAGER and
                         user.is_assigned_to_project(issue.project_number))

    # 检查用户是否为管理员
    is_admin = user.is_admin or user.role == UserRole.ADMIN

    # 所有字段列表
    all_fields = {
        # 基本信息
        'project_number', 'sequence_number', 'anlauf_number', 'created_at',
        'problem_report_week', 'problem_report_date', 'department', 'engineer',

        # 车辆信息
        'vin', 'vehicle_status', 'mileage', 'is_rework_vehicle', 'kdnr', 'banr',
        'breakdown', 'repair_station_info',

        # 问题信息
        'complaint_category', 'issue_status', 'problem_description',
        'after_sales_fault_description', 'analysis_measures', 'problem_type',
        'fault_parts_status', 'warehouse_received_fault_parts', 'mark',

        # 流程信息
        'review_status', 'engineer_first_review_time', 'engineer_first_review_week',
        'zp8_delivery_date', 'meeting_topic_name', 'problem_in_meeting',
        'complaint_in_meeting',

        # 兼容性字段
        'severity', 'handler', 'reporter', 'project', 'key_field', 'vehicle_vin'
    }

    permissions = {}

    for field in all_fields:
        # 管理员可以编辑所有字段（除了只读字段）
        if is_admin:
            permissions[field] = field not in readonly_fields
        # 只读字段，所有人都不能编辑
        elif field in readonly_fields:
            permissions[field] = False
        # 项目负责人专属字段
        elif field in project_manager_only_fields:
            permissions[field] = is_project_manager
        # 项目负责人受限字段（项目负责人不能编辑）
        elif field in project_manager_restricted_fields:
            # 只有管理员可以编辑，项目负责人不能编辑
            if is_admin:
                permissions[field] = True
            elif is_project_manager:
                permissions[field] = False
            else:
                permissions[field] = user.can_edit_issue(issue)
        # 其他字段，普通用户可以编辑（如果有议题编辑权限）
        else:
            permissions[field] = user.can_edit_issue(issue)

    return permissions


@issues.route('/<int:issue_id>/update-field', methods=['POST'])
@login_required
def update_field(issue_id):
    """更新议题字段"""
    try:
        current_user = get_current_user()
        issue = Issue.query.get_or_404(issue_id)
        field = request.json.get('field')
        value = request.json.get('value')

        current_app.logger.info(f'更新字段请求: 用户={current_user.username}, 议题={issue_id}, 字段={field}, 值={value}')

        # 检查字段编辑权限
        field_permissions = get_field_edit_permissions(current_user, issue)

        if field not in field_permissions or not field_permissions[field]:
            current_app.logger.warning(f'用户 {current_user.username} 无权编辑字段 {field}')
            return jsonify({'success': False, 'message': f'您没有权限编辑字段 {field}'}), 403
        
        # 处理空值
        if value == '' or value == '未设置' or value == '未填写':
            value = None
        
        # 处理布尔值字段
        boolean_fields = ['breakdown', 'warehouse_received_fault_parts', 'is_rework_vehicle', 
                         'problem_in_meeting', 'complaint_in_meeting']
        if field in boolean_fields:
            if value in ['1', 'true', True, 'True']:
                value = True
            elif value in ['0', 'false', False, 'False']:
                value = False
            else:
                value = None
        
        # 处理数字字段
        number_fields = ['sequence_number', 'problem_report_week', 'engineer_first_review_week', 
                        'mileage', 'mark']
        if field in number_fields:
            if value and str(value).strip():
                try:
                    if field == 'mileage':
                        value = float(value)
                    else:
                        value = int(value)
                except ValueError:
                    return jsonify({'success': False, 'message': '请输入有效的数字'}), 400
            else:
                value = None
        
        # 处理时间字段
        datetime_fields = ['created_at', 'engineer_first_review_time', 'zp8_delivery_date', 'problem_report_date']
        if field in datetime_fields:
            if value:
                from datetime import datetime
                try:
                    # 处理前端传来的datetime-local格式
                    if 'T' in str(value):
                        value = datetime.fromisoformat(str(value).replace('T', ' '))
                    else:
                        value = datetime.strptime(str(value), '%Y-%m-%d %H:%M:%S')
                except ValueError as e:
                    current_app.logger.error(f'时间格式转换失败: {value}, 错误: {e}')
                    return jsonify({'success': False, 'message': '时间格式不正确，请使用YYYY-MM-DD HH:MM:SS格式'}), 400
            else:
                value = None
        
        # 处理枚举字段
        if field == 'issue_status':
            if value:
                try:
                    # 确保传入的是枚举名称
                    if isinstance(value, str) and hasattr(IssueStatus, value):
                        value = getattr(IssueStatus, value)
                    else:
                        # 尝试通过中文标签查找枚举值
                        for status in IssueStatus:
                            if status.chinese_label == value:
                                value = status
                                break
                        else:
                            return jsonify({'success': False, 'message': f'无效的议题状态: {value}'}), 400
                except Exception as e:
                    current_app.logger.error(f'议题状态转换失败: {value}, 错误: {e}')
                    return jsonify({'success': False, 'message': f'无效的议题状态: {value}'}), 400
            else:
                value = None
        
        elif field == 'complaint_category':
            if value:
                try:
                    if isinstance(value, str) and hasattr(ComplaintCategory, value):
                        value = getattr(ComplaintCategory, value)
                    else:
                        # 尝试通过中文标签查找枚举值
                        for category in ComplaintCategory:
                            if category.chinese_label == value:
                                value = category
                                break
                        else:
                            return jsonify({'success': False, 'message': f'无效的抱怨类别: {value}'}), 400
                except Exception as e:
                    current_app.logger.error(f'抱怨类别转换失败: {value}, 错误: {e}')
                    return jsonify({'success': False, 'message': f'无效的抱怨类别: {value}'}), 400
            else:
                value = None
        
        elif field == 'vehicle_status':
            if value:
                try:
                    if isinstance(value, str) and hasattr(VehicleStatus, value):
                        value = getattr(VehicleStatus, value)
                    else:
                        # 尝试通过中文标签查找枚举值
                        for status in VehicleStatus:
                            if status.chinese_label == value:
                                value = status
                                break
                        else:
                            return jsonify({'success': False, 'message': f'无效的车辆状态: {value}'}), 400
                except Exception as e:
                    current_app.logger.error(f'车辆状态转换失败: {value}, 错误: {e}')
                    return jsonify({'success': False, 'message': f'无效的车辆状态: {value}'}), 400
            else:
                value = None

        # 检查字段是否存在于模型中
        if not hasattr(issue, field):
            current_app.logger.error(f'Issue模型中不存在字段: {field}')
            return jsonify({'success': False, 'message': f'字段 {field} 不存在'}), 400

        # 更新字段值
        old_value = getattr(issue, field)
        setattr(issue, field, value)
        
        # 更新时间戳（除了created_at字段外）
        if field != 'created_at':
            issue.updated_at = beijing_now()
        
        # 提交到数据库
        db.session.commit()
        
        current_app.logger.info(f'字段更新成功: {field} 从 {old_value} 更新为 {value}')
        
        # 根据字段类型返回正确的值
        return_value = value
        if hasattr(value, 'name'):  # 枚举类型
            return_value = value.name
        elif hasattr(value, 'isoformat'):  # 时间类型
            return_value = value.isoformat()
        elif value is None:
            return_value = None
        
        return jsonify({
            'success': True, 
            'message': '字段更新成功',
            'field': field,
            'new_value': return_value
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'字段更新失败: {e}', exc_info=True)
        return jsonify({
            'success': False, 
            'error': f'更新失败: {str(e)}',
            'message': '字段更新失败，请重试'
        }), 500


@issues.route('/<int:issue_id>/comments', methods=['POST'])
@login_required
def add_comment(issue_id):
    """添加评论"""
    current_user = get_current_user()
    issue = Issue.query.get_or_404(issue_id)
    
    try:
        data = request.get_json()
        if not data or not data.get('content'):
            return jsonify({'success': False, 'error': '评论内容不能为空'}), 400
        
        comment = Comment(
            issue_id=issue_id,
            author=current_user.username,
            content=data['content'],
            is_internal=data.get('is_internal', False),
            created_at=beijing_now()
        )
        
        db.session.add(comment)
        db.session.commit()
        
        # 返回新评论的完整信息，包括ID
        return jsonify({
            'success': True,
            'message': '评论添加成功',
            'comment': {
                'id': comment.id,
                'author': comment.author,
                'content': comment.content,
                'is_internal': comment.is_internal,
                'created_at': comment.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'is_edited': False
            }
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'添加评论失败: {e}')
        return jsonify({'success': False, 'error': '添加评论失败'}), 500


@issues.route('/<int:issue_id>/comments/<int:comment_id>', methods=['PUT'])
@login_required  
def edit_comment(issue_id, comment_id):
    """编辑评论"""
    current_user = get_current_user()
    comment = Comment.query.get_or_404(comment_id)
    
    # 权限检查
    if comment.author != current_user.username and not current_user.is_admin:
        return jsonify({'success': False, 'error': '您没有权限编辑此评论'}), 403
    
    try:
        data = request.get_json()
        if not data or not data.get('content'):
            return jsonify({'success': False, 'error': '评论内容不能为空'}), 400
        
        comment.content = data['content']
        comment.is_internal = data.get('is_internal', False)
        comment.is_edited = True
        comment.updated_at = beijing_now()
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '评论编辑成功'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': '编辑评论失败'}), 500


@issues.route('/<int:issue_id>/comments/<int:comment_id>', methods=['DELETE'])
@login_required
def delete_comment(issue_id, comment_id):
    """删除评论（软删除）"""
    current_user = get_current_user()
    comment = Comment.query.get_or_404(comment_id)
    
    # 权限检查
    if comment.author != current_user.username and not current_user.is_admin:
        return jsonify({'success': False, 'error': '您没有权限删除此评论'}), 403
    
    # 检查评论是否已被删除
    if comment.is_deleted:
        return jsonify({'success': False, 'error': '评论已被删除'}), 404
    
    try:
        # 软删除：标记为已删除而不是物理删除
        comment.is_deleted = True
        comment.updated_at = beijing_now()
        
        db.session.commit()
        
        # 返回成功响应，包含当前有效评论数
        active_comments_count = Comment.query.filter_by(
            issue_id=issue_id, 
            is_deleted=False
        ).count()
        
        return jsonify({
            'success': True,
            'message': '评论删除成功',
            'active_comments_count': active_comments_count
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'删除评论失败: {e}')
        return jsonify({'success': False, 'error': '删除评论失败'}), 500


@issues.route('/export')
@login_required
def export_issues():
    """导出筛选后的议题详细清单"""
    try:
        current_user = get_current_user()
        
        # 获取与list_issues相同的筛选参数
        status_filter = request.args.get('status')  # 议题状态
        maintenance_status_filter = request.args.get('maintenance_status')  # 维护状态
        search_query = request.args.get('q', '').strip()  # 关键字搜索
        project_number_filter = request.args.get('project_number', '').strip()  # 项目编号筛选
        department_filter = request.args.get('department', '').strip()  # 部门筛选
        engineer_filter = request.args.get('engineer', '').strip()  # 工程师筛选
        complaint_category_filter = request.args.get('complaint_category')  # 抱怨类别筛选
        vehicle_status_filter = request.args.get('vehicle_status')  # 车辆状态筛选
        created_date_from = request.args.get('created_date_from')  # 创建日期起始
        created_date_to = request.args.get('created_date_to')  # 创建日期结束
        problem_report_week_filter = request.args.get('problem_report_week')  # 问题上报周次筛选
        has_updates_filter = request.args.getlist('has_updates')  # 更新状态筛选（多选）
        
        # 构建与list_issues相同的查询逻辑，包含权限控制
        if current_user.role == UserRole.ADMIN or current_user.is_admin:
            # 管理员可以导出所有议题
            query = Issue.query
        else:
            # 根据用户角色应用权限控制
            if current_user.role == UserRole.PROJECT_MANAGER:
                # 项目负责人权限逻辑
                assigned_projects = ProjectAssignment.get_user_projects(current_user.id, active_only=True)
                if assigned_projects:
                    assignments = ProjectAssignment.query.filter_by(
                        user_id=current_user.id, 
                        is_active=True
                    ).all()
                    
                    project_reporter_conditions = []
                    project_reporter_conditions.append(Issue.handler == current_user.username)
                    
                    for assignment in assignments:
                        project_number = assignment.project_number
                        reporters = assignment.get_reporters()
                        
                        if reporters:
                            reporter_conditions_for_project = []
                            for reporter in reporters:
                                if reporter and reporter.strip():
                                    reporter_conditions_for_project.append(Issue.reporter.contains(reporter.strip()))
                            
                            if reporter_conditions_for_project:
                                project_reporter_conditions.append(
                                    db.and_(
                                        db.or_(
                                            Issue.project == project_number,
                                            Issue.project_number == project_number
                                        ),
                                        db.or_(*reporter_conditions_for_project)
                                    )
                                )
                        else:
                            project_reporter_conditions.append(
                                db.or_(
                                    Issue.project == project_number,
                                    Issue.project_number == project_number
                                )
                            )
                    
                    if project_reporter_conditions:
                        query = Issue.query.filter(db.or_(*project_reporter_conditions))
                    else:
                        query = Issue.query.filter(Issue.handler == current_user.username)
                else:
                    query = Issue.query.filter(
                        db.or_(
                            Issue.reporter.contains(current_user.username),
                            Issue.handler == current_user.username
                        )
                    )
            else:
                # 普通用户权限
                query = Issue.query.filter(
                    db.or_(
                        Issue.reporter.contains(current_user.username),
                        Issue.handler == current_user.username
                    )
                )
        
        # 应用筛选条件
        if search_query:
            query = query.filter(
                db.or_(
                    Issue.anlauf_number.contains(search_query),
                    Issue.project_number.contains(search_query),
                    Issue.problem_description.contains(search_query),  # 🔄 修复：使用problem_description
                    Issue.engineer.contains(search_query),
                    Issue.handler.contains(search_query)
                )
            )
        
        if project_number_filter:
            query = query.filter(
                db.or_(
                    Issue.project_number.contains(project_number_filter),
                    Issue.project.contains(project_number_filter)
                )
            )
        
        if department_filter:
            query = query.filter(
                db.or_(
                    Issue.department.contains(department_filter),
                    Issue.handler.contains(department_filter)
                )
            )
        
        if engineer_filter:
            query = query.filter(
                db.or_(
                    Issue.engineer.contains(engineer_filter),
                    Issue.reporter.contains(engineer_filter)
                )
            )
        
        if complaint_category_filter:
            try:
                category_enum = getattr(ComplaintCategory, complaint_category_filter)
                query = query.filter(Issue.complaint_category == category_enum)
            except AttributeError:
                pass
        
        if vehicle_status_filter:
            try:
                vehicle_status_enum = getattr(VehicleStatus, vehicle_status_filter)
                query = query.filter(Issue.vehicle_status == vehicle_status_enum)
            except AttributeError:
                pass
        
        if status_filter:
            try:
                status_enum = getattr(IssueStatus, status_filter)
                # 🔄 修复：使用实际的数据库字段 _issue_status 进行查询
                query = query.filter(
                    db.or_(
                        Issue._issue_status == status_enum.value,
                        Issue._issue_status == status_enum.chinese_label
                    )
                )
            except AttributeError:
                pass
        
        if maintenance_status_filter:
            try:
                maintenance_status_enum = getattr(MaintenanceStatus, maintenance_status_filter)
                query = query.filter(Issue.maintenance_status == maintenance_status_enum)
            except AttributeError:
                pass
        
        if problem_report_week_filter:
            try:
                week_num = int(problem_report_week_filter)
                query = query.filter(Issue.problem_report_week == week_num)
            except (ValueError, TypeError):
                pass
        
        if has_updates_filter:
            has_updates_conditions = []
            if 'true' in has_updates_filter:
                has_updates_conditions.append(Issue.has_updates == True)
            if 'false' in has_updates_filter:
                has_updates_conditions.append(db.or_(
                    Issue.has_updates == False,
                    Issue.has_updates.is_(None)
                ))
            if has_updates_conditions:
                query = query.filter(db.or_(*has_updates_conditions))
        
        # 创建日期范围筛选
        if created_date_from:
            try:
                from datetime import datetime
                from_date = datetime.strptime(created_date_from, '%Y-%m-%d')
                query = query.filter(Issue.created_at >= from_date)
            except ValueError:
                pass
        
        if created_date_to:
            try:
                from datetime import datetime
                to_date = datetime.strptime(created_date_to, '%Y-%m-%d')
                # 设置为当天结束时间（23:59:59）
                to_date = to_date.replace(hour=23, minute=59, second=59)
                query = query.filter(Issue.created_at <= to_date)
            except ValueError:
                pass
        
        # 获取所有符合条件的议题（按序号排序）
        issues = query.order_by(
            Issue.sequence_number.asc().nulls_last(),
            Issue.created_at.desc()
        ).all()
        
        # 构建导出数据 - 按照指定的32个字段顺序
        export_data = []
        for issue in issues:
            row = {
                # 1. 项目编号
                '项目编号': issue.project_number or issue.project or '',
                
                # 2. 序号
                '序号': issue.sequence_number or '',
                
                # 3. Anlauf编号
                'Anlauf编号': issue.anlauf_number or issue.key_field or '',
                
                # 4. 创建日期
                '创建日期': issue.created_at.strftime('%Y-%m-%d %H:%M:%S') if issue.created_at else '',
                
                # 5. 问题上报周次
                '问题上报周次': issue.problem_report_week or '',
                
                # 6. 审核状态
                '审核状态': issue.review_status or '',
                
                # 7. 工程师首次审核时间
                '工程师首次审核时间': issue.engineer_first_review_time.strftime('%Y-%m-%d %H:%M:%S') if issue.engineer_first_review_time else '',
                
                # 8. 工程师首次审核周次
                '工程师首次审核周次': issue.engineer_first_review_week or '',
                
                # 9. VIN
                'VIN': issue.vin or issue.vehicle_vin or '',
                
                # 10. 抱怨类别
                '抱怨类别': issue.complaint_category.chinese_label if issue.complaint_category else '',
                
                # 11. KDNR
                'KDNR': issue.kdnr or '',
                
                # 12. BANR
                'BANR': issue.banr or '',
                
                # 13. 抛锚
                '抛锚': '是' if issue.breakdown else '否' if issue.breakdown is not None else '',
                
                # 14. 售后故障描述
                '售后故障描述': issue.after_sales_fault_description or '',
                
                # 15. 里程
                '里程': f"{float(issue.mileage):.2f}" if issue.mileage else '',
                
                # 16. ZP8报交日期
                'ZP8报交日期': issue.zp8_delivery_date.strftime('%Y-%m-%d %H:%M:%S') if issue.zp8_delivery_date else '',
                
                # 17. 部门
                '部门': issue.department or issue.handler or '',
                
                # 18. 工程师
                '工程师': issue.engineer or issue.reporter or '',
                
                # 19. 分析/措施
                '分析/措施': issue.analysis_measures or '',
                
                # 20. 仓库收到故障件
                '仓库收到故障件': '是' if issue.warehouse_received_fault_parts else '否' if issue.warehouse_received_fault_parts is not None else '',
                
                # 21. 维修站号+中文名
                '维修站号+中文名': issue.repair_station_info or '',
                
                # 22. 问题上报日期
                '问题上报日期': issue.problem_report_date.strftime('%Y-%m-%d %H:%M:%S') if issue.problem_report_date else '',
                
                # 23. 车辆状态
                '车辆状态': issue.vehicle_status.chinese_label if issue.vehicle_status else '',
                
                # 24. 是否返工车辆
                '是否返工车辆': '是' if issue.is_rework_vehicle else '否' if issue.is_rework_vehicle is not None else '',
                
                # 25. 议题状态
                '议题状态': issue.issue_status.chinese_label if issue.issue_status else '',
                
                # 26. 问题描述
                '问题描述': issue.problem_description or '',
                
                # 27. 上会议题名
                '上会议题名': issue.meeting_topic_name or '',
                
                # 28. 标记
                '标记': issue.mark or '',
                
                # 29. 故障件状态
                '故障件状态': issue.fault_parts_status or '',
                
                # 30. 问题类型
                '问题类型': issue.problem_type or '',
                
                # 31. 问题是否上会
                '问题是否上会': '是' if issue.problem_in_meeting else '否' if issue.problem_in_meeting is not None else '',
                
                # 32. 抱怨是否上会
                '抱怨是否上会': '是' if issue.complaint_in_meeting else '否' if issue.complaint_in_meeting is not None else '',
            }
            export_data.append(row)
        
        # 使用pandas创建Excel文件
        df = pd.DataFrame(export_data)
        
        # 创建Excel文件内存缓冲区
        excel_buffer = io.BytesIO()
        
        # 写入Excel文件
        with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='议题详细清单', index=False)
            
            # 获取工作表以设置列宽和格式
            worksheet = writer.sheets['议题详细清单']
            
            # 设置表头样式
            from openpyxl.styles import Font, PatternFill, Alignment
            header_font = Font(bold=True, color='FFFFFF')
            header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
            center_alignment = Alignment(horizontal='center', vertical='center')
            
            # 应用表头样式
            for cell in worksheet[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = center_alignment
            
            # 自动调整列宽
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                # 根据字段类型设置合适的列宽
                if column_letter in ['A', 'C']:  # 项目编号、Anlauf编号
                    adjusted_width = min(max_length + 2, 15)
                elif column_letter in ['D', 'G', 'P', 'V']:  # 日期时间字段
                    adjusted_width = 20
                elif column_letter in ['N', 'S', 'Z', 'AA']:  # 长文本字段
                    adjusted_width = min(max_length + 2, 40)
                else:
                    adjusted_width = min(max_length + 2, 25)
                
                worksheet.column_dimensions[column_letter].width = adjusted_width
            
            # 冻结首行
            worksheet.freeze_panes = 'A2'
        
        excel_buffer.seek(0)
        
        # 生成文件名
        timestamp = beijing_now().strftime('%Y%m%d_%H%M%S')
        filename = f'议题详细清单_{timestamp}.xlsx'
        
        current_app.logger.info(f'用户 {current_user.username} 导出了 {len(issues)} 条议题记录')
        
        return send_file(
            excel_buffer,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )
        
    except Exception as e:
        current_app.logger.error(f'导出议题清单失败: {e}')
        flash('导出失败，请重试', 'error')
        return redirect(url_for('issues.list_issues'))


@issues.route('/batch-maintenance-status', methods=['POST'])
@login_required
def batch_update_maintenance_status():
    """批量更新维护状态"""
    try:
        current_user = get_current_user()
        
        # 权限检查
        if current_user.role not in [UserRole.ADMIN, UserRole.PROJECT_MANAGER] and not current_user.is_admin:
            return jsonify({'success': False, 'error': '您没有权限执行批量操作'}), 403
        
        # 从POST请求体获取状态信息
        data = request.get_json()
        if not data or 'status' not in data:
            return jsonify({'success': False, 'error': '缺少状态参数'}), 400
        
        status_name = data['status']
        
        # 验证状态值
        try:
            new_status = getattr(MaintenanceStatus, status_name)
        except AttributeError:
            return jsonify({'success': False, 'error': '无效的状态值'}), 400
        
        # 获取与list_issues完全相同的筛选参数（支持多选）
        status_filter = request.args.getlist('status')  # 议题状态（多选）
        maintenance_status_filter = request.args.getlist('maintenance_status')  # 维护状态（多选）
        search_query = request.args.get('q', '').strip()  # 关键字搜索
        project_number_filter = request.args.getlist('project_number')  # 项目编号筛选（多选）
        department_filter = request.args.getlist('department')  # 部门筛选（多选）
        engineer_filter = request.args.getlist('engineer')  # 工程师筛选（多选）
        complaint_category_filter = request.args.getlist('complaint_category')  # 抱怨类别筛选（多选）
        vehicle_status_filter = request.args.getlist('vehicle_status')  # 车辆状态筛选（多选）
        breakdown_filter = request.args.getlist('breakdown')  # 抛锚筛选（多选）
        problem_in_meeting_filter = request.args.getlist('problem_in_meeting')  # 问题是否上会筛选（多选）
        complaint_in_meeting_filter = request.args.getlist('complaint_in_meeting')  # 抱怨是否上会筛选（多选）
        kdnr_filter = request.args.getlist('kdnr')  # KDNR筛选（多选）
        banr_filter = request.args.getlist('banr')  # BANR筛选（多选）
        created_date_from = request.args.get('created_date_from', '').strip()  # 创建日期从
        created_date_to = request.args.get('created_date_to', '').strip()  # 创建日期到
        problem_report_week_filter = request.args.get('problem_report_week', '').strip()  # 问题上报周次筛选
        has_updates_filter = request.args.getlist('has_updates')  # 更新状态筛选（多选）
        online_status_filter = request.args.get('online_status', '').strip()  # 已上线/未上线议题筛选
        
        # 构建基础查询（根据用户权限）
        query = get_user_accessible_issues(current_user)
        
        # 🔄 新增：处理已上线/未上线议题筛选
        if online_status_filter:
            if online_status_filter == 'true':
                # 已上线议题：状态中文标签包含"已上"的状态
                online_statuses = [status for status in IssueStatus if "已上" in status.chinese_label]
                online_status_values = []
                for status in online_statuses:
                    online_status_values.extend([status.value, status.chinese_label])
                query = query.filter(Issue._issue_status.in_(online_status_values))
            elif online_status_filter == 'false':
                # 未上线议题：状态中文标签包含"未上"的状态  
                offline_statuses = [status for status in IssueStatus if "未上" in status.chinese_label]
                offline_status_values = []
                for status in offline_statuses:
                    offline_status_values.extend([status.value, status.chinese_label])
                query = query.filter(Issue._issue_status.in_(offline_status_values))
        
        # 应用筛选条件（支持多选）- 与list_issues完全相同的逻辑
        if status_filter:
            status_conditions = []
            for status_name in status_filter:
                try:
                    status_enum = getattr(IssueStatus, status_name)
                    status_conditions.append(Issue._issue_status == status_enum.value)
                    status_conditions.append(Issue._issue_status == status_enum.chinese_label)
                except AttributeError:
                    pass
            if status_conditions:
                query = query.filter(db.or_(*status_conditions))
        
        if maintenance_status_filter:
            maintenance_status_conditions = []
            for maintenance_status_name in maintenance_status_filter:
                try:
                    maintenance_status_enum = getattr(MaintenanceStatus, maintenance_status_name)
                    maintenance_status_conditions.append(Issue.maintenance_status == maintenance_status_enum)
                except AttributeError:
                    pass
            if maintenance_status_conditions:
                query = query.filter(db.or_(*maintenance_status_conditions))
        
        if search_query:
            query = query.filter(
                db.or_(
                    Issue.anlauf_number.contains(search_query),
                    Issue.after_sales_fault_description.contains(search_query),
                    Issue.vin.contains(search_query),
                    Issue.project_number.contains(search_query),
                    Issue.summary.contains(search_query),
                    Issue.problem_remarks.contains(search_query)
                )
            )
        
        if project_number_filter:
            query = query.filter(Issue.project_number.in_(project_number_filter))
        
        if department_filter:
            query = query.filter(Issue.department.in_(department_filter))
        
        if engineer_filter:
            query = query.filter(Issue.engineer.in_(engineer_filter))
        
        if complaint_category_filter:
            complaint_category_conditions = []
            for category_name in complaint_category_filter:
                try:
                    category_enum = getattr(ComplaintCategory, category_name)
                    complaint_category_conditions.append(Issue.complaint_category == category_enum)
                except AttributeError:
                    pass
            if complaint_category_conditions:
                query = query.filter(db.or_(*complaint_category_conditions))
        
        if vehicle_status_filter:
            vehicle_status_conditions = []
            for vehicle_status_name in vehicle_status_filter:
                try:
                    vehicle_status_enum = getattr(VehicleStatus, vehicle_status_name)
                    vehicle_status_conditions.append(Issue.vehicle_status == vehicle_status_enum)
                except AttributeError:
                    pass
            if vehicle_status_conditions:
                query = query.filter(db.or_(*vehicle_status_conditions))
        
        if breakdown_filter:
            breakdown_conditions = []
            for breakdown_value in breakdown_filter:
                if breakdown_value == 'true':
                    breakdown_conditions.append(Issue.breakdown == True)
                elif breakdown_value == 'false':
                    breakdown_conditions.append(Issue.breakdown == False)
            if breakdown_conditions:
                query = query.filter(db.or_(*breakdown_conditions))
        
        if problem_in_meeting_filter:
            problem_in_meeting_conditions = []
            for meeting_value in problem_in_meeting_filter:
                if meeting_value == 'true':
                    problem_in_meeting_conditions.append(Issue.problem_in_meeting == True)
                elif meeting_value == 'false':
                    problem_in_meeting_conditions.append(Issue.problem_in_meeting == False)
            if problem_in_meeting_conditions:
                query = query.filter(db.or_(*problem_in_meeting_conditions))
        
        if complaint_in_meeting_filter:
            complaint_in_meeting_conditions = []
            for meeting_value in complaint_in_meeting_filter:
                if meeting_value == 'true':
                    complaint_in_meeting_conditions.append(Issue.complaint_in_meeting == True)
                elif meeting_value == 'false':
                    complaint_in_meeting_conditions.append(Issue.complaint_in_meeting == False)
            if complaint_in_meeting_conditions:
                query = query.filter(db.or_(*complaint_in_meeting_conditions))
        
        if kdnr_filter:
            query = query.filter(Issue.kdnr.in_(kdnr_filter))
        
        if banr_filter:
            query = query.filter(Issue.banr.in_(banr_filter))
        
        # 创建日期范围筛选
        if created_date_from:
            try:
                from datetime import datetime
                from_date = datetime.strptime(created_date_from, '%Y-%m-%d')
                query = query.filter(Issue.created_at >= from_date)
            except ValueError:
                pass
        
        if created_date_to:
            try:
                from datetime import datetime
                to_date = datetime.strptime(created_date_to, '%Y-%m-%d')
                to_date = to_date.replace(hour=23, minute=59, second=59)
                query = query.filter(Issue.created_at <= to_date)
            except ValueError:
                pass
        
        if problem_report_week_filter:
            query = query.filter(Issue.problem_report_week.contains(problem_report_week_filter))
        
        if has_updates_filter:
            has_updates_conditions = []
            for update_value in has_updates_filter:
                if update_value == 'true':
                    has_updates_conditions.append(Issue.has_updates == True)
                elif update_value == 'false':
                    has_updates_conditions.append(Issue.has_updates == False)
            if has_updates_conditions:
                query = query.filter(db.or_(*has_updates_conditions))
        
        # 执行批量更新
        updated_count = query.update({
            Issue.maintenance_status: new_status,
            Issue.updated_at: beijing_now()  # 更新修改时间
        })
        db.session.commit()
        
        # 记录操作日志
        current_app.logger.info(f'用户 {current_user.username} 批量更新了 {updated_count} 个议题的维护状态为 {new_status.value}')
        
        return jsonify({
            'success': True,
            'message': f'已成功更新 {updated_count} 个议题的维护状态为 {new_status.chinese_label}',
            'updated_count': updated_count,
            'new_status': {
                'name': new_status.name,
                'value': new_status.value,
                'chinese_label': new_status.chinese_label
            }
        })
        
    except Exception as e:
        current_app.logger.error(f'批量更新维护状态失败: {str(e)}')
        db.session.rollback()
        return jsonify({
            'success': False, 
            'error': '批量更新失败，请稍后重试',
            'details': str(e)
        }), 500


@issues.route('/batch-emails', methods=['POST'])
@login_required
def batch_send_emails():
    """
    批量发送邮件给工程师
    根据工程师字段分组并发送给对应的工程师
    """
    try:
        from app.utils.email_utils import send_batch_emails_via_outlook, generate_engineer_notification_email_body
        
        current_user = get_current_user()
        
        # 权限控制：只有项目负责人和管理员可以使用批量邮件功能
        if current_user.role not in [UserRole.PROJECT_MANAGER, UserRole.ADMIN] and not current_user.is_admin:
            return jsonify({
                'success': False,
                'message': '权限不足：只有项目负责人和管理员可以使用批量邮件功能'
            })
        
        # 使用与列表页面相同的筛选逻辑获取议题
        # 获取过滤参数
        status_filter = request.args.get('status')
        maintenance_status_filter = request.args.get('maintenance_status')
        search_query = request.args.get('q', '').strip()
        project_filter = request.args.get('project', '').strip()
        vehicle_vin_filter = request.args.get('vehicle_vin', '').strip()
        severity_filter = request.args.get('severity')
        problem_description_filter = request.args.get('problem_description', '').strip() # 🔄 修复
        created_date_from = request.args.get('created_date_from')
        created_date_to = request.args.get('created_date_to')
        resolution_result_type = request.args.get('resolution_result_type')
        online_status_filter = request.args.get('online_status')
        priority_filter = request.args.get('priority')
        handler_filter = request.args.get('handler', '').strip()
        reporter_filter = request.args.get('reporter', '').strip()
        
        # 基于用户角色构建查询 - 与list_issues保持一致
        if current_user.role == UserRole.ADMIN or current_user.is_admin:
            query = Issue.query
        elif current_user.role == UserRole.PROJECT_MANAGER:
            assigned_projects = ProjectAssignment.get_user_projects(current_user.id, active_only=True)
            if assigned_projects:
                # 获取所有项目分配及其对应的报告人
                assignments = ProjectAssignment.query.filter_by(
                    user_id=current_user.id, 
                    is_active=True
                ).all()
                
                # 构建复合条件：项目编号 + 对应的报告人
                project_reporter_conditions = []
                
                # 自己经办的议题总是可以查看
                project_reporter_conditions.append(Issue.handler == current_user.username)
                
                for assignment in assignments:
                    project_number = assignment.project_number
                    reporters = assignment.get_reporters()
                    
                    if reporters:
                        # 构建该项目下指定报告人的条件
                        reporter_conditions_for_project = []
                        for reporter in reporters:
                            if reporter and reporter.strip():
                                reporter_conditions_for_project.append(Issue.reporter.contains(reporter.strip()))
                        
                        if reporter_conditions_for_project:
                            # 项目编号匹配 AND 报告人匹配
                            project_reporter_conditions.append(
                                db.and_(
                                    Issue.project == project_number,
                                    db.or_(*reporter_conditions_for_project)
                                )
                            )
                    else:
                        # 如果该项目没有指定报告人，则可以查看该项目的所有议题
                        project_reporter_conditions.append(Issue.project == project_number)
                
                if project_reporter_conditions:
                    query = Issue.query.filter(db.or_(*project_reporter_conditions))
                else:
                    # 如果没有任何有效的分配条件，只能查看自己经办的议题
                    query = Issue.query.filter(Issue.handler == current_user.username)
            else:
                query = Issue.query.filter(
                    db.or_(
                        Issue.reporter.contains(current_user.username),
                        Issue.handler == current_user.username
                    )
                )
        elif current_user.role == UserRole.MANAGER:
            if current_user.department:
                query = Issue.query.filter(
                    db.or_(
                        Issue.summary.contains(current_user.department),
                        Issue.handler == current_user.username,
                        Issue.reporter.contains(current_user.username)
                    )
                )
            else:
                query = Issue.query.filter(
                    db.or_(
                        Issue.reporter.contains(current_user.username),
                        Issue.handler == current_user.username
                    )
                )
        else:
            query = Issue.query.filter(
                db.or_(
                    Issue.reporter.contains(current_user.username),
                    Issue.handler == current_user.username
                )
            )
        
        # 应用所有筛选条件
        if search_query:
            query = query.filter(
                db.or_(
                    Issue.key_field.contains(search_query),
                    Issue.summary.contains(search_query),
                    Issue.problem_remarks.contains(search_query)
                )
            )
        
        if project_filter:
            query = query.filter(Issue.project.contains(project_filter))
        
        if vehicle_vin_filter:
            query = query.filter(Issue.vehicle_vin.contains(vehicle_vin_filter))
        
        if severity_filter:
            query = query.filter(Issue.severity == severity_filter)
        
        if problem_description_filter:
            query = query.filter(Issue.problem_description.contains(problem_description_filter))
        
        if handler_filter:
            query = query.filter(Issue.handler.contains(handler_filter))
        
        if reporter_filter:
            query = query.filter(Issue.reporter.contains(reporter_filter))
        
        if online_status_filter:
            if online_status_filter == 'true':
                # 🔄 修复：使用中文标签判断，而不是英文枚举名
                online_statuses = [status for status in IssueStatus if "已上" in status.chinese_label]
                online_status_values = []
                for status in online_statuses:
                    online_status_values.extend([status.value, status.chinese_label])
                query = query.filter(Issue._issue_status.in_(online_status_values))
            elif online_status_filter == 'false':
                # 🔄 修复：使用中文标签判断，而不是英文枚举名
                offline_statuses = [status for status in IssueStatus if "未上" in status.chinese_label]
                offline_status_values = []
                for status in offline_statuses:
                    offline_status_values.extend([status.value, status.chinese_label])
                query = query.filter(Issue._issue_status.in_(offline_status_values))
        
        if priority_filter:
            try:
                priority_enum = getattr(Priority, priority_filter)
                query = query.filter(Issue.priority == priority_enum)
            except AttributeError:
                pass
        
        if status_filter:
            try:
                status_enum = getattr(IssueStatus, status_filter)
                # 🔄 修复：使用实际的数据库字段 _issue_status 进行查询
                query = query.filter(
                    db.or_(
                        Issue._issue_status == status_enum.value,
                        Issue._issue_status == status_enum.chinese_label
                    )
                )
            except AttributeError:
                pass
        
        if maintenance_status_filter:
            try:
                maintenance_status_enum = getattr(MaintenanceStatus, maintenance_status_filter)
                query = query.filter(Issue.maintenance_status == maintenance_status_enum)
            except AttributeError:
                pass
        
        # 添加日期筛选
        if created_date_from:
            try:
                from datetime import datetime
                from_date = datetime.strptime(created_date_from, '%Y-%m-%d')
                query = query.filter(Issue.created_at >= from_date)
            except ValueError:
                pass
        
        if created_date_to:
            try:
                from datetime import datetime
                to_date = datetime.strptime(created_date_to, '%Y-%m-%d')
                # 设置为当天的23:59:59
                to_date = to_date.replace(hour=23, minute=59, second=59)
                query = query.filter(Issue.created_at <= to_date)
            except ValueError:
                pass
        
        # 获取筛选后的议题
        issues = query.all()
        
        if not issues:
            return jsonify({
                'success': False,
                'message': '没有找到符合筛选条件的议题'
            })
        
        # 🔄 修改：按工程师分组议题，而不是按报告人
        engineer_issues = {}
        
        for issue in issues:
            # 检查工程师字段是否有值
            if not issue.engineer:
                continue
            
            engineer_name = issue.engineer.strip()
            if not engineer_name:
                continue
            
            if engineer_name not in engineer_issues:
                engineer_issues[engineer_name] = []
            engineer_issues[engineer_name].append(issue)
        
        if not engineer_issues:
            return jsonify({
                'success': False,
                'message': '筛选结果中没有议题指定了工程师'
            })
        
        # 🔄 修改：获取所有工程师的用户信息和邮箱
        all_users = User.query.filter(User.email.isnot(None), User.email != '').all()
        
        if not all_users:
            return jsonify({
                'success': False,
                'message': '系统中没有找到有邮箱的用户'
            })
        
        # 创建用户名到用户对象的映射
        user_map = {user.username: user for user in all_users}
        
        # 准备邮件列表
        email_list = []
        current_app.logger.info(f'开始准备邮件列表，共找到 {len(engineer_issues)} 个工程师')
        
        for engineer_name, engineer_issue_list in engineer_issues.items():
            # 查找工程师的用户信息和邮箱
            user = user_map.get(engineer_name)
            if not user or not user.email:
                current_app.logger.warning(f'工程师 {engineer_name} 没有找到用户记录或邮箱地址')
                continue
            
            # 收集项目编号
            project_numbers = list(set([
                issue.project_number or issue.project 
                for issue in engineer_issue_list 
                if (issue.project_number or issue.project)
            ]))
            
            # 构建议题信息列表
            issues_info = []
            for issue in engineer_issue_list:
                issues_info.append({
                    'issue_number': issue.anlauf_number or issue.key_field or 'N/A',
                    'project_number': issue.project_number or issue.project or 'N/A',
                    'status': issue.issue_status.chinese_label if issue.issue_status else 'N/A',
                    'description': issue.problem_description or issue.after_sales_fault_description or issue.summary or 'N/A',
                    'report_date': issue.problem_report_date.strftime('%Y-%m-%d') if issue.problem_report_date else (issue.created_at.strftime('%Y-%m-%d') if issue.created_at else 'N/A')
                })
            
            # 生成邮件主题
            if project_numbers:
                subject = f'【工程师提醒】"{", ".join(project_numbers)}"项目议题处理通知'
            else:
                subject = '【工程师提醒】议题处理通知'
            
            # 生成邮件正文
            body = generate_engineer_notification_email_body(engineer_name, project_numbers, issues_info)
            
            email_list.append({
                'to_email': user.email,
                'subject': subject,
                'body': body
            })
            
            current_app.logger.info(f'为工程师 {engineer_name} ({user.email}) 准备邮件，包含 {len(engineer_issue_list)} 个议题')
        
        if not email_list:
            return jsonify({
                'success': False,
                'message': '没有找到有效的工程师邮箱地址'
            })
        
        # 发送邮件
        current_app.logger.info(f'开始批量发送邮件给工程师，共 {len(email_list)} 封')
        results = send_batch_emails_via_outlook(email_list)
        
        # 记录发送结果
        current_app.logger.info(f'邮件发送完成 - 成功: {results["success_count"]}, 失败: {results["failed_count"]}')
        
        # 记录失败的邮件
        if results['failed_emails']:
            for failed_email in results['failed_emails']:
                current_app.logger.error(f'邮件发送失败 - {failed_email["email"]}: {failed_email["error"]}')
        
        return jsonify({
            'success': True,
            'sent_count': results['success_count'],
            'failed_count': results['failed_count'],
            'unique_engineers': len(engineer_issues),
            'message': f'邮件发送完成，成功发送给 {results["success_count"]} 位工程师，失败 {results["failed_count"]} 封'
        })
        
    except Exception as e:
        current_app.logger.error(f'批量发送邮件失败: {e}')
        return jsonify({
            'success': False,
            'message': f'发送邮件时发生错误: {str(e)}'
        })

@issues.route('/api/users', methods=['GET'])
@login_required
def get_users_for_mention():
    """获取可用于@提及的用户列表"""
    try:
        from app.models.user import User
        from app.models.enums import UserStatus
        
        # 只获取激活的用户
        users = User.query.filter_by(
            status=UserStatus.ACTIVE,
            is_active=True
        ).order_by(User.username.asc()).all()
        
        user_list = []
        for user in users:
            user_data = {
                'id': user.id,
                'username': user.username,
                'role': user.role.chinese_label if hasattr(user.role, 'chinese_label') else user.role.value,
                'department': user.department or '未设置',
                'position': user.position or '未设置',
                'avatar_url': user.avatar_url or '/static/images/default-avatar.png'
            }
            user_list.append(user_data)
        
        return jsonify({
            'success': True,
            'users': user_list
        })
        
    except Exception as e:
        current_app.logger.error(f'获取用户列表失败: {e}')
        return jsonify({
            'success': False,
            'message': '获取用户列表失败'
        }), 500 