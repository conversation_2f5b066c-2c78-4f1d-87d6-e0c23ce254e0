#!/usr/bin/env python
"""
测试车辆状态枚举更新
验证四类车辆状态的正确映射和显示
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import Issue
from app.models.enums import VehicleStatus

def test_vehicle_status_enum():
    """测试车辆状态枚举定义"""
    print("\n测试车辆状态枚举定义...")
    
    # 检查新的四类车辆状态
    expected_statuses = {
        'PRE_SALE_PDI': '售前PDI',
        'TEST_DRIVE': '试驾车',
        'DISPLAY': '展车',
        'CUSTOMER_CAR': '客户车'
    }
    
    print("   预期的车辆状态:")
    for status_value, chinese_label in expected_statuses.items():
        print(f"     - {status_value}: {chinese_label}")
    
    print("\n   实际的车辆状态:")
    for status in VehicleStatus:
        print(f"     - {status.value}: {status.chinese_label}")
        
    # 验证枚举值
    success = True
    for status_value, expected_label in expected_statuses.items():
        try:
            status = VehicleStatus(status_value)
            actual_label = status.chinese_label
            if actual_label == expected_label:
                print(f"   [OK] {status_value} -> {actual_label}")
            else:
                print(f"   [ERROR] {status_value} -> 期望: {expected_label}, 实际: {actual_label}")
                success = False
        except ValueError as e:
            print(f"   [ERROR] {status_value} 枚举值不存在: {e}")
            success = False
    
    return success

def test_vehicle_status_mapping():
    """测试车辆状态映射逻辑"""
    print("\n测试车辆状态映射逻辑...")
    
    app = create_app()
    with app.app_context():
        # 创建测试议题
        test_issue = Issue()
        
        # 测试旧数据映射
        test_cases = [
            ('客户车', 'CUSTOMER_CAR', '客户车'),
            ('售前车', 'PRE_SALE_PDI', '售前PDI'),  # 售前车 -> 售前PDI
            ('试驾车', 'TEST_DRIVE', '试驾车'),
            ('展车', 'DISPLAY', '展车'),
            ('商品车', 'PRE_SALE_PDI', '售前PDI'),  # 商品车 -> 售前PDI
            ('PRE_SALE', 'PRE_SALE_PDI', '售前PDI'),  # 旧英文值
            ('COMMERCIAL', 'PRE_SALE_PDI', '售前PDI'),  # 旧英文值
        ]
        
        success = True
        for input_value, expected_enum, expected_label in test_cases:
            try:
                # 设置车辆状态
                test_issue._vehicle_status = input_value
                
                # 获取转换后的枚举值
                result_enum = test_issue.vehicle_status
                
                if result_enum is None:
                    print(f"   [ERROR] {input_value} -> None (期望: {expected_enum})")
                    success = False
                elif result_enum.value == expected_enum and result_enum.chinese_label == expected_label:
                    print(f"   [OK] {input_value} -> {result_enum.value} ({result_enum.chinese_label})")
                else:
                    print(f"   [ERROR] {input_value} -> {result_enum.value} ({result_enum.chinese_label}), 期望: {expected_enum} ({expected_label})")
                    success = False

            except Exception as e:
                print(f"   [ERROR] {input_value} 转换失败: {e}")
                success = False
        
        return success

def test_database_compatibility():
    """测试数据库兼容性"""
    print("\n测试数据库兼容性...")
    
    app = create_app()
    with app.app_context():
        try:
            # 查询包含车辆状态的议题
            issues_with_vehicle_status = Issue.query.filter(Issue._vehicle_status.isnot(None)).limit(5).all()
            
            print(f"   找到 {len(issues_with_vehicle_status)} 个包含车辆状态的议题")
            
            success = True
            for issue in issues_with_vehicle_status:
                try:
                    raw_status = issue._vehicle_status
                    converted_status = issue.vehicle_status
                    
                    if converted_status:
                        print(f"   [OK] 议题 {issue.id}: {raw_status} -> {converted_status.value} ({converted_status.chinese_label})")
                    else:
                        print(f"   [WARN] 议题 {issue.id}: {raw_status} -> None (无法转换)")

                except Exception as e:
                    print(f"   [ERROR] 议题 {issue.id} 处理失败: {e}")
                    success = False
            
            return success
            
        except Exception as e:
            print(f"   [ERROR] 数据库查询失败: {e}")
            return False

def main():
    """主测试函数"""
    print("车辆状态枚举更新测试")
    print("=" * 50)
    
    # 测试枚举定义
    enum_test = test_vehicle_status_enum()
    
    # 测试映射逻辑
    mapping_test = test_vehicle_status_mapping()
    
    # 测试数据库兼容性
    db_test = test_database_compatibility()
    
    # 总结
    print("\n测试结果总结:")
    print(f"   枚举定义测试: {'通过' if enum_test else '失败'}")
    print(f"   映射逻辑测试: {'通过' if mapping_test else '失败'}")
    print(f"   数据库兼容性测试: {'通过' if db_test else '失败'}")
    
    if enum_test and mapping_test and db_test:
        print("\n所有测试通过！车辆状态枚举更新成功。")
        return True
    else:
        print("\n部分测试失败，请检查相关问题。")
        return False

if __name__ == '__main__':
    main()
