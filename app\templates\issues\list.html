{% extends "base.html" %}

{% block title %}议题列表 - {{ app_name }}{% endblock %}

{% block extra_meta %}
<meta name="csrf-token" content="{{ csrf_token() }}">
{% endblock %}

{% block extra_css %}
<style>
/* 🔄 维护状态容器样式 - 进一步缩窄宽度 */
.maintenance-status-container {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    gap: 4px; /* 🔄 减小间距 */
    width: 100%;
    max-width: 90px; /* 🔄 从75px调整到90px，确保列名在一行 */
    margin: 0 auto;
}

/* 🔄 维护状态徽章样式 - 进一步缩窄宽度 */
.maintenance-status-badge {
    width: 100% !important;
    text-align: center;
    display: flex !important;
    align-items: center;
    justify-content: center;
    font-size: 0.5rem !important; /* 🔄 进一步减小字体 */
    padding: 0.2rem 0.1rem !important; /* 🔄 进一步减小内边距 */
    line-height: 1.0;
    white-space: nowrap;
    box-sizing: border-box;
    border-radius: 0.25rem; /* 🔄 进一步减小圆角 */
}

/* 🔄 按钮组样式 - 进一步缩窄宽度 */
.maintenance-btn-group {
    width: 100% !important;
    display: flex !important;
    border-radius: 0.25rem; /* 🔄 进一步减小圆角 */
    overflow: hidden;
}

.maintenance-btn-group .btn {
    flex: 1 1 33.333%;
    padding: 0.2rem 0.03rem !important; /* 🔄 进一步减小内边距 */
    font-size: 0.55rem; /* 🔄 进一步减小字体 */
    border-radius: 0 !important;
    margin: 0 !important;
    box-sizing: border-box;
}

/* 第一个按钮左圆角 */
.maintenance-btn-group .btn:first-child {
    border-top-left-radius: 0.25rem !important;
    border-bottom-left-radius: 0.25rem !important;
}

/* 最后一个按钮右圆角 */
.maintenance-btn-group .btn:last-child {
    border-top-right-radius: 0.25rem !important;
    border-bottom-right-radius: 0.25rem !important;
}

/* 移除按钮间的边框重叠 */
.maintenance-btn-group .btn + .btn {
    border-left: none;
}

/* 🔄 维护状态列的样式优化 - 适当放宽确保列名在一行 */
.maintenance-status-column {
    min-width: 95px; /* 🔄 从80px放宽到95px，确保"维修状态"列名在一行 */
    width: 95px;
}

/* 维护状态图标样式优化 */
.maintenance-status-badge i {
    font-size: 0.45rem !important; /* 🔄 进一步缩小图标 */
    margin-right: 0.15rem !important;
}

.maintenance-btn-group .btn i {
    font-size: 0.5rem !important; /* 🔄 进一步缩小按钮图标 */
}

.btn-group .btn-sm {
    padding: 0.25rem 0.4rem;
    font-size: 0.7rem;
    border-radius: 0.2rem;
}

.btn-group .btn-sm:first-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.btn-group .btn-sm:last-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.btn-group .btn-sm:not(:first-child):not(:last-child) {
    border-radius: 0;
}

/* 标签列样式 */
.tags-column {
    max-width: 150px;
    overflow: hidden;
}

.tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 2px;
    justify-content: center;
    align-items: center;
    min-height: 20px;
}

.tag-badge {
    font-size: 0.65rem;
    padding: 0.15rem 0.4rem;
    border-radius: 3px;
    white-space: nowrap;
    display: inline-block;
    max-width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.2;
}

.tag-badge:hover {
    opacity: 0.8;
    transform: scale(1.05);
    transition: all 0.2s ease;
}

/* 分析/措施列样式 - 自动换行，显示完整内容 */
.analysis-measures-cell {
    word-wrap: break-word;
    word-break: break-all;
    white-space: normal;
    line-height: 1.3;
    padding: 8px 6px;
    vertical-align: top;
}

.analysis-measures-content {
    /* 移除高度限制，显示完整内容 */
    white-space: normal;
    word-wrap: break-word;
}

/* 🔄 优化表格滚动和列宽度分配 */
.table-container {
    overflow-x: auto;
    overflow-y: auto;
    max-width: 100%;
    max-height: 75vh;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    position: relative;
}

.table-container table {
    min-width: 1800px; /* 🔄 增加表格最小宽度，新增了4列 */
    margin-bottom: 0;
    table-layout: fixed;
    width: 100%;
}

/* 固定表头样式 */
.table-container thead th {
    position: sticky;
    top: 0;
    background-color: #f8f9fa;
    z-index: 10;
    border-top: none;
    box-shadow: 0 2px 2px -1px rgba(0, 0, 0, 0.1);
}

/* 🔄 优化列宽度分配 - 调整右侧状态列和操作列的宽度 */
.table-container th:nth-child(1) { width: 2%; }   /* 序号 */
.table-container th:nth-child(2) { width: 5%; }   /* Anlauf编号 */
.table-container th:nth-child(3) { width: 5%; }   /* 项目编号 */
.table-container th:nth-child(4) { width: 8%; }   /* VIN码 */
.table-container th:nth-child(5) { width: 12%; }  /* 售后故障描述 */
.table-container th:nth-child(6) { width: 25%; }  /* 分析/措施 */
.table-container th:nth-child(7) { width: 8%; }   /* 维修站号+中文名 */
.table-container th:nth-child(8) { width: 5%; }   /* 上报时间天数差 */
.table-container th:nth-child(9) { width: 4%; }   /* 标记 */
.table-container th:nth-child(10) { width: 8%; }  /* 问题标签 */
.table-container th:nth-child(11) { width: 4%; }  /* KDNR */
.table-container th:nth-child(12) { width: 4%; }  /* 车辆状态 */
.table-container th:nth-child(13) { width: 6%; }  /* 议题状态 - 增加宽度 */
.table-container th:nth-child(14) { width: 6%; }  /* 维护状态 - 增加宽度 */
.table-container th:nth-child(15) { width: 8%; }  /* 操作 - 减少宽度 */

/* 表格滚动条样式 */
.table-container::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.table-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}

/* 议题状态徽章样式 */
.issue-status-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.35rem 0.6rem;
    font-size: 0.65rem;
    font-weight: 500;
    border-radius: 0.375rem;
    white-space: nowrap;
    min-width: 80px;
    max-width: 120px;
    text-align: center;
    border: 1px solid transparent;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.issue-status-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

/* 状态文本样式 */
.status-text {
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 紧凑徽章样式 */
.compact-badge {
    padding: 0.25rem 0.5rem;
    font-size: 0.6rem;
    border-radius: 0.25rem;
}

/* 🎨 批量操作按钮美化样式 */
.batch-operations {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: wrap;
}

/* 基础按钮美化 */
.batch-operations .btn {
    position: relative;
    font-weight: 600;
    letter-spacing: 0.025em;
    border: none;
    border-radius: 8px;
    padding: 0.6rem 1.2rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.batch-operations .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s;
}

.batch-operations .btn:hover::before {
    left: 100%;
}

.batch-operations .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.batch-operations .btn:active {
    transform: translateY(0);
    transition: transform 0.1s;
}

/* 批量维护状态按钮（渐变蓝色） */
.batch-operations .btn-outline-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    border: 2px solid transparent;
}

.batch-operations .btn-outline-info:hover {
    background: linear-gradient(135deg, #138496 0%, #0f6674 100%);
    color: white;
    border-color: #0f6674;
}

/* 批量邮件按钮（渐变橙色） */
.batch-operations .btn-outline-warning {
    background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);
    color: white;
    border: 2px solid transparent;
}

.batch-operations .btn-outline-warning:hover {
    background: linear-gradient(135deg, #e55a00 0%, #cc4e00 100%);
    color: white;
    border-color: #cc4e00;
}

/* 批量导出按钮（渐变绿色） */
.batch-operations .btn-outline-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    color: white;
    border: 2px solid transparent;
}

.batch-operations .btn-outline-success:hover {
    background: linear-gradient(135deg, #1e7e34 0%, #155724 100%);
    color: white;
    border-color: #155724;
}

/* AI问题归类按钮（渐变蓝色） */
.batch-operations .btn-outline-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border: 2px solid transparent;
}

.batch-operations .btn-outline-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    color: white;
    border-color: #004085;
}

/* AI按钮加载状态 */
.btn-outline-primary.loading {
    pointer-events: none;
    opacity: 0.6;
}

.btn-outline-primary.loading i.fas {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}



/* 按钮图标动效 */
.batch-operations .btn i {
    transition: all 0.3s ease;
}

.batch-operations .btn:hover i {
    transform: scale(1.1);
}

/* 下拉菜单美化 */
.batch-operations .dropdown-toggle::after {
    margin-left: 0.5rem;
    transition: transform 0.3s ease;
}

.batch-operations .dropdown-toggle[aria-expanded="true"]::after {
    transform: rotate(180deg);
}

.batch-operations .dropdown-menu {
    border: none;
    border-radius: 8px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    padding: 0.5rem 0;
    margin-top: 0.5rem;
}

.batch-operations .dropdown-item {
    padding: 0.6rem 1.2rem;
    transition: all 0.2s ease;
    border-radius: 0;
}

.batch-operations .dropdown-item:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    transform: translateX(5px);
}

.batch-operations .dropdown-item i {
    width: 20px;
    text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .batch-operations {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }
    
    .batch-operations .btn {
        width: 100%;
        text-align: center;
    }
}

/* 按钮加载状态 */
.batch-operations .btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

.batch-operations .btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: button-spin 1s linear infinite;
}

@keyframes button-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 表格信息栏样式 */
.table-info {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    color: #6c757d;
}

.table-info i {
    color: #007bff;
}


.no-updates-badge {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    color: white;
    font-size: 0.6rem;
    padding: 3px 6px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(108, 117, 125, 0.25);
    font-weight: 500;
    margin-left: 3px;
    display: inline-flex;
    align-items: center;
    gap: 2px;
}

.has-updates-badge {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    font-size: 0.6rem;
    padding: 3px 6px;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(220, 53, 69, 0.25);
    animation: pulse-update 2s infinite;
    font-weight: 600;
    margin-left: 3px;
    display: inline-flex;
    align-items: center;
    gap: 2px;
}

@keyframes pulse-update {
    0%, 100% { 
        opacity: 1; 
        transform: scale(1);
    }
    50% { 
        opacity: 0.8; 
        transform: scale(0.95);
    }
}

.has-updates-badge::before {
    content: '';
    width: 4px;
    height: 4px;
    background: white;
    border-radius: 50%;
    animation: blink 1.5s infinite;
}

@keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0; }
}

/* 移除文字截断样式 */
.text-truncate {
    overflow: visible !important;
    text-overflow: unset !important;
    white-space: normal !important;
}

/* 🔄 新增：多选筛选器样式 */
.multi-select-container {
    position: relative;
    display: inline-block;
    width: 100%;
}

.multi-select-button {
    width: 100%;
    background: white;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    line-height: 1.5;
    color: #495057;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;
    min-height: 38px;
}

.multi-select-button:hover {
    border-color: #007bff;
}

.multi-select-button:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.multi-select-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ced4da;
    border-top: none;
    border-radius: 0 0 0.375rem 0.375rem;
    max-height: 300px;
    z-index: 1000;
    display: none;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.multi-select-dropdown.show {
    display: block;
}

/* 🔄 新增：搜索框和全选按钮容器 */
.multi-select-header {
    padding: 0.5rem 0.75rem;
    border-bottom: 1px solid #e9ecef;
    background-color: #f8f9fa;
}

.multi-select-search {
    width: 100%;
    padding: 0.25rem 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    margin-bottom: 0.5rem;
}

.multi-select-search:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.1rem rgba(0, 123, 255, 0.25);
}

.multi-select-actions {
    display: flex;
    gap: 0.5rem;
}

.multi-select-btn {
    flex: 1;
    padding: 0.25rem 0.5rem;
    font-size: 0.7rem;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    background: white;
    cursor: pointer;
    text-align: center;
    transition: all 0.15s ease;
}

.multi-select-btn:hover {
    background-color: #e9ecef;
    border-color: #007bff;
}

/* 选项容器 */
.multi-select-options {
    max-height: 200px;
    overflow-y: auto;
}

.multi-select-option {
    padding: 0.5rem 0.75rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    transition: background-color 0.15s ease;
}

.multi-select-option:hover {
    background-color: #f8f9fa;
}

.multi-select-option.hidden {
    display: none;
}

.multi-select-option input[type="checkbox"] {
    margin: 0;
    cursor: pointer;
}

.multi-select-option label {
    cursor: pointer;
    margin: 0;
    flex: 1;
}

.multi-select-selected-count {
    background: #007bff;
    color: white;
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    margin-left: 0.5rem;
}

.multi-select-placeholder {
    color: #6c757d;
}

.multi-select-arrow {
    transition: transform 0.15s ease;
}

.multi-select-arrow.rotated {
    transform: rotate(180deg);
}

/* 🔄 "有更新"标签样式 */
.has-updates-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.25rem 0.5rem;
    font-size: 0.65rem;
    font-weight: 600;
    color: #ffffff;
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border: 1px solid #dc3545;
    border-radius: 0.375rem;
    white-space: nowrap;
    min-width: 50px;
    text-align: center;
    box-shadow: 0 2px 6px rgba(220, 53, 69, 0.3);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    animation: updatePulse 2s ease-in-out infinite;
    position: relative;
    overflow: hidden;
}

.has-updates-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.has-updates-badge:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
    background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
}

.has-updates-badge:hover::before {
    left: 100%;
}

/* 脉动动画效果 */
@keyframes updatePulse {
    0%, 100% {
        opacity: 1;
        box-shadow: 0 2px 6px rgba(220, 53, 69, 0.3);
    }
    50% {
        opacity: 0.8;
        box-shadow: 0 2px 6px rgba(220, 53, 69, 0.5), 0 0 8px rgba(220, 53, 69, 0.4);
    }
}

/* AI状态徽章样式 */
.ai-status-badge {
    font-size: 0.65rem;
    padding: 0.2rem 0.5rem;
    border-radius: 0.25rem;
    white-space: nowrap;
    display: inline-block;
    margin: 2px;
    min-width: 60px;
    text-align: center;
    font-weight: 500;
}

.ai-status-badge i {
    font-size: 0.6rem;
}

.ai-status-badge[data-status="processing"] {
    background-color: #ffc107 !important;
    color: #212529 !important;
    animation: pulse 1.5s ease-in-out infinite;
}

.ai-status-badge[data-status="completed"] {
    background-color: #28a745 !important;
    color: white !important;
}

.ai-status-badge[data-status="error"] {
    background-color: #dc3545 !important;
    color: white !important;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

/* 响应式调整 */
@media (max-width: 768px) {
    .has-updates-badge {
        font-size: 0.6rem;
        padding: 0.2rem 0.4rem;
        min-width: 45px;
    }
    
    .ai-status-badge {
        font-size: 0.6rem;
        padding: 0.15rem 0.3rem;
        min-width: 50px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h2 mb-3">
            <i class="fas fa-exclamation-circle me-2"></i>议题管理
        </h1>
        <p class="text-muted">查看和管理所有系统议题。</p>
    </div>
</div>

<!-- 筛选器表单 -->
<form method="GET" id="filterForm" class="mb-4">
    <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
            <h6 class="mb-0"><i class="fas fa-filter me-2"></i>筛选条件</h6>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <!-- 第一行：基础信息筛选 -->
                <div class="col-md-3">
                    <label class="form-label">项目编号</label>
                    <div class="multi-select-container">
                        <div class="multi-select-button" onclick="toggleMultiSelect('project_number')">
                            <span class="multi-select-text">
                                {% if current_filters.project_number %}
                                    已选择 {{ current_filters.project_number|length }} 项
                                {% else %}
                                    全部项目编号
                                {% endif %}
                            </span>
                            <i class="fas fa-chevron-down multi-select-arrow"></i>
                        </div>
                        <div class="multi-select-dropdown" id="project_number_dropdown">
                            <div class="multi-select-header">
                                <input type="text" class="multi-select-search" placeholder="搜索项目编号..." onkeyup="filterOptions('project_number', this.value)">
                                <div class="multi-select-actions">
                                    <button type="button" class="multi-select-btn" onclick="selectAll('project_number')">全选</button>
                                    <button type="button" class="multi-select-btn" onclick="unselectAll('project_number')">清空</button>
                                </div>
                            </div>
                            <div class="multi-select-options">
                                {% for project_number in filter_options.unique_project_numbers %}
                                <div class="multi-select-option" data-value="{{ project_number.lower() }}">
                                    <input type="checkbox" name="project_number" value="{{ project_number }}" 
                                           id="project_number_{{ loop.index }}"
                                           {% if project_number in current_filters.project_number %}checked{% endif %}>
                                    <label for="project_number_{{ loop.index }}">{{ project_number }}</label>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
                
                {% if current_user.role.name in ['ADMIN', 'MANAGER'] or current_user.is_admin %}
                <div class="col-md-3">
                    <label class="form-label">部门</label>
                    <div class="multi-select-container">
                        <div class="multi-select-button" onclick="toggleMultiSelect('department')">
                            <span class="multi-select-text">
                                {% if current_filters.department %}
                                    已选择 {{ current_filters.department|length }} 项
                                {% else %}
                                    全部部门
                                {% endif %}
                            </span>
                            <i class="fas fa-chevron-down multi-select-arrow"></i>
                        </div>
                        <div class="multi-select-dropdown" id="department_dropdown">
                            <div class="multi-select-header">
                                <input type="text" class="multi-select-search" placeholder="搜索部门..." onkeyup="filterOptions('department', this.value)">
                                <div class="multi-select-actions">
                                    <button type="button" class="multi-select-btn" onclick="selectAll('department')">全选</button>
                                    <button type="button" class="multi-select-btn" onclick="unselectAll('department')">清空</button>
                                </div>
                            </div>
                            <div class="multi-select-options">
                                {% for department in filter_options.unique_departments %}
                                <div class="multi-select-option" data-value="{{ department.lower() }}">
                                    <input type="checkbox" name="department" value="{{ department }}" 
                                           id="department_{{ loop.index }}"
                                           {% if department in current_filters.department %}checked{% endif %}>
                                    <label for="department_{{ loop.index }}">{{ department }}</label>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                <div class="col-md-3">
                    <label class="form-label">工程师</label>
                    <div class="multi-select-container">
                        <div class="multi-select-button" onclick="toggleMultiSelect('engineer')">
                            <span class="multi-select-text">
                                {% if current_filters.engineer %}
                                    已选择 {{ current_filters.engineer|length }} 项
                                {% else %}
                                    全部工程师
                                {% endif %}
                            </span>
                            <i class="fas fa-chevron-down multi-select-arrow"></i>
                        </div>
                        <div class="multi-select-dropdown" id="engineer_dropdown">
                            <div class="multi-select-header">
                                <input type="text" class="multi-select-search" placeholder="搜索工程师..." onkeyup="filterOptions('engineer', this.value)">
                                <div class="multi-select-actions">
                                    <button type="button" class="multi-select-btn" onclick="selectAll('engineer')">全选</button>
                                    <button type="button" class="multi-select-btn" onclick="unselectAll('engineer')">清空</button>
                                </div>
                            </div>
                            <div class="multi-select-options">
                                {% for engineer in filter_options.unique_engineers %}
                                <div class="multi-select-option" data-value="{{ engineer.lower() }}">
                                    <input type="checkbox" name="engineer" value="{{ engineer }}" 
                                           id="engineer_{{ loop.index }}"
                                           {% if engineer in current_filters.engineer %}checked{% endif %}>
                                    <label for="engineer_{{ loop.index }}">{{ engineer }}</label>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">KDNR</label>
                    <div class="multi-select-container">
                        <div class="multi-select-button" onclick="toggleMultiSelect('kdnr')">
                            <span class="multi-select-text">
                                {% if current_filters.kdnr %}
                                    已选择 {{ current_filters.kdnr|length }} 项
                                {% else %}
                                    全部KDNR
                                {% endif %}
                            </span>
                            <i class="fas fa-chevron-down multi-select-arrow"></i>
                        </div>
                        <div class="multi-select-dropdown" id="kdnr_dropdown">
                            <div class="multi-select-header">
                                <input type="text" class="multi-select-search" placeholder="搜索KDNR..." onkeyup="filterOptions('kdnr', this.value)">
                                <div class="multi-select-actions">
                                    <button type="button" class="multi-select-btn" onclick="selectAll('kdnr')">全选</button>
                                    <button type="button" class="multi-select-btn" onclick="unselectAll('kdnr')">清空</button>
                                </div>
                            </div>
                            <div class="multi-select-options">
                                {% for kdnr in filter_options.unique_kdnrs %}
                                <div class="multi-select-option" data-value="{{ kdnr.lower() }}">
                                    <input type="checkbox" name="kdnr" value="{{ kdnr }}" 
                                           id="kdnr_{{ loop.index }}"
                                           {% if kdnr in current_filters.kdnr %}checked{% endif %}>
                                    <label for="kdnr_{{ loop.index }}">{{ kdnr }}</label>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row g-3 mt-2">
                <!-- 第二行：状态和分类筛选 -->
                <div class="col-md-3">
                    <label class="form-label">议题状态</label>
                    <div class="multi-select-container">
                        <div class="multi-select-button" onclick="toggleMultiSelect('status')">
                            <span class="multi-select-text">
                                {% if current_filters.status %}
                                    已选择 {{ current_filters.status|length }} 项
                                {% else %}
                                    全部状态
                                {% endif %}
                            </span>
                            <i class="fas fa-chevron-down multi-select-arrow"></i>
                        </div>
                        <div class="multi-select-dropdown" id="status_dropdown">
                            <div class="multi-select-header">
                                <input type="text" class="multi-select-search" placeholder="搜索状态..." onkeyup="filterOptions('status', this.value)">
                                <div class="multi-select-actions">
                                    <button type="button" class="multi-select-btn" onclick="selectAll('status')">全选</button>
                                    <button type="button" class="multi-select-btn" onclick="unselectAll('status')">清空</button>
                                </div>
                            </div>
                            <div class="multi-select-options">
                                {% for status in issue_statuses %}
                                <div class="multi-select-option" data-value="{{ status.chinese_label.lower() }}">
                                    <input type="checkbox" name="status" value="{{ status.name }}" 
                                           id="status_{{ loop.index }}"
                                           {% if status.name in current_filters.status %}checked{% endif %}>
                                    <label for="status_{{ loop.index }}">{{ status.chinese_label }}</label>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">维护状态</label>
                    <div class="multi-select-container">
                        <div class="multi-select-button" onclick="toggleMultiSelect('maintenance_status')">
                            <span class="multi-select-text">
                                {% if current_filters.maintenance_status %}
                                    已选择 {{ current_filters.maintenance_status|length }} 项
                                {% else %}
                                    全部维护状态
                                {% endif %}
                            </span>
                            <i class="fas fa-chevron-down multi-select-arrow"></i>
                        </div>
                        <div class="multi-select-dropdown" id="maintenance_status_dropdown">
                            <div class="multi-select-header">
                                <input type="text" class="multi-select-search" placeholder="搜索维护状态..." onkeyup="filterOptions('maintenance_status', this.value)">
                                <div class="multi-select-actions">
                                    <button type="button" class="multi-select-btn" onclick="selectAll('maintenance_status')">全选</button>
                                    <button type="button" class="multi-select-btn" onclick="unselectAll('maintenance_status')">清空</button>
                                </div>
                            </div>
                            <div class="multi-select-options">
                                {% for status in maintenance_statuses %}
                                <div class="multi-select-option" data-value="{{ status.chinese_label.lower() }}">
                                    <input type="checkbox" name="maintenance_status" value="{{ status.name }}" 
                                           id="maintenance_status_{{ loop.index }}"
                                           {% if status.name in current_filters.maintenance_status %}checked{% endif %}>
                                    <label for="maintenance_status_{{ loop.index }}">{{ status.chinese_label }}</label>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">抱怨类别</label>
                    <div class="multi-select-container">
                        <div class="multi-select-button" onclick="toggleMultiSelect('complaint_category')">
                            <span class="multi-select-text">
                                {% if current_filters.complaint_category %}
                                    已选择 {{ current_filters.complaint_category|length }} 项
                                {% else %}
                                    全部类别
                                {% endif %}
                            </span>
                            <i class="fas fa-chevron-down multi-select-arrow"></i>
                        </div>
                        <div class="multi-select-dropdown" id="complaint_category_dropdown">
                            <div class="multi-select-header">
                                <input type="text" class="multi-select-search" placeholder="搜索类别..." onkeyup="filterOptions('complaint_category', this.value)">
                                <div class="multi-select-actions">
                                    <button type="button" class="multi-select-btn" onclick="selectAll('complaint_category')">全选</button>
                                    <button type="button" class="multi-select-btn" onclick="unselectAll('complaint_category')">清空</button>
                                </div>
                            </div>
                            <div class="multi-select-options">
                                {% for category in complaint_categories %}
                                <div class="multi-select-option" data-value="{{ category.chinese_label.lower() }}">
                                    <input type="checkbox" name="complaint_category" value="{{ category.name }}" 
                                           id="complaint_category_{{ loop.index }}"
                                           {% if category.name in current_filters.complaint_category %}checked{% endif %}>
                                    <label for="complaint_category_{{ loop.index }}">{{ category.chinese_label }}</label>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">车辆状态</label>
                    <div class="multi-select-container">
                        <div class="multi-select-button" onclick="toggleMultiSelect('vehicle_status')">
                            <span class="multi-select-text">
                                {% if current_filters.vehicle_status %}
                                    已选择 {{ current_filters.vehicle_status|length }} 项
                                {% else %}
                                    全部车辆状态
                                {% endif %}
                            </span>
                            <i class="fas fa-chevron-down multi-select-arrow"></i>
                        </div>
                        <div class="multi-select-dropdown" id="vehicle_status_dropdown">
                            <div class="multi-select-header">
                                <input type="text" class="multi-select-search" placeholder="搜索车辆状态..." onkeyup="filterOptions('vehicle_status', this.value)">
                                <div class="multi-select-actions">
                                    <button type="button" class="multi-select-btn" onclick="selectAll('vehicle_status')">全选</button>
                                    <button type="button" class="multi-select-btn" onclick="unselectAll('vehicle_status')">清空</button>
                                </div>
                            </div>
                            <div class="multi-select-options">
                                {% for status in vehicle_statuses %}
                                <div class="multi-select-option" data-value="{{ status.chinese_label.lower() }}">
                                    <input type="checkbox" name="vehicle_status" value="{{ status.name }}" 
                                           id="vehicle_status_{{ loop.index }}"
                                           {% if status.name in current_filters.vehicle_status %}checked{% endif %}>
                                    <label for="vehicle_status_{{ loop.index }}">{{ status.chinese_label }}</label>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row g-3 mt-2">
                <!-- 第三行：布尔值和上会情况筛选 -->
                <div class="col-md-3">
                    <label class="form-label">抛锚</label>
                    <div class="multi-select-container">
                        <div class="multi-select-button" onclick="toggleMultiSelect('breakdown')">
                            <span class="multi-select-text">
                                {% if current_filters.breakdown %}
                                    已选择 {{ current_filters.breakdown|length }} 项
                                {% else %}
                                    全部
                                {% endif %}
                            </span>
                            <i class="fas fa-chevron-down multi-select-arrow"></i>
                        </div>
                        <div class="multi-select-dropdown" id="breakdown_dropdown">
                            <div class="multi-select-header">
                                <div class="multi-select-actions">
                                    <button type="button" class="multi-select-btn" onclick="selectAll('breakdown')">全选</button>
                                    <button type="button" class="multi-select-btn" onclick="unselectAll('breakdown')">清空</button>
                                </div>
                            </div>
                            <div class="multi-select-options">
                                <div class="multi-select-option" data-value="是">
                                    <input type="checkbox" name="breakdown" value="true" 
                                           id="breakdown_true"
                                           {% if "true" in current_filters.breakdown %}checked{% endif %}>
                                    <label for="breakdown_true">是</label>
                                </div>
                                <div class="multi-select-option" data-value="否">
                                    <input type="checkbox" name="breakdown" value="false" 
                                           id="breakdown_false"
                                           {% if "false" in current_filters.breakdown %}checked{% endif %}>
                                    <label for="breakdown_false">否</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">问题是否上会</label>
                    <div class="multi-select-container">
                        <div class="multi-select-button" onclick="toggleMultiSelect('problem_in_meeting')">
                            <span class="multi-select-text">
                                {% if current_filters.problem_in_meeting %}
                                    已选择 {{ current_filters.problem_in_meeting|length }} 项
                                {% else %}
                                    全部
                                {% endif %}
                            </span>
                            <i class="fas fa-chevron-down multi-select-arrow"></i>
                        </div>
                        <div class="multi-select-dropdown" id="problem_in_meeting_dropdown">
                            <div class="multi-select-header">
                                <div class="multi-select-actions">
                                    <button type="button" class="multi-select-btn" onclick="selectAll('problem_in_meeting')">全选</button>
                                    <button type="button" class="multi-select-btn" onclick="unselectAll('problem_in_meeting')">清空</button>
                                </div>
                            </div>
                            <div class="multi-select-options">
                                <div class="multi-select-option" data-value="是">
                                    <input type="checkbox" name="problem_in_meeting" value="true" 
                                           id="problem_in_meeting_true"
                                           {% if "true" in current_filters.problem_in_meeting %}checked{% endif %}>
                                    <label for="problem_in_meeting_true">是</label>
                                </div>
                                <div class="multi-select-option" data-value="否">
                                    <input type="checkbox" name="problem_in_meeting" value="false" 
                                           id="problem_in_meeting_false"
                                           {% if "false" in current_filters.problem_in_meeting %}checked{% endif %}>
                                    <label for="problem_in_meeting_false">否</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">抱怨是否上会</label>
                    <div class="multi-select-container">
                        <div class="multi-select-button" onclick="toggleMultiSelect('complaint_in_meeting')">
                            <span class="multi-select-text">
                                {% if current_filters.complaint_in_meeting %}
                                    已选择 {{ current_filters.complaint_in_meeting|length }} 项
                                {% else %}
                                    全部
                                {% endif %}
                            </span>
                            <i class="fas fa-chevron-down multi-select-arrow"></i>
                        </div>
                        <div class="multi-select-dropdown" id="complaint_in_meeting_dropdown">
                            <div class="multi-select-header">
                                <div class="multi-select-actions">
                                    <button type="button" class="multi-select-btn" onclick="selectAll('complaint_in_meeting')">全选</button>
                                    <button type="button" class="multi-select-btn" onclick="unselectAll('complaint_in_meeting')">清空</button>
                                </div>
                            </div>
                            <div class="multi-select-options">
                                <div class="multi-select-option" data-value="是">
                                    <input type="checkbox" name="complaint_in_meeting" value="true" 
                                           id="complaint_in_meeting_true"
                                           {% if "true" in current_filters.complaint_in_meeting %}checked{% endif %}>
                                    <label for="complaint_in_meeting_true">是</label>
                                </div>
                                <div class="multi-select-option" data-value="否">
                                    <input type="checkbox" name="complaint_in_meeting" value="false" 
                                           id="complaint_in_meeting_false"
                                           {% if "false" in current_filters.complaint_in_meeting %}checked{% endif %}>
                                    <label for="complaint_in_meeting_false">否</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">BANR</label>
                    <div class="multi-select-container">
                        <div class="multi-select-button" onclick="toggleMultiSelect('banr')">
                            <span class="multi-select-text">
                                {% if current_filters.banr %}
                                    已选择 {{ current_filters.banr|length }} 项
                                {% else %}
                                    全部BANR
                                {% endif %}
                            </span>
                            <i class="fas fa-chevron-down multi-select-arrow"></i>
                        </div>
                        <div class="multi-select-dropdown" id="banr_dropdown">
                            <div class="multi-select-header">
                                <input type="text" class="multi-select-search" placeholder="搜索BANR..." onkeyup="filterOptions('banr', this.value)">
                                <div class="multi-select-actions">
                                    <button type="button" class="multi-select-btn" onclick="selectAll('banr')">全选</button>
                                    <button type="button" class="multi-select-btn" onclick="unselectAll('banr')">清空</button>
                                </div>
                            </div>
                            <div class="multi-select-options">
                                {% for banr in filter_options.unique_banrs %}
                                <div class="multi-select-option" data-value="{{ banr.lower() }}">
                                    <input type="checkbox" name="banr" value="{{ banr }}" 
                                           id="banr_{{ loop.index }}"
                                           {% if banr in current_filters.banr %}checked{% endif %}>
                                    <label for="banr_{{ loop.index }}">{{ banr }}</label>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row g-3 mt-2">
                <!-- 第四行：时间筛选 -->
                <div class="col-md-3">
                    <label for="created_date_from" class="form-label">创建日期（从）</label>
                    <input type="date" class="form-control" name="created_date_from" id="created_date_from" 
                           value="{{ current_filters.created_date_from or '' }}">
                </div>
                <div class="col-md-3">
                    <label for="created_date_to" class="form-label">创建日期（到）</label>
                    <input type="date" class="form-control" name="created_date_to" id="created_date_to" 
                           value="{{ current_filters.created_date_to or '' }}">
                </div>
                <div class="col-md-3">
                    <label for="problem_report_week" class="form-label">问题上报周次</label>
                    <input type="number" class="form-control" name="problem_report_week" id="problem_report_week" 
                           value="{{ current_filters.problem_report_week or '' }}" 
                           placeholder="输入周次">
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">记录更新</label>
                    <div class="multi-select-container">
                        <div class="multi-select-button" onclick="toggleMultiSelect('has_updates')">
                            <span class="multi-select-text">
                                {% if current_filters.has_updates %}
                                    已选择 {{ current_filters.has_updates|length }} 项
                                {% else %}
                                    全部
                                {% endif %}
                            </span>
                            <i class="fas fa-chevron-down multi-select-arrow"></i>
                        </div>
                        <div class="multi-select-dropdown" id="has_updates_dropdown">
                            <div class="multi-select-header">
                                <div class="multi-select-actions">
                                    <button type="button" class="multi-select-btn" onclick="selectAll('has_updates')">全选</button>
                                    <button type="button" class="multi-select-btn" onclick="unselectAll('has_updates')">清空</button>
                                </div>
                            </div>
                            <div class="multi-select-options">
                                <div class="multi-select-option" data-value="已更新">
                                    <input type="checkbox" name="has_updates" value="true" 
                                           id="has_updates_true"
                                           {% if "true" in current_filters.has_updates %}checked{% endif %}>
                                    <label for="has_updates_true">已更新</label>
                                </div>
                                <div class="multi-select-option" data-value="未更新">
                                    <input type="checkbox" name="has_updates" value="false" 
                                           id="has_updates_false"
                                           {% if "false" in current_filters.has_updates %}checked{% endif %}>
                                    <label for="has_updates_false">未更新</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row g-3 mt-2">
                <!-- 第五行：关键字搜索（移到最下面） -->
                <div class="col-md-9">
                    <label for="q" class="form-label">关键字搜索</label>
                    <input type="text" class="form-control" name="q" id="q" 
                           value="{{ current_filters.q or '' }}" 
                           placeholder="输入Anlauf编号、项目编号、问题描述、工程师等关键字">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <div class="d-flex gap-2 w-100">
                        <button type="submit" class="btn btn-primary flex-fill">
                            <i class="fas fa-search me-1"></i>搜索
                        </button>
                        <a href="{{ url_for('issues.list_issues') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-undo me-1"></i>重置
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 保留分页参数 -->
    <input type="hidden" name="page" value="1">
</form>

<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title mb-0">议题列表</h5>
                        {% if pagination and pagination.total > 0 %}
                        <small class="text-muted">
                            共 {{ pagination.total }} 条记录
                            {% if pagination.pages > 1 %}
                            （当前第 {{ pagination.page }}/{{ pagination.pages }} 页）
                            {% endif %}
                        </small>
                        {% endif %}
                    </div>
                    <div class="batch-operations">
                        {% if issues %}
                        <!-- 仅项目负责人和管理员可见的批量操作 -->
                        {% if current_user.role.name in ['ADMIN', 'PROJECT_MANAGER'] or current_user.is_admin %}
                        <!-- 批量维护状态更新：放在左边 -->
                        <div class="btn-group">
                            <button type="button" class="btn btn-outline-info dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-wrench me-2"></i>批量维护状态
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="batchUpdateMaintenanceStatus('COMPLETED')">
                                    <i class="fas fa-check me-2 text-success"></i>批量设为已维护
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="batchUpdateMaintenanceStatus('PENDING')">
                                    <i class="fas fa-clock me-2 text-warning"></i>批量设为待维护
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="batchUpdateMaintenanceStatus('ON_HOLD')">
                                    <i class="fas fa-pause me-2 text-secondary"></i>批量设为挂起
                                </a></li>
                            </ul>
                        </div>
                        
                        <!-- 批量邮件功能：放在右边 -->
                        <button type="button" class="btn btn-outline-warning" onclick="sendBatchEmails()" 
                                title="向筛选结果中的工程师发送议题处理邮件提醒">
                            <i class="fas fa-envelope me-2"></i>批量邮件
                        </button>
                        {% endif %}
                        
                        <button type="button" class="btn btn-outline-success" onclick="exportFilteredIssues()" 
                                title="导出所有符合当前筛选条件的议题详细信息（Excel格式）">
                            <i class="fas fa-download me-2"></i>批量导出
                        </button>
                        
                        <!-- AI问题归类下拉按钮 - 仅限管理员和项目负责人 -->
                        {% if current_user.role.name == 'ADMIN' or current_user.role.name == 'PROJECT_MANAGER' or current_user.is_admin %}
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary" onclick="aiClassifyIssues('{{ ai_provider }}')" 
                                    title="使用{{ '本地' if ai_provider == 'local' else '在线' }}AI大模型对议题列表中的售后故障描述进行自动归类和标签提取" id="aiClassifyBtn">
                                <i class="fas fa-brain me-2"></i>AI问题归类
                            </button>
                            <button type="button" class="btn btn-primary dropdown-toggle dropdown-toggle-split" 
                                    data-bs-toggle="dropdown" aria-expanded="false" id="aiClassifyDropdown">
                                <span class="visually-hidden">切换下拉菜单</span>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item{{ ' active' if ai_provider == 'local' else '' }}" href="#" onclick="aiClassifyIssues('local')">
                                    <i class="fas fa-server me-2"></i>本地模型 (localhost:1234)
                                    {% if ai_provider == 'local' %}<i class="fas fa-check ms-2 text-success"></i>{% endif %}
                                </a></li>
                                <li><a class="dropdown-item{{ ' active' if ai_provider == 'online' else '' }}" href="#" onclick="aiClassifyIssues('online')">
                                    <i class="fas fa-cloud me-2"></i>在线模型 (OpenAI/Azure)
                                    {% if ai_provider == 'online' %}<i class="fas fa-check ms-2 text-success"></i>{% endif %}
                                </a></li>
                            </ul>
                        </div>
                        {% endif %}
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 议题列表 -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-body p-0"> <!-- 移除padding让表格紧贴边界 -->
                {% if issues %}
                    <!-- 表格信息栏 -->
                    <div class="table-info d-flex justify-content-between align-items-center">
                        <span>
                            <i class="fas fa-info-circle me-2"></i>
                            提示：表格支持水平和垂直滚动，表头会保持固定
                        </span>
                        <span class="text-muted">
                            共 {{ issues|length }} 条记录
                        </span>
                    </div>
                    
                    <!-- 带滚动功能的表格容器 -->
                    <div class="table-responsive table-container" id="tableContainer">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th class="text-center">序号</th>
                                    <th class="text-center">Anlauf编号</th>
                                    <th class="text-center">项目编号</th>
                                    <th class="text-center">VIN码</th>
                                    <th class="text-center">售后故障描述</th>
                                    <th class="text-center">分析/措施</th>
                                    <th class="text-center">维修站号+中文名</th>
                                    <th class="text-center">上报时间</th>
                                    <th class="text-center">标记</th>
                                    <th class="text-center">问题标签</th>
                                    <th class="text-center">KDNR</th>
                                    <th class="text-center">车辆状态</th>
                                    <th class="text-center">议题状态</th>
                                    <th class="text-center">维护状态</th>
                                    <th class="text-center">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for issue in issues %}
                                <tr>
                                    <!-- 序号 -->
                                    <td class="text-center">
                                        {% if issue.sequence_number %}
                                            <span class="badge bg-info compact-badge">{{ issue.sequence_number }}</span>
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    
                                    <!-- Anlauf编号 -->
                                    <td class="text-center">
                                        <span class="badge bg-primary compact-badge">{{ issue.anlauf_number or issue.key_field or '-' }}</span>
                                    </td>
                                    
                                    <!-- 项目编号 -->
                                    <td class="text-center">{{ issue.project_number or issue.project or '-' }}</td>
                                    
                                    <!-- VIN码 -->
                                    <td class="text-center vin-cell">
                                        {{ issue.vin or issue.vehicle_vin or '-' }}
                                    </td>
                                    
                                    <!-- 售后故障描述 -->
                                    <td class="text-left problem-description-cell">
                                        {{ issue.after_sales_fault_description or issue.problem_description or issue.summary or '-' }}
                                    </td>

                                    <!-- 分析/措施 -->
                                    <td class="text-left analysis-measures-cell">
                                        <div class="analysis-measures-content">
                                            {{ issue.analysis_measures or '-' }}
                                        </div>
                                    </td>

                                    <!-- 维修站号+中文名 -->
                                    <td class="text-center">
                                        {{ issue.repair_station_info or '-' }}
                                    </td>

                                    <!-- 上报时间天数差 -->
                                    <td class="text-center days-diff-cell">
                                        {% if issue.problem_report_date %}
                                            <span class="days-diff" data-date="{{ issue.problem_report_date.strftime('%Y-%m-%d') }}">
                                                计算中...
                                            </span>
                                        {% elif issue.created_at %}
                                            <span class="days-diff text-muted" data-date="{{ issue.created_at.strftime('%Y-%m-%d') }}">
                                                计算中...
                                            </span>
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>

                                    <!-- 标记 -->
                                    <td class="text-center">
                                        {% if issue.mark %}
                                            <span class="badge bg-primary compact-badge">{{ issue.mark }}</span>
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    
                                    <!-- 问题标签 -->
                                    <td class="text-center tags-column">
                                        <div class="tags-container" id="tags-container-{{ issue.id }}">
                                            {% if issue.ai_tag_status == 'completed' %}
                                                {% for tag in issue.tags_relationship %}
                                                    <span class="tag-badge" style="background-color: {{ tag.color }}; color: white;" title="{{ tag.name }}">
                                                        {{ tag.name }}
                                                    </span>
                                                {% endfor %}
                                                <!-- 移除"标签已生成"状态显示，只显示实际的标签 -->
                                            {% elif issue.ai_tag_status == 'error' %}
                                                <span class="badge bg-danger ai-status-badge" data-issue-id="{{ issue.id }}" data-status="error" title="{{ issue.ai_tag_error_message or 'AI标签生成失败' }}">
                                                    <i class="fas fa-exclamation-triangle me-1"></i>生成失败
                                                </span>
                                            {% else %}
                                                <!-- 只显示现有标签，不显示processing状态（避免显示过时的"更新中"） -->
                                                {% for tag in issue.tags_relationship %}
                                                    <span class="tag-badge" style="background-color: {{ tag.color }}; color: white;" title="{{ tag.name }}">
                                                        {{ tag.name }}
                                                    </span>
                                                {% else %}
                                                    <span class="text-muted small">-</span>
                                                {% endfor %}
                                            {% endif %}
                                        </div>
                                    </td>
                                    
                                    <!-- KDNR -->
                                    <td class="text-center">
                                        {% if issue.kdnr %}
                                            <span class="badge bg-secondary compact-badge">{{ issue.kdnr }}</span>
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    

                                    
                                    <!-- 车辆状态 -->
                                    <td class="text-center">
                                        {% if issue.vehicle_status %}
                                            {% if issue.vehicle_status.name == 'PRE_SALE' %}
                                                <span class="badge bg-primary compact-badge">{{ issue.vehicle_status.chinese_label }}</span>
                                            {% elif issue.vehicle_status.name == 'TEST_DRIVE' %}
                                                <span class="badge bg-success compact-badge">{{ issue.vehicle_status.chinese_label }}</span>
                                            {% elif issue.vehicle_status.name == 'DISPLAY' %}
                                                <span class="badge bg-info compact-badge">{{ issue.vehicle_status.chinese_label }}</span>
                                            {% elif issue.vehicle_status.name == 'COMMERCIAL' %}
                                                <span class="badge bg-warning text-dark compact-badge">{{ issue.vehicle_status.chinese_label }}</span>
                                            {% else %}
                                                <span class="badge bg-secondary compact-badge">{{ issue.vehicle_status.chinese_label }}</span>
                                            {% endif %}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    

                                    
                                    <!-- 议题状态 -->
                                    <td class="text-center">
                                        {% if issue.issue_status %}
                                            <span class="issue-status-badge" style="background-color: {{ issue.issue_status.background_color }}; color: {{ issue.issue_status.text_color }};">
                                                <span class="status-text">{{ issue.issue_status.chinese_label }}</span>
                                            </span>
                                        {% else %}
                                            <span class="badge bg-secondary compact-badge">未设置</span>
                                        {% endif %}
                                    </td>
                                    
                                    <!-- 维护状态列 -->
                                    <td class="text-center">
                                        <div class="maintenance-status-container">
                                            <!-- 状态徽章 - 固定宽度 -->
                                            {% if issue.maintenance_status %}
                                                {% if issue.maintenance_status.name == 'COMPLETED' %}
                                                    <span class="badge bg-success maintenance-status-badge">
                                                        <i class="fas fa-check me-1"></i>{{ issue.maintenance_status.chinese_label }}
                                                    </span>
                                                {% elif issue.maintenance_status.name == 'PENDING' %}
                                                    <span class="badge bg-warning maintenance-status-badge">
                                                        <i class="fas fa-clock me-1"></i>{{ issue.maintenance_status.chinese_label }}
                                                    </span>
                                                {% elif issue.maintenance_status.name == 'ON_HOLD' %}
                                                    <span class="badge bg-secondary maintenance-status-badge">
                                                        <i class="fas fa-pause me-1"></i>{{ issue.maintenance_status.chinese_label }}
                                                    </span>
                                                {% endif %}
                                            {% else %}
                                                <span class="badge bg-light text-dark maintenance-status-badge">
                                                    <i class="fas fa-question me-1"></i>未设置
                                                </span>
                                            {% endif %}
                                            
                                            <!-- 快速操作按钮组 - 固定宽度与状态徽章一致 -->
                                            {% if current_user.role.name in ['ADMIN', 'PROJECT_MANAGER'] or current_user.is_admin %}
                                            <div class="btn-group maintenance-btn-group" role="group">
                                                <button type="button" class="btn btn-outline-success btn-sm" 
                                                        onclick="quickUpdateMaintenanceStatus({{ issue.id }}, 'COMPLETED')"
                                                        title="标记为已维护">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-warning btn-sm" 
                                                        onclick="quickUpdateMaintenanceStatus({{ issue.id }}, 'PENDING')"
                                                        title="标记为待维护">
                                                    <i class="fas fa-clock"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-secondary btn-sm" 
                                                        onclick="quickUpdateMaintenanceStatus({{ issue.id }}, 'ON_HOLD')"
                                                        title="标记为挂起">
                                                    <i class="fas fa-pause"></i>
                                                </button>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </td>
                                    
                                    <!-- 操作列 -->
                                    <td class="text-center">
                                        <div class="d-flex align-items-center justify-content-center gap-2">
                                            <a href="{{ url_for('issues.view_issue', issue_id=issue.id) }}" 
                                               class="btn btn-sm btn-outline-primary" title="查看详情">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            
                                            <!-- 已更新标签 -->
                                            {% if issue.has_updates %}
                                                <span class="has-updates-badge" title="此议题在最近的导入中有字段更新">
                                                    <i class="fas fa-exclamation-circle me-1"></i>已更新
                                                </span>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页导航移到表格外部 -->
                    {% if pagination and pagination.pages > 1 %}
                    <div class="p-3">
                        <nav aria-label="议题列表分页">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <small class="text-muted">
                                    显示第 {{ (pagination.page - 1) * pagination.per_page + 1 }} - 
                                    {{ pagination.page * pagination.per_page if pagination.page * pagination.per_page <= pagination.total else pagination.total }} 条，
                                    共 {{ pagination.total }} 条记录
                                </small>
                            </div>
                            <ul class="pagination pagination-sm mb-0">
                                <!-- 上一页 -->
                                <li class="page-item {{ 'disabled' if not pagination.has_prev else '' }}">
                                    {% if pagination.has_prev %}
                                    <a class="page-link" href="{{ url_for('issues.list_issues', page=pagination.prev_num, **current_filters) }}">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                    {% else %}
                                    <span class="page-link">
                                        <i class="fas fa-chevron-left"></i>
                                    </span>
                                    {% endif %}
                                </li>
                                
                                <!-- 页码 -->
                                {% for page_num in pagination.iter_pages() %}
                                    {% if page_num %}
                                        {% if page_num != pagination.page %}
                                            <li class="page-item">
                                                <a class="page-link" href="{{ url_for('issues.list_issues', page=page_num, **current_filters) }}">
                                                    {{ page_num }}
                                                </a>
                                            </li>
                                        {% else %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ page_num }}</span>
                                            </li>
                                        {% endif %}
                                    {% else %}
                                        <li class="page-item disabled">
                                            <span class="page-link">…</span>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                                
                                <!-- 下一页 -->
                                <li class="page-item {{ 'disabled' if not pagination.has_next else '' }}">
                                    {% if pagination.has_next %}
                                    <a class="page-link" href="{{ url_for('issues.list_issues', page=pagination.next_num, **current_filters) }}">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                    {% else %}
                                    <span class="page-link">
                                        <i class="fas fa-chevron-right"></i>
                                    </span>
                                    {% endif %}
                                </li>
                            </ul>
                        </div>
                        </nav>
                    </div>
                    {% endif %}
                    
                {% else %}
                    <!-- 空状态显示 -->
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">暂无议题</h4>
                        <p class="text-muted">目前没有符合条件的议题。</p>
                        <a href="{{ url_for('issues.create_issue') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>创建第一个议题
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
/**
 * 🔄 多选筛选器相关函数
 */
function toggleMultiSelect(fieldName) {
    const dropdown = document.getElementById(fieldName + '_dropdown');
    const arrow = dropdown.previousElementSibling.querySelector('.multi-select-arrow');
    
    // 关闭其他下拉菜单
    document.querySelectorAll('.multi-select-dropdown').forEach(dd => {
        if (dd.id !== fieldName + '_dropdown') {
            dd.classList.remove('show');
            const otherArrow = dd.previousElementSibling.querySelector('.multi-select-arrow');
            if (otherArrow) {
                otherArrow.classList.remove('rotated');
            }
        }
    });
    
    // 切换当前下拉菜单
    dropdown.classList.toggle('show');
    arrow.classList.toggle('rotated');
    
    // 更新显示文本
    updateMultiSelectText(fieldName);
}

function updateMultiSelectText(fieldName) {
    const dropdown = document.getElementById(fieldName + '_dropdown');
    const button = dropdown.previousElementSibling;
    const textElement = button.querySelector('.multi-select-text');
    
    const checkedBoxes = dropdown.querySelectorAll('input[type="checkbox"]:checked');
    const count = checkedBoxes.length;
    
    if (count === 0) {
        // 根据字段名设置默认文本
        const defaultTexts = {
            'project_number': '全部项目编号',
            'department': '全部部门',
            'engineer': '全部工程师',
            'kdnr': '全部KDNR',
            'status': '全部状态',
            'maintenance_status': '全部维护状态',
            'complaint_category': '全部类别',
            'vehicle_status': '全部车辆状态',
            'breakdown': '全部',
            'problem_in_meeting': '全部',
            'complaint_in_meeting': '全部',
            'banr': '全部BANR',
            'has_updates': '全部'
        };
        textElement.textContent = defaultTexts[fieldName] || '全部';
    } else if (count <= 3) {
        // 🔄 新增：当选中项少于等于3个时，显示具体的值
        const selectedValues = Array.from(checkedBoxes).map(checkbox => {
            const label = checkbox.nextElementSibling.textContent;
            return label.length > 8 ? label.substring(0, 8) + '...' : label;
        });
        textElement.textContent = selectedValues.join(', ');
    } else {
        // 选中项较多时显示数量
        textElement.textContent = `已选择 ${count} 项`;
    }
}

/**
 * 🔄 新增：搜索筛选功能
 */
function filterOptions(fieldName, searchValue) {
    const dropdown = document.getElementById(fieldName + '_dropdown');
    const options = dropdown.querySelectorAll('.multi-select-option');
    const searchLower = searchValue.toLowerCase();
    
    options.forEach(option => {
        const label = option.querySelector('label').textContent.toLowerCase();
        const value = option.getAttribute('data-value') || '';
        
        if (label.includes(searchLower) || value.includes(searchLower)) {
            option.classList.remove('hidden');
        } else {
            option.classList.add('hidden');
        }
    });
}

/**
 * 🔄 新增：全选功能
 */
function selectAll(fieldName) {
    const dropdown = document.getElementById(fieldName + '_dropdown');
    const visibleOptions = dropdown.querySelectorAll('.multi-select-option:not(.hidden)');
    
    visibleOptions.forEach(option => {
        const checkbox = option.querySelector('input[type="checkbox"]');
        checkbox.checked = true;
    });
    
    updateMultiSelectText(fieldName);
}

/**
 * 🔄 新增：清空选择功能
 */
function unselectAll(fieldName) {
    const dropdown = document.getElementById(fieldName + '_dropdown');
    const checkboxes = dropdown.querySelectorAll('input[type="checkbox"]');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    
    updateMultiSelectText(fieldName);
}

// 点击外部关闭下拉菜单
document.addEventListener('click', function(event) {
    if (!event.target.closest('.multi-select-container')) {
        document.querySelectorAll('.multi-select-dropdown').forEach(dropdown => {
            dropdown.classList.remove('show');
            const arrow = dropdown.previousElementSibling.querySelector('.multi-select-arrow');
            if (arrow) {
                arrow.classList.remove('rotated');
            }
        });
    }
});

// 复选框变化时更新显示文本
document.addEventListener('change', function(event) {
    if (event.target.type === 'checkbox' && event.target.name) {
        const fieldName = event.target.name;
        updateMultiSelectText(fieldName);
    }
});

/**
 * 🔄 新增：从URL参数初始化筛选器状态
 */
function initializeFiltersFromURL() {
    const urlParams = new URLSearchParams(window.location.search);
    console.log('URL参数:', Array.from(urlParams.entries()));
    
    // 处理多选筛选器
    const fieldNames = ['project_number', 'department', 'engineer', 'kdnr', 'status', 
                       'maintenance_status', 'complaint_category', 'vehicle_status', 
                       'breakdown', 'problem_in_meeting', 'complaint_in_meeting', 
                       'banr', 'has_updates'];
    
    fieldNames.forEach(fieldName => {
        const dropdown = document.getElementById(fieldName + '_dropdown');
        if (dropdown) {
            // 获取URL中的参数值
            const paramValues = urlParams.getAll(fieldName);
            console.log(`${fieldName}参数值:`, paramValues);
            
            if (paramValues.length > 0) {
                // 清空所有复选框
                dropdown.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
                    checkbox.checked = false;
                });
                
                // 根据URL参数设置复选框状态
                paramValues.forEach(value => {
                    const checkbox = dropdown.querySelector(`input[value="${value}"]`);
                    if (checkbox) {
                        checkbox.checked = true;
                        console.log(`选中 ${fieldName}: ${value}`);
                    } else {
                        console.log(`未找到 ${fieldName} 的值: ${value}`);
                    }
                });
            }
            
            // 更新显示文本
            updateMultiSelectText(fieldName);
        }
    });
    
    // 处理特殊参数
    // 处理在线状态参数
    if (urlParams.has('online_status')) {
        const onlineStatus = urlParams.get('online_status');
        console.log('在线状态参数:', onlineStatus);
        const statusDropdown = document.getElementById('status_dropdown');
        if (statusDropdown) {
            // 清空状态选择
            statusDropdown.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
                checkbox.checked = false;
            });
            
            // 根据在线状态设置相应的状态
            if (onlineStatus === 'true') {
                // 已上线状态：PRE_ANLAUF + 以MEETING_开头的状态（不包含NO_）
                const onlineStatuses = [
                    'PRE_ANLAUF',
                    'MEETING_ANALYZING',
                    'MEETING_ANALYSIS_COMPLETE', 
                    'MEETING_MEASURES_FORMULATING',
                    'MEETING_MEASURES_IMPLEMENTING',
                    'MEETING_CONTINUE_TRACKING'
                ];
                
                onlineStatuses.forEach(statusValue => {
                    const checkbox = statusDropdown.querySelector(`input[value="${statusValue}"]`);
                    if (checkbox) {
                        checkbox.checked = true;
                        console.log('选中已上线状态:', statusValue);
                    }
                });
            } else if (onlineStatus === 'false') {
                // 未上线状态：以NO_MEETING_开头的状态
                const offlineStatuses = [
                    'NO_MEETING_CONFIRMING',
                    'NO_MEETING_CONTINUE_TRACKING',
                    'NO_MEETING_INVALID_COMPLAINT',
                    'NO_MEETING_MEASURES_DEFINED',
                    'NO_MEETING_MEASURES_IMPLEMENTING'
                ];
                
                offlineStatuses.forEach(statusValue => {
                    const checkbox = statusDropdown.querySelector(`input[value="${statusValue}"]`);
                    if (checkbox) {
                        checkbox.checked = true;
                        console.log('选中未上线状态:', statusValue);
                    }
                });
            }
            
            updateMultiSelectText('status');
        }
    }
    
    // 处理优先级参数
    if (urlParams.has('priority')) {
        const priority = urlParams.get('priority');
        console.log('优先级参数:', priority);
        
        // 如果是紧急优先级，这里可以添加相应的处理逻辑
        // 例如：根据优先级筛选相关议题
        if (priority === 'URGENT') {
            console.log('检测到紧急优先级筛选');
            // 可以添加一个提示信息
            const filterInfo = document.createElement('div');
            filterInfo.className = 'alert alert-warning mt-2';
            filterInfo.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>正在显示紧急优先级议题';
            const filterForm = document.getElementById('filterForm');
            if (filterForm) {
                filterForm.insertBefore(filterInfo, filterForm.firstChild);
            }
        }
    }
    
    // 处理单值参数
    const singleValueFields = ['q', 'created_date_from', 'created_date_to', 'problem_report_week'];
    singleValueFields.forEach(fieldName => {
        const element = document.getElementById(fieldName);
        if (element && urlParams.has(fieldName)) {
            element.value = urlParams.get(fieldName);
            console.log(`设置 ${fieldName}:`, element.value);
        }
    });
    
    // 处理特殊的布尔类型参数
    const booleanFields = ['breakdown', 'problem_in_meeting', 'complaint_in_meeting', 'has_updates'];
    booleanFields.forEach(fieldName => {
        if (urlParams.has(fieldName)) {
            const value = urlParams.get(fieldName);
            console.log(`布尔参数 ${fieldName}:`, value);
            
            const dropdown = document.getElementById(fieldName + '_dropdown');
            if (dropdown) {
                // 清空选择
                dropdown.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
                    checkbox.checked = false;
                });
                
                // 设置对应的值
                const targetValue = value === 'true' ? 'true' : 'false';
                const checkbox = dropdown.querySelector(`input[value="${targetValue}"]`);
                if (checkbox) {
                    checkbox.checked = true;
                    console.log(`选中 ${fieldName}: ${targetValue}`);
                }
                
                updateMultiSelectText(fieldName);
            }
        }
    });
    
    console.log('URL参数初始化完成');
}

// 页面加载时初始化多选筛选器文本和URL参数
document.addEventListener('DOMContentLoaded', function() {
    // 首先从URL参数初始化筛选器状态
    initializeFiltersFromURL();
    
    // 然后初始化所有筛选器的显示文本
    const fieldNames = ['project_number', 'department', 'engineer', 'kdnr', 'status', 
                       'maintenance_status', 'complaint_category', 'vehicle_status', 
                       'breakdown', 'problem_in_meeting', 'complaint_in_meeting', 
                       'banr', 'has_updates'];
    
    fieldNames.forEach(fieldName => {
        const dropdown = document.getElementById(fieldName + '_dropdown');
        if (dropdown) {
            updateMultiSelectText(fieldName);
        }
    });
});

/**
 * 清空所有筛选条件
 */
function clearFilters() {
    // 清空关键字搜索
    document.getElementById('q').value = '';
    
    // 清空日期筛选
    document.getElementById('created_date_from').value = '';
    document.getElementById('created_date_to').value = '';
    document.getElementById('problem_report_week').value = '';
    
    // 清空所有多选筛选器
    document.querySelectorAll('.multi-select-dropdown input[type="checkbox"]').forEach(checkbox => {
        checkbox.checked = false;
    });
    
    // 更新所有多选筛选器的显示文本
    const fieldNames = ['project_number', 'department', 'engineer', 'kdnr', 'status', 
                       'maintenance_status', 'complaint_category', 'vehicle_status', 
                       'breakdown', 'problem_in_meeting', 'complaint_in_meeting', 
                       'banr', 'has_updates'];
    
    fieldNames.forEach(fieldName => {
        const dropdown = document.getElementById(fieldName + '_dropdown');
        if (dropdown) {
            updateMultiSelectText(fieldName);
        }
    });
    
    // 提交表单
    document.getElementById('filterForm').submit();
}

/**
 * 快速更新维护状态
 * @param {number} issueId - 议题ID
 * @param {string} status - 新的维护状态
 */
function quickUpdateMaintenanceStatus(issueId, status) {
    if (!confirm('确定要更新这个议题的维护状态吗？')) {
        return;
    }
    
    // 发送AJAX请求更新状态
    fetch(`/issues/${issueId}/maintenance-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            status: status
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 刷新页面以显示更新后的状态
            location.reload();
        } else {
            alert('更新失败：' + (data.message || '未知错误'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('更新失败，请稍后重试');
    });
}

/**
 * 批量更新维护状态
 * @param {string} status - 新的维护状态
 */
function batchUpdateMaintenanceStatus(status) {
    if (!confirm('确定要批量更新当前筛选结果中所有议题的维护状态吗？')) {
        return;
    }
    
    // 获取当前筛选参数
    const formData = new FormData(document.getElementById('filterForm'));
    const params = new URLSearchParams(formData);
    
    // 发送AJAX请求批量更新状态
    fetch(`/issues/batch-maintenance-status?${params.toString()}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            status: status
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`成功更新了 ${data.updated_count} 个议题的维护状态`);
            // 刷新页面以显示更新后的状态
            location.reload();
        } else {
            alert('批量更新失败：' + (data.message || '未知错误'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('批量更新失败，请稍后重试');
    });
}

/**
 * 发送批量邮件
 */
function sendBatchEmails() {
    if (!confirm('确定要向当前筛选结果中的议题负责人发送邮件通知吗？')) {
        return;
    }
    
    // 获取当前筛选参数
    const formData = new FormData(document.getElementById('filterForm'));
    const params = new URLSearchParams(formData);
    
    // 发送AJAX请求批量发送邮件
    fetch(`/issues/batch-emails?${params.toString()}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`成功发送了 ${data.sent_count} 封邮件`);
        } else {
            alert('批量发送邮件失败：' + (data.message || '未知错误'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('批量发送邮件失败，请稍后重试');
    });
}

/**
 * 导出筛选后的议题
 */
function exportFilteredIssues() {
    // 获取当前筛选条件
    const currentParams = new URLSearchParams(window.location.search);
    
    // 构建导出URL，包含所有筛选参数
    const exportUrl = `{{ url_for('issues.export_issues') }}?${currentParams.toString()}`;
    
    // 显示加载提示
    const loadingToast = $('<div class="toast position-fixed top-0 start-50 translate-middle-x" style="z-index: 9999;">' +
        '<div class="toast-body bg-primary text-white">' +
        '<i class="fas fa-spinner fa-spin me-2"></i>正在生成Excel文件，请稍候...' +
        '</div></div>');
    
    $('body').append(loadingToast);
    loadingToast.toast({ delay: 10000 }).toast('show');
    
    // 创建隐藏的下载链接
    const downloadLink = document.createElement('a');
    downloadLink.href = exportUrl;
    downloadLink.style.display = 'none';
    document.body.appendChild(downloadLink);
    
    // 触发下载
    downloadLink.click();
    
    // 清理
    document.body.removeChild(downloadLink);
    
    // 3秒后隐藏加载提示
    setTimeout(() => {
        loadingToast.toast('hide');
        setTimeout(() => loadingToast.remove(), 500);
    }, 3000);
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化表格滚动提示
    const tableContainer = document.getElementById('tableContainer');
    if (tableContainer) {
        // 检查是否需要滚动
        const needsHorizontalScroll = tableContainer.scrollWidth > tableContainer.clientWidth;
        const needsVerticalScroll = tableContainer.scrollHeight > tableContainer.clientHeight;
        
        if (needsHorizontalScroll || needsVerticalScroll) {
            // 可以在这里添加滚动提示逻辑
            console.log('表格支持滚动');
        }
    }
});

/**
 * AI问题归类功能
 * 使用大模型对议题列表中的问题描述进行自动归类和标签提取
 * @param {string} aiProvider - AI提供商类型 ('local' 或 'online')
 */
function aiClassifyIssues(aiProvider = 'local') {
    const btn = document.getElementById('aiClassifyBtn');
    const dropdownBtn = document.getElementById('aiClassifyDropdown');
    const btnIcon = btn.querySelector('i');
    
    // 确定提示文本
    const providerText = aiProvider === 'local' ? '本地模型 (localhost:1234)' : '在线模型 (OpenAI/Azure)';
    
    // 确认对话框
    if (!confirm(`AI问题归类将使用${providerText}分析当前筛选结果中所有议题的售后故障描述，为其自动生成分类标签。此过程可能需要几分钟时间，确定要继续吗？`)) {
        return;
    }
    
    // 设置加载状态
    btn.disabled = true;
    dropdownBtn.disabled = true;
    btn.classList.add('loading');
    btnIcon.className = 'fas fa-spinner fa-spin me-2';
    
    // 显示进度提示
    const progressToast = $('<div class="toast position-fixed top-0 start-50 translate-middle-x" style="z-index: 9999;">' +
        '<div class="toast-body bg-info text-white">' +
        `<i class="fas fa-${aiProvider === 'local' ? 'server' : 'cloud'} me-2"></i>AI正在使用${providerText}分析售后故障描述并生成标签，请耐心等待...` +
        '</div></div>');
    
    $('body').append(progressToast);
    progressToast.toast({ delay: 30000 }).toast('show');
    
    // 获取当前筛选条件
    const currentParams = new URLSearchParams(window.location.search);
    
    // 获取CSRF token（安全检查）
    const csrfTokenElement = document.querySelector('meta[name=csrf-token]');
    const csrfToken = csrfTokenElement ? csrfTokenElement.getAttribute('content') : '';
    
    if (!csrfToken) {
        console.warn('未找到CSRF token，可能会导致请求失败');
    }
    
    // 发送AI归类请求
    fetch('/api/ai-classify-issues', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({
            filters: Object.fromEntries(currentParams.entries()),
            ai_provider: aiProvider
        })
    })
    .then(response => response.json())
    .then(data => {
        // 隐藏进度提示
        progressToast.toast('hide');
        
        if (data.success) {
            // 先动态设置相关议题的状态为"更新中"
            console.log('AI分类任务启动成功，服务器返回的数据:', data);
            console.log('准备设置议题状态，issue_ids:', data.issue_ids);
            
            // 测试：手动设置当前页面可见的议题ID
            const visibleIssueIds = [];
            document.querySelectorAll('[id^="tags-container-"]').forEach(container => {
                const issueId = container.id.replace('tags-container-', '');
                visibleIssueIds.push(parseInt(issueId));
            });
            console.log('当前页面可见的议题ID:', visibleIssueIds);
            
            // 优先使用服务器返回的issue_ids，如果为空则使用可见的议题ID作为测试
            const idsToProcess = (data.issue_ids && data.issue_ids.length > 0) ? data.issue_ids : visibleIssueIds;
            console.log('最终要处理的议题ID:', idsToProcess);
            
            setIssuesProcessingStatus(idsToProcess);
            
            // 显示成功结果
            const resultMessage = `AI归类任务已启动！共处理 ${data.total_issues} 个议题，使用 ${data.ai_provider} 模型。`;
            
            const successToast = $('<div class="toast position-fixed top-0 start-50 translate-middle-x" style="z-index: 9999;">' +
                '<div class="toast-body bg-success text-white">' +
                '<i class="fas fa-check-circle me-2"></i>' + resultMessage +
                '</div></div>');
            
            $('body').append(successToast);
            successToast.toast({ delay: 5000 }).toast('show');
            
            // 启动实时状态监控
            startAIStatusMonitoring();
            
        } else {
            // 显示错误信息
            const errorToast = $('<div class="toast position-fixed top-0 start-50 translate-middle-x" style="z-index: 9999;">' +
                '<div class="toast-body bg-danger text-white">' +
                '<i class="fas fa-exclamation-triangle me-2"></i>AI归类启动失败: ' + (data.message || '未知错误') +
                '</div></div>');
            
            $('body').append(errorToast);
            errorToast.toast({ delay: 5000 }).toast('show');
        }
    })
    .catch(error => {
        // 隐藏进度提示
        progressToast.toast('hide');
        
        console.error('AI归类请求失败:', error);
        
        const errorToast = $('<div class="toast position-fixed top-0 start-50 translate-middle-x" style="z-index: 9999;">' +
            '<div class="toast-body bg-danger text-white">' +
            '<i class="fas fa-exclamation-triangle me-2"></i>网络错误，请稍后重试' +
            '</div></div>');
        
        $('body').append(errorToast);
        errorToast.toast({ delay: 5000 }).toast('show');
    })
    .finally(() => {
        // 恢复按钮状态
        btn.disabled = false;
        dropdownBtn.disabled = false;
        btn.classList.remove('loading');
        btnIcon.className = 'fas fa-brain me-2';
        
        // 清理进度提示
        setTimeout(() => progressToast.remove(), 1000);
    });
}

/**
 * 启动AI状态监控
 */
function startAIStatusMonitoring() {
    // 获取当前页面所有议题ID
    const issueIds = [];
    document.querySelectorAll('.ai-status-badge').forEach(badge => {
        const issueId = badge.getAttribute('data-issue-id');
        if (issueId) {
            issueIds.push(issueId);
        }
    });
    
    if (issueIds.length === 0) {
        console.log('没有找到需要监控的议题');
        return;
    }
    
    console.log('开始监控', issueIds.length, '个议题的AI状态');
    
    // 开始定期检查状态
    const statusInterval = setInterval(() => {
        checkAIStatus(issueIds, statusInterval);
    }, 5000); // 每5秒检查一次（减少频率）
    
    // 设置超时，180秒后停止监控（延长到3分钟）
    setTimeout(() => {
        clearInterval(statusInterval);
        console.log('AI状态监控已停止（超时）');
        
        // 显示超时提示
        const timeoutToast = $('<div class="toast position-fixed top-0 start-50 translate-middle-x" style="z-index: 9999;">' +
            '<div class="toast-body bg-warning text-dark">' +
            '<i class="fas fa-clock me-2"></i>AI处理时间较长，请稍后手动刷新页面查看结果' +
            '</div></div>');
        
        $('body').append(timeoutToast);
        timeoutToast.toast({ delay: 8000 }).toast('show');
    }, 180000);
}

/**
 * 检查AI状态
 */
function checkAIStatus(issueIds, statusInterval) {
    fetch(`/api/ai-classification-status?issue_ids=${issueIds.join(',')}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateAIStatusDisplay(data.issue_statuses);
                
                // 检查是否所有任务都完成
                const allCompleted = Object.values(data.issue_statuses).every(status => 
                    status.status === 'completed' || status.status === 'error'
                );
                
                if (allCompleted) {
                    clearInterval(statusInterval);
                    console.log('所有AI分类任务已完成');
                    
                    // 显示完成提示
                    const completeToast = $('<div class="toast position-fixed top-0 start-50 translate-middle-x" style="z-index: 9999;">' +
                        '<div class="toast-body bg-success text-white">' +
                        '<i class="fas fa-check-circle me-2"></i>AI标签生成完成！' +
                        '</div></div>');
                    
                    $('body').append(completeToast);
                    completeToast.toast({ delay: 3000 }).toast('show');
                }
            } else {
                console.error('获取AI状态失败:', data.message);
            }
        })
        .catch(error => {
            console.error('检查AI状态失败:', error);
        });
}

/**
 * 更新AI状态显示
 */
function updateAIStatusDisplay(issueStatuses) {
    Object.entries(issueStatuses).forEach(([issueId, statusInfo]) => {
        const container = document.getElementById(`tags-container-${issueId}`);
        if (!container) return;
        
        const currentBadge = container.querySelector('.ai-status-badge');
        if (!currentBadge) return;
        
        const currentStatus = currentBadge.getAttribute('data-status');
        const newStatus = statusInfo.status;
        
        // 如果状态没有变化，跳过
        if (currentStatus === newStatus) return;
        
        // 更新状态显示
        if (newStatus === 'completed') {
            // 重新加载这行内容以显示新标签
            window.location.reload();
        } else if (newStatus === 'error') {
            // 更新为错误状态
            currentBadge.className = 'badge bg-danger ai-status-badge';
            currentBadge.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i>生成失败';
            currentBadge.setAttribute('data-status', 'error');
            currentBadge.setAttribute('title', statusInfo.error_message || 'AI标签生成失败');
        }
    });
}

/**
 * 动态设置议题状态为"更新中"
 * @param {Array} issueIds - 需要设置状态的议题ID列表
 */
function setIssuesProcessingStatus(issueIds) {
    console.log('setIssuesProcessingStatus 被调用，参数:', issueIds);
    
    if (!Array.isArray(issueIds)) {
        console.warn('issueIds 不是数组:', issueIds);
        return;
    }
    
    if (issueIds.length === 0) {
        console.warn('issueIds 数组为空');
        return;
    }
    
    let successCount = 0;
    issueIds.forEach(issueId => {
        console.log(`处理议题 ID: ${issueId}`);
        const container = document.getElementById(`tags-container-${issueId}`);
        
        if (!container) {
            console.warn(`未找到议题 ${issueId} 的容器元素`);
            return;
        }
        
        // 清空当前容器
        container.innerHTML = '';
        
        // 添加"更新中"状态徽章
        const processingBadge = document.createElement('span');
        processingBadge.className = 'badge bg-warning text-dark ai-status-badge';
        processingBadge.setAttribute('data-issue-id', issueId);
        processingBadge.setAttribute('data-status', 'processing');
        processingBadge.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>更新中';
        
        container.appendChild(processingBadge);
        successCount++;
        console.log(`成功为议题 ${issueId} 添加"更新中"状态`);
    });
    
    console.log(`总共处理了 ${successCount} 个议题，设置为"更新中"状态`);
}

// 计算天数差并更新显示
function calculateDaysDiff() {
    const daysDiffElements = document.querySelectorAll('.days-diff');
    const today = new Date();
    today.setHours(0, 0, 0, 0); // 设置为当天0点

    daysDiffElements.forEach(element => {
        const dateStr = element.getAttribute('data-date');
        if (dateStr) {
            const targetDate = new Date(dateStr);
            targetDate.setHours(0, 0, 0, 0); // 设置为当天0点

            const timeDiff = today.getTime() - targetDate.getTime();
            const daysDiff = Math.floor(timeDiff / (1000 * 3600 * 24));

            // 根据天数差设置不同的样式和文本
            let badgeClass = '';
            let text = '';

            if (daysDiff === 0) {
                badgeClass = 'badge bg-success';
                text = '今天';
            } else if (daysDiff === 1) {
                badgeClass = 'badge bg-info';
                text = '1天';
            } else if (daysDiff <= 7) {
                badgeClass = 'badge bg-warning';
                text = `${daysDiff}天`;
            } else if (daysDiff <= 30) {
                badgeClass = 'badge bg-secondary';
                text = `${daysDiff}天`;
            } else {
                badgeClass = 'badge bg-danger';
                text = `${daysDiff}天`;
            }

            // 如果是创建时间（有text-muted类），保持灰色样式
            if (element.classList.contains('text-muted')) {
                badgeClass += ' text-muted';
            }

            element.className = badgeClass;
            element.textContent = text;
        }
    });
}

// 页面加载完成后计算天数差
document.addEventListener('DOMContentLoaded', function() {
    calculateDaysDiff();
});
</script>
{% endblock %}