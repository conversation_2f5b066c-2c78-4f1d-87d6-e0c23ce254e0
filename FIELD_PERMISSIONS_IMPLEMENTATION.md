# 字段编辑权限控制实现文档

## 概述

本文档描述了议题管理系统中基于角色的字段编辑权限控制功能的实现。该功能确保不同角色的用户只能编辑其有权限的字段，提高了系统的安全性和数据完整性。

## 权限分级

### 1. 系统只读字段
所有用户都不能编辑的字段：
- `project_number` - 项目编号
- `sequence_number` - 序号
- `anlauf_number` - Anlauf编号
- `created_at` - 创建日期
- `vin` - VIN码
- `mileage` - 里程
- `repair_station_info` - 维修站信息
- `after_sales_fault_description` - 售后故障描述
- `review_status` - 审核状态
- `engineer_first_review_time` - 工程师首次审核时间
- `engineer_first_review_week` - 工程师首次审核周次
- `zp8_delivery_date` - ZP8报交日期

### 2. 项目负责人专属字段
只有项目负责人和管理员可以编辑的字段：
- `problem_report_week` - 问题上报周次
- `problem_report_date` - 问题上报日期
- `issue_status` - 议题状态
- `problem_in_meeting` - 问题是否上会
- `complaint_in_meeting` - 抱怨是否上会

### 3. 项目负责人受限字段
项目负责人不能编辑，只有管理员可以编辑的字段：
- `department` - 部门
- `engineer` - 工程师
- `vehicle_status` - 车辆状态
- `is_rework_vehicle` - 是否返工车辆
- `kdnr` - KDNR
- `banr` - BANR
- `breakdown` - 抛锚
- `problem_description` - 问题描述
- `analysis_measures` - 分析/措施
- `problem_type` - 问题类型
- `fault_parts_status` - 故障件状态
- `warehouse_received_fault_parts` - 仓库收到故障件
- `mark` - 标记
- `meeting_topic_name` - 上会议题名

### 4. 普通字段
有议题编辑权限的用户可以编辑的字段：
- 其他业务字段...

## 角色权限

### 管理员 (ADMIN)
- 可以编辑所有字段（除了系统只读字段）
- 拥有最高权限

### 项目负责人 (PROJECT_MANAGER)
- 可以编辑项目负责人专属字段
- 不能编辑项目负责人受限字段
- 不能编辑系统只读字段
- 必须被分配到相应项目才能编辑该项目的议题

### 普通用户 (USER)
- 只能编辑基本字段（如果有权限）
- 不能编辑项目负责人专属字段
- 不能编辑项目负责人受限字段
- 不能编辑系统只读字段

## 技术实现

### 后端实现

#### 1. 权限检查函数
```python
def get_field_edit_permissions(user, issue):
    """获取用户对议题字段的编辑权限"""
    # 实现权限逻辑
    # 返回字段权限字典
```

#### 2. API权限验证
在 `update_field` API中添加权限检查：
```python
# 检查字段编辑权限
field_permissions = get_field_edit_permissions(current_user, issue)
if field not in field_permissions or not field_permissions[field]:
    return jsonify({'success': False, 'message': '您没有权限编辑字段'}), 403
```

### 前端实现

#### 1. 模板宏
```jinja2
{# 检查字段是否可编辑的宏 #}
{% macro is_field_editable(field_name) %}
    {%- if field_permissions is defined and field_permissions and field_permissions.get(field_name, False) -%}
        editable
    {%- else -%}
        readonly
    {%- endif -%}
{% endmacro %}
```

#### 2. CSS样式
```css
/* 只读字段样式 */
.readonly {
    background-color: #f8f9fa !important;
    color: #6c757d !important;
    cursor: not-allowed !important;
    opacity: 0.8;
    border: 1px solid #e9ecef !important;
}
```

#### 3. JavaScript控制
```javascript
// 双击编辑功能 - 只对可编辑字段启用
$('.editable').dblclick(function() {
    // 检查是否为只读字段
    if ($(this).hasClass('readonly')) {
        return;
    }
    // 执行编辑逻辑
});
```

## 视觉效果

### 可编辑字段
- 正常背景色
- 鼠标悬停时高亮
- 双击可进入编辑模式
- 提示文本："双击编辑此字段"

### 只读字段
- 灰色背景 (#f8f9fa)
- 灰色文字 (#6c757d)
- 禁用鼠标指针
- 提示文本："此字段为只读，您没有编辑权限"
- 权限说明："(仅项目负责人可编辑)" 或 "(只读字段)"

## 测试

### 自动化测试
运行 `python test_field_permissions.py` 验证权限逻辑

### 手动测试
运行 `python test_web_permissions.py` 获取Web界面测试指南

### 测试用户
- 管理员: `admin / admin123`
- 项目负责人: `project_manager / 123456`
- 普通用户: `normal_user / 123456`

## 安全考虑

1. **前后端双重验证**: 前端控制用户体验，后端确保安全性
2. **权限检查**: 每次字段更新都进行权限验证
3. **角色分离**: 不同角色有明确的权限边界
4. **项目隔离**: 项目负责人只能编辑分配的项目

## 维护说明

### 添加新字段
1. 在 `get_field_edit_permissions` 函数中定义字段权限
2. 在模板中使用 `is_field_editable` 宏
3. 更新测试脚本

### 修改权限规则
1. 修改 `get_field_edit_permissions` 函数中的权限逻辑
2. 更新相关测试用例
3. 更新文档

### 添加新角色
1. 在 `UserRole` 枚举中添加新角色
2. 在权限检查函数中添加新角色的逻辑
3. 更新测试和文档

## 文件清单

- `app/views/issues.py` - 权限检查函数和API验证
- `app/templates/issues/detail.html` - 前端模板和样式
- `app/models/user.py` - 用户权限方法
- `test_field_permissions.py` - 权限测试脚本
- `create_test_users.py` - 测试用户创建脚本
- `update_field_permissions.py` - 批量更新模板脚本
- `test_web_permissions.py` - Web测试指南

## 总结

该实现提供了完整的基于角色的字段编辑权限控制，包括：
- 清晰的权限分级
- 安全的后端验证
- 直观的前端体验
- 完整的测试覆盖
- 详细的文档说明

系统现在能够根据用户角色动态控制字段的可编辑性，确保数据安全和业务流程的正确性。
