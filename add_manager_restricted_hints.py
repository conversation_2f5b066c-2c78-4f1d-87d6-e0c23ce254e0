#!/usr/bin/env python
"""
为经理受限字段添加权限提示
"""

import re
import os

def add_manager_restricted_hints():
    """为经理受限字段添加权限提示"""
    template_path = "app/templates/issues/detail.html"
    
    if not os.path.exists(template_path):
        print(f"模板文件不存在: {template_path}")
        return False
    
    # 经理受限字段列表
    manager_restricted_fields = [
        'complaint_category',      # 抱怨类别
    ]
    
    # 读取文件内容
    with open(template_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    updated_content = content
    updated_count = 0
    
    # 为每个受限字段添加权限提示
    for field in manager_restricted_fields:
        # 查找该字段的帮助文本模式
        # 匹配: data-field="field_name"...之后的 <div class="form-help-text">内容</div>
        pattern = f'data-field="{field}"[^>]*>.*?<div class="form-help-text">([^<]+(?:<small[^>]*>[^<]*</small>)?[^<]*)</div>'
        match = re.search(pattern, updated_content, re.DOTALL)
        
        if match:
            help_text = match.group(1).strip()
            # 检查是否已经包含经理受限字段提示
            if '经理受限字段' not in help_text and '仅管理员可编辑' not in help_text:
                # 添加权限提示
                permission_check = f"field_permissions.get('{field}', False)"
                new_help_text = f'{help_text} {{%% if field_permissions is defined and field_permissions and not {permission_check} and current_user.role.value == "MANAGER" %%}}<small class="text-muted">(经理受限字段)</small>{{%% endif %%}}'
                
                # 替换原有的帮助文本
                old_div = f'<div class="form-help-text">{help_text}</div>'
                new_div = f'<div class="form-help-text">{new_help_text}</div>'
                
                updated_content = updated_content.replace(old_div, new_div)
                updated_count += 1
                print(f"为字段 {field} 添加权限提示")
            else:
                print(f"字段 {field} 已有权限提示，跳过")
        else:
            print(f"未找到字段 {field} 的帮助文本")
    
    # 写回文件
    if updated_count > 0:
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        print(f"\n已为 {updated_count} 个字段添加权限提示")
        return True
    else:
        print("\n没有需要添加权限提示的字段")
        return False

def main():
    """主函数"""
    print("为经理受限字段添加权限提示...")
    print("=" * 50)
    
    success = add_manager_restricted_hints()
    
    print("\n" + "=" * 50)
    if success:
        print("权限提示添加完成！")
        print("\n经理受限字段将显示:")
        print("'(经理受限字段)' 提示")
    else:
        print("没有需要更新的内容")

if __name__ == '__main__':
    main()
