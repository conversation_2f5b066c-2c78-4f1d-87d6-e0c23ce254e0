#!/usr/bin/env python
"""
测试字段编辑权限功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.user import User
from app.models.issue import Issue
from app.models.enums import UserRole, UserStatus
from app.views.issues import get_field_edit_permissions

def test_field_permissions():
    """测试字段编辑权限"""
    app = create_app()
    
    with app.app_context():
        # 获取测试用户
        admin = User.query.filter_by(username='admin').first()
        project_manager = User.query.filter_by(username='project_manager').first()
        normal_user = User.query.filter_by(username='normal_user').first()

        # 创建经理测试用户（如果不存在）
        manager_user = User.query.filter_by(username='manager_user').first()
        if not manager_user:
            manager_user = User(
                username='manager_user',
                email='<EMAIL>',
                department='管理部',
                position='经理',
                role=UserRole.MANAGER,
                status=UserStatus.ACTIVE,
                is_active=True
            )
            manager_user.set_password('123456')
            db.session.add(manager_user)
            db.session.commit()
            print("创建经理测试用户: manager_user / 123456")

        if not all([admin, project_manager, normal_user, manager_user]):
            print("错误: 测试用户不完整，请先运行 create_test_users.py")
            return
        
        # 获取或创建测试议题
        test_issue = Issue.query.filter_by(project_number='TEST001').first()
        if not test_issue:
            # 使用唯一的Anlauf编号
            import random
            unique_anlauf = f"9999{random.randint(1000, 9999)}"
            test_issue = Issue(
                project_number='TEST001',
                sequence_number=999,
                anlauf_number=unique_anlauf,
                department='测试部门',
                engineer='test_engineer'
            )
            db.session.add(test_issue)
            db.session.commit()
            print(f"创建测试议题: TEST001-999 (Anlauf: {unique_anlauf})")
        else:
            print(f"使用现有测试议题: {test_issue.project_number}-{test_issue.sequence_number}")
        
        print("\n" + "="*60)
        print("字段编辑权限测试结果")
        print("="*60)
        
        # 测试关键字段的权限
        test_fields = [
            'project_number',      # 只读字段
            'sequence_number',     # 只读字段
            'problem_report_week', # 项目负责人专属
            'problem_report_date', # 项目负责人专属
            'issue_status',        # 项目负责人专属
            'problem_in_meeting',  # 项目负责人专属
            'complaint_in_meeting', # 项目负责人专属
            'department',          # 项目负责人受限字段 + 经理受限字段
            'engineer',            # 项目负责人受限字段 + 经理受限字段
            'problem_description', # 项目负责人受限字段 + 经理受限字段
            'complaint_category',  # 经理受限字段
            'analysis_measures',   # 项目负责人受限字段 + 经理受限字段
            'problem_type',        # 项目负责人受限字段 + 经理受限字段
            'fault_parts_status',  # 项目负责人受限字段 + 经理受限字段
            'mark',                # 项目负责人受限字段 + 经理受限字段
            'vehicle_status',      # 普通用户受限字段
            'is_rework_vehicle',   # 普通用户受限字段
            'kdnr',                # 普通用户受限字段
            'banr',                # 普通用户受限字段
            'breakdown',           # 普通用户受限字段
            'warehouse_received_fault_parts', # 普通用户受限字段
            'meeting_topic_name',  # 普通用户受限字段
        ]
        
        users_to_test = [
            ('管理员', admin),
            ('项目负责人', project_manager),
            ('经理', manager_user),
            ('普通用户', normal_user)
        ]
        
        for user_name, user in users_to_test:
            print(f"\n{user_name} ({user.username}) 的字段权限:")
            print("-" * 40)
            
            permissions = get_field_edit_permissions(user, test_issue)
            
            for field in test_fields:
                can_edit = permissions.get(field, False)
                status = "[可编辑]" if can_edit else "[只读]"
                
                # 添加权限说明
                if field in ['project_number', 'sequence_number']:
                    reason = "(系统只读字段)"
                elif field in ['problem_report_week', 'problem_report_date', 'issue_status', 'problem_in_meeting', 'complaint_in_meeting']:
                    if user.role == UserRole.ADMIN:
                        reason = "(管理员权限)"
                    elif user.role == UserRole.PROJECT_MANAGER and user.is_assigned_to_project('TEST001'):
                        reason = "(项目负责人权限)"
                    else:
                        reason = "(需要项目负责人权限)"
                elif field in ['vehicle_status', 'is_rework_vehicle', 'kdnr', 'banr', 'breakdown', 'warehouse_received_fault_parts', 'meeting_topic_name']:
                    if user.role == UserRole.ADMIN:
                        reason = "(管理员权限)"
                    else:
                        reason = "(普通用户受限字段)"
                elif field in ['department', 'engineer', 'problem_description', 'analysis_measures', 'problem_type', 'fault_parts_status', 'mark']:
                    if user.role == UserRole.ADMIN:
                        reason = "(管理员权限)"
                    elif user.role == UserRole.PROJECT_MANAGER:
                        reason = "(项目负责人受限字段)"
                    elif user.role == UserRole.MANAGER:
                        reason = "(经理受限字段)"
                    else:
                        reason = "(普通用户权限)"
                elif field in ['complaint_category']:
                    if user.role == UserRole.ADMIN:
                        reason = "(管理员权限)"
                    elif user.role == UserRole.MANAGER:
                        reason = "(经理受限字段)"
                    else:
                        reason = "(普通用户权限)"
                else:
                    # 检查用户是否有编辑权限
                    # 管理员可以编辑所有议题
                    if user.role == UserRole.ADMIN or user.is_admin:
                        reason = "(管理员权限)"
                    # 项目负责人可以编辑分配的项目议题
                    elif user.role == UserRole.PROJECT_MANAGER and user.is_assigned_to_project('TEST001'):
                        reason = "(项目负责人权限)"
                    # 普通用户只能编辑自己创建或分配的议题
                    elif user.role == UserRole.USER:
                        reason = "(普通用户权限)"
                    else:
                        reason = "(无编辑权限)"
                
                print(f"  {field:25} {status:10} {reason}")
        
        print("\n" + "="*60)
        print("权限规则说明:")
        print("="*60)
        print("1. 只读字段: 所有用户都不能编辑")
        print("   - project_number, sequence_number, anlauf_number")
        print("   - created_at, vin, mileage, repair_station_info")
        print("   - after_sales_fault_description, review_status")
        print("   - engineer_first_review_time, engineer_first_review_week")
        print("   - zp8_delivery_date")
        print()
        print("2. 项目负责人专属字段: 只有分配到该项目的项目负责人可编辑")
        print("   - problem_report_week (问题上报周次)")
        print("   - problem_report_date (问题上报日期)")
        print("   - issue_status (议题状态)")
        print("   - problem_in_meeting (问题是否上会)")
        print("   - complaint_in_meeting (抱怨是否上会)")
        print()
        print("3. 项目负责人受限字段: 项目负责人不能编辑，只有管理员可编辑")
        print("   - department (部门)")
        print("   - engineer (工程师)")
        print("   - problem_description (问题描述)")
        print("   - analysis_measures (分析/措施)")
        print("   - problem_type (问题类型)")
        print("   - fault_parts_status (故障件状态)")
        print("   - mark (标记)")
        print()
        print("4. 经理受限字段: 经理不能编辑，只有管理员可编辑")
        print("   - department (部门)")
        print("   - engineer (工程师)")
        print("   - problem_description (问题描述)")
        print("   - complaint_category (抱怨类别)")
        print("   - analysis_measures (分析/措施)")
        print("   - problem_type (问题类型)")
        print("   - fault_parts_status (故障件状态)")
        print("   - mark (标记)")
        print()
        print("5. 普通用户受限字段: 普通用户、项目负责人和经理都不能编辑，只有管理员可编辑")
        print("   - vehicle_status (车辆状态)")
        print("   - is_rework_vehicle (是否返工车辆)")
        print("   - kdnr (KDNR)")
        print("   - banr (BANR)")
        print("   - breakdown (抛锚)")
        print("   - warehouse_received_fault_parts (仓库收到故障件)")
        print("   - meeting_topic_name (上会议题名)")
        print()
        print("6. 普通字段: 有议题编辑权限的用户可编辑")
        print()
        print("7. 管理员: 可编辑所有字段(除了只读字段)")

if __name__ == '__main__':
    test_field_permissions()
