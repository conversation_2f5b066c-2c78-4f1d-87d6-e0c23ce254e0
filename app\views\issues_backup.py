"""
议题管理视图控制器
包含议题的增删改查、状态管理、评论等核心功能
"""

from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash, current_app, session, send_file, Response
from datetime import datetime
import pandas as pd
import io
from app import db
from app.models import Issue, Comment, Tag, Project, User
from app.models.enums import IssueStatus, UserRole, UserStatus, MaintenanceStatus, Priority, Severity, VehicleStatus, IssueType, MeetingModule, ReviewStatus, ComplaintCategory
from app.utils.timezone_utils import beijing_now

issues = Blueprint('issues', __name__)


def get_current_user():
    """获取当前登录用户"""
    current_user_id = session.get('user_id')
    if not current_user_id:
        return None
    return User.query.get(current_user_id)


def login_required(f):
    """登录验证装饰器"""
    from functools import wraps
    @wraps(f)
    def decorated_function(*args, **kwargs):
        current_user = get_current_user()
        if not current_user:
            flash('请先登录', 'error')
            return redirect(url_for('user.login'))
        if current_user.status != UserStatus.ACTIVE:
            flash('您的账户尚未激活或已被禁用', 'error')
            return redirect(url_for('user.login'))
        return f(*args, **kwargs)
    return decorated_function


@issues.route('/')
@login_required
def list_issues():
    """议题列表页面"""
    current_user = get_current_user()
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config.get('ISSUES_PER_PAGE', 20)
    
    # 获取过滤参数
    status_filter = request.args.get('status')
    priority_filter = request.args.get('priority')
    severity_filter = request.args.get('severity')
    search_query = request.args.get('q', '').strip()
    
    # 新增筛选参数
    anlauf_number_filter = request.args.get('anlauf_number', '').strip()
    project_number_filter = request.args.get('project_number', '').strip()
    chassis_number_filter = request.args.get('chassis_number', '').strip()
    kdnr_filter = request.args.get('kdnr', '').strip()
    responsible_person_filter = request.args.get('responsible_person', '').strip()
    department_filter = request.args.get('department', '').strip()
    complaint_in_meeting_filter = request.args.get('complaint_in_meeting')
    problem_in_meeting_filter = request.args.get('problem_in_meeting')
    vehicle_status_filter = request.args.get('vehicle_status')
    report_date_filter = request.args.get('report_date', '').strip()
    production_date_zp8_filter = request.args.get('production_date_zp8', '').strip()
    online_status_filter = request.args.get('online_status')
    
    # 新增：维护状态筛选
    maintenance_status_filter = request.args.get('maintenance_status')
    
    # 基于用户角色构建查询
    if current_user.role == UserRole.ADMIN or current_user.is_admin:
        # 管理员可以查看所有议题
        query = Issue.query
    elif current_user.role == UserRole.MANAGER and current_user.department:
        # 经理可以查看自己部门的所有议题
        query = Issue.query.filter(Issue.department == current_user.department)
    elif current_user.role == UserRole.PROJECT_MANAGER:
        # 项目负责人权限控制
        assigned_projects = current_user.get_assigned_projects()
        if assigned_projects:
            # 有激活的项目分配，能查看：分配的项目议题 + 分配给自己的议题
            query = Issue.query.filter(
                db.or_(
                    Issue.project_number.in_(assigned_projects),  # 管理的项目议题
                    Issue.assignee == current_user.username       # 分配给自己的议题
                )
            )
        else:
            # 没有激活的项目分配，降级为普通用户权限
            query = Issue.query.filter(
                db.or_(
                    Issue.creator == current_user.username,
                    Issue.assignee == current_user.username
                )
            )
    else:
        # 普通用户只能查看自己创建或分配给自己的议题
        query = Issue.query.filter(
            db.or_(
                Issue.creator == current_user.username,
                Issue.assignee == current_user.username
            )
        )
    
    if search_query:
        query = query.filter(Issue.problem_description.contains(search_query))
    
    # 处理在线状态过滤
    if online_status_filter:
        if online_status_filter == 'true':
            # 筛选所有已上线状态（以ONLINE_开头的状态）
            online_statuses = [status for status in IssueStatus if status.name.startswith('ONLINE_')]
            query = query.filter(Issue.issue_status.in_(online_statuses))
        elif online_status_filter == 'false':
            # 筛选所有未上线状态（以OFFLINE_开头的状态）
            offline_statuses = [status for status in IssueStatus if status.name.startswith('OFFLINE_')]
            query = query.filter(Issue.issue_status.in_(offline_statuses))
    
    if status_filter:
        try:
            # 特殊处理：未上线议题筛选
            if status_filter == 'NOT_ONLINE':
                # 筛选所有非ONLINE_PRE_ANLAUF状态的议题
                query = query.filter(Issue.issue_status != IssueStatus.ONLINE_PRE_ANLAUF)
            else:
                # 通过枚举名称获取枚举实例
                status_enum = getattr(IssueStatus, status_filter)
                query = query.filter(Issue.issue_status == status_enum)
        except AttributeError:
            # 如果枚举名称不存在，忽略筛选条件
            pass
    
    if priority_filter:
        try:
            # 通过枚举名称获取枚举实例
            priority_enum = getattr(Priority, priority_filter)
            query = query.filter(Issue.priority == priority_enum)
        except AttributeError:
            # 如果枚举名称不存在，忽略筛选条件
            pass
    
    if severity_filter:
        try:
            # 通过枚举名称获取枚举实例
            severity_enum = getattr(Severity, severity_filter)
            query = query.filter(Issue.severity == severity_enum)
        except AttributeError:
            # 如果枚举名称不存在，忽略筛选条件
            pass
    
    # 新增筛选条件
    if anlauf_number_filter:
        query = query.filter(Issue.key_field.contains(anlauf_number_filter))
    
    if project_number_filter:
        query = query.filter(Issue.project.contains(project_number_filter))
    
    if chassis_number_filter:
        query = query.filter(Issue.vehicle_vin.contains(chassis_number_filter))
    
    if kdnr_filter:
        # kdnr 字段已被删除，暂时跳过此过滤条件
        pass
    
    if responsible_person_filter:
        query = query.filter(Issue.handler.contains(responsible_person_filter))
    
    if department_filter:
        # department 字段已被删除，暂时跳过此过滤条件
        pass
    
    if complaint_in_meeting_filter:
        is_meeting = complaint_in_meeting_filter == 'true'
        query = query.filter(Issue.complaint_in_meeting == is_meeting)
    
    if problem_in_meeting_filter:
        is_meeting = problem_in_meeting_filter == 'true'
        query = query.filter(Issue.problem_in_meeting == is_meeting)
    
    if report_date_filter:
        try:
            # 解析日期字符串
            filter_date = datetime.strptime(report_date_filter, '%Y-%m-%d').date()
            query = query.filter(Issue.report_date == filter_date)
        except ValueError:
            pass
    
    if production_date_zp8_filter:
        try:
            # 解析日期字符串
            filter_date = datetime.strptime(production_date_zp8_filter, '%Y-%m-%d').date()
            query = query.filter(Issue.production_date_zp8 == filter_date)
        except ValueError:
            pass
    
    if vehicle_status_filter:
        try:
            # 通过枚举名称获取枚举实例
            vehicle_status_enum = getattr(VehicleStatus, vehicle_status_filter)
            query = query.filter(Issue.vehicle_status == vehicle_status_enum)
        except AttributeError:
            # 如果枚举名称不存在，忽略筛选条件
            pass
    
    # 新增：维护状态筛选条件
    if maintenance_status_filter:
        try:
            # 通过枚举名称获取枚举实例
            maintenance_status_enum = getattr(MaintenanceStatus, maintenance_status_filter)
            query = query.filter(Issue.maintenance_status == maintenance_status_enum)
        except AttributeError:
            # 如果枚举名称不存在，忽略筛选条件
            pass
    
    # 排序和分页
    query = query.order_by(Issue.created_at.desc())
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    
    # 获取过滤选项数据
    filter_options = {
        'statuses': [(s.name, s.chinese_label) for s in IssueStatus],
        'priorities': [(p.name, p.value) for p in Priority],
        'severities': [(s.name, s.value) for s in Severity],
        'vehicle_statuses': [(vs.name, vs.value) for vs in VehicleStatus],
        'maintenance_statuses': [(ms.name, ms.value) for ms in MaintenanceStatus],
        'unique_anlauf_numbers': [item[0] for item in db.session.query(Issue.key_field).distinct().filter(Issue.key_field.isnot(None)).all()],
        'unique_project_numbers': [item[0] for item in db.session.query(Issue.project).distinct().filter(Issue.project.isnot(None)).all()],
        'unique_responsible_persons': [item[0] for item in db.session.query(Issue.handler).distinct().filter(Issue.handler.isnot(None)).all()]
    }
    
    # 获取当前筛选参数
    current_filters = {
        'status': status_filter,
        'priority': priority_filter,
        'severity': severity_filter,
        'q': search_query,
        'anlauf_number': anlauf_number_filter,
        'project_number': project_number_filter,
        'chassis_number': chassis_number_filter,
        'kdnr': kdnr_filter,
        'responsible_person': responsible_person_filter,
        'department': department_filter,
        'complaint_in_meeting': complaint_in_meeting_filter,
        'problem_in_meeting': problem_in_meeting_filter,
        'vehicle_status': vehicle_status_filter,
        'report_date': report_date_filter,
        'production_date_zp8': production_date_zp8_filter,
        'maintenance_status': maintenance_status_filter
    }
    
    return render_template('issues/list.html',
                         issues=pagination.items,
                         pagination=pagination,
                         filter_options=filter_options,
                         current_filters=current_filters,
                         current_user=current_user)


@issues.route('/<int:issue_id>')
@login_required
def view_issue(issue_id):
    """查看议题详情"""
    current_user = get_current_user()
    issue = Issue.query.get_or_404(issue_id)
    
    # 检查用户是否有权限查看此议题
    if not current_user.can_view_issue(issue):
        flash('您没有权限查看此议题', 'error')
        return redirect(url_for('issues.list_issues'))
    
    # 获取议题评论
    comments = Comment.query.filter_by(
        issue_id=issue_id,
        is_deleted=False
    ).order_by(Comment.created_at.asc()).all()
    
    # 计算问题上报天数和显示信息
    days_since_report = 0
    report_info = {
        'days': 0,
        'label': '天前上报',
        'has_report_date': False
    }
    
    from datetime import date
    today = date.today()
    
    if issue.report_date:
        # 使用问题上报日期
        days_since_report = (today - issue.report_date).days
        report_info.update({
            'days': days_since_report,
            'label': '天前上报',
            'has_report_date': True
        })
    else:
        # 回退到使用创建时间
        if issue.created_at:
            created_date = issue.created_at.date()
            days_since_report = (today - created_date).days
            report_info.update({
                'days': days_since_report,
                'label': '天前创建',
                'has_report_date': False
            })
    
    # 处理特殊情况
    if days_since_report < 0:
        # 未来日期，可能数据有误
        report_info.update({
            'days': 0,
            'label': '日期异常'
        })
    elif days_since_report == 0:
        report_info['label'] = '今天' + ('上报' if report_info['has_report_date'] else '创建')
    elif days_since_report == 1:
        report_info['label'] = '昨天' + ('上报' if report_info['has_report_date'] else '创建')
    
    return render_template('issues/detail.html',
                         issue=issue,
                         comments=comments,
                         current_user=current_user,
                         days_since_report=days_since_report,
                         report_info=report_info)


@issues.route('/create', methods=['GET', 'POST'])
@login_required
def create_issue():
    """创建新议题"""
    current_user = get_current_user()
    
    if request.method == 'GET':
        projects = Project.query.filter_by(archived=False).all()
        users = User.query.filter_by(is_active=True, status=UserStatus.ACTIVE).all()
        
        return render_template('issues/create.html',
                             projects=projects,
                             users=users,
                             current_user=current_user,
                             statuses=list(IssueStatus),
                             priorities=list(Priority),
                             severities=list(Severity),
                             issue_types=list(IssueType),
                             vehicle_statuses=list(VehicleStatus),
                             meeting_modules=list(MeetingModule),
                             review_statuses=list(ReviewStatus),
                             complaint_categories=list(ComplaintCategory))
    
    try:
        # 验证必填字段
        required_fields = ['title', 'issue_number', 'project_number', 'anlauf_number']
        missing_fields = [field for field in required_fields if not request.form.get(field)]
        
        if missing_fields:
            flash(f'请填写所有必填字段: {", ".join(missing_fields)}', 'error')
            return redirect(url_for('issues.create_issue'))
        
        # 创建新议题
        issue = Issue(
            # 基础标识字段
            title=request.form['title'],
            issue_number=request.form['issue_number'],
            project_number=request.form['project_number'],
            anlauf_number=request.form['anlauf_number'],
            sequence_number=request.form.get('sequence_number', type=int),
            review_status=ReviewStatus[request.form.get('review_status', 'PENDING')],
            
            # 内容描述字段
            problem_description=request.form.get('problem_description', ''),
            meeting_issue_name=request.form.get('meeting_issue_name', ''),
            analysis_measures=request.form.get('analysis_measures', ''),
            remarks=request.form.get('remarks', ''),
            fault_component_status=request.form.get('fault_component_status', ''),
            warehouse_received_fault=request.form.get('warehouse_received_fault') == 'true' if request.form.get('warehouse_received_fault') else False,
            
            # 人员与组织字段
            creator=request.form.get('creator', 'system'),
            assignee=request.form.get('assignee'),
            department=request.form.get('department'),
            dealer_info=request.form.get('dealer_info', ''),
            
            # 分类与优先级字段
            priority=Priority[request.form.get('priority', 'MEDIUM')],
            issue_status=IssueStatus[request.form.get('status', 'PENDING')],
            meeting_module=MeetingModule[request.form.get('meeting_module')] if request.form.get('meeting_module') else None,
            problem_code=request.form.get('problem_code', type=int),
            problem_type=request.form.get('problem_type', ''),
            
            # 车辆信息字段
            chassis_number=request.form.get('chassis_number', ''),
            vehicle_status=VehicleStatus[request.form.get('vehicle_status')] if request.form.get('vehicle_status') else None,
            breakdown=request.form.get('breakdown') == 'on',
            mileage_km=request.form.get('mileage_km', type=int),
            kdnr=request.form.get('kdnr', ''),
            banr=request.form.get('banr', ''),
            complaint_category=ComplaintCategory[request.form.get('complaint_category')] if request.form.get('complaint_category') else None,
            
            # 状态管理字段
            status_code=request.form.get('status_code', 0, type=int),
            problem_in_meeting=request.form.get('problem_in_meeting') == 'on',
            complaint_in_meeting=request.form.get('complaint_in_meeting') == 'on',
            reviewed=request.form.get('reviewed') == 'on',
            is_software_issue=request.form.get('is_software_issue') == 'on',
            
            # 扩展字段
            custom_tags=request.form.get('custom_tags', ''),
            attachments=request.form.get('attachments', ''),
            related_documents=request.form.get('related_documents', '')
        )
        
        # 设置日期字段
        date_fields = [
            ('report_date', 'report_date'),
            ('production_date_zp8', 'production_date_zp8'),
            ('engineer_first_review_date', 'engineer_first_review_date')
        ]
        
        for field_name, form_key in date_fields:
            date_str = request.form.get(form_key)
            if date_str:
                try:
                    # 处理日期字段
                    setattr(issue, field_name, datetime.strptime(date_str, '%Y-%m-%d').date())
                except ValueError:
                    flash(f'日期格式错误: {form_key}', 'error')
                    return redirect(url_for('issues.create_issue'))
        
        # 设置默认报告日期
        if not issue.report_date:
            issue.report_date = beijing_now().date()
        
        # 设置抱怨周次
        complaint_week = request.form.get('complaint_week')
        if complaint_week:
            issue.complaint_week = int(complaint_week)
            
        # 设置工程师首次审核周次
        engineer_first_review_week = request.form.get('engineer_first_review_week')
        if engineer_first_review_week:
            issue.engineer_first_review_week = int(engineer_first_review_week)
        
        db.session.add(issue)
        db.session.commit()
        
        flash(f'议题 {issue.issue_number} 创建成功', 'success')
        return redirect(url_for('issues.view_issue', issue_id=issue.id))
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'创建议题失败: {e}')
        flash('创建议题失败，请重试', 'error')
        return redirect(url_for('issues.create_issue'))


@issues.route('/<int:issue_id>/edit', methods=['GET', 'POST'])
def edit_issue(issue_id):
    """编辑议题"""
    issue = Issue.query.get_or_404(issue_id)
    
    if request.method == 'GET':
        projects = Project.query.filter_by(archived=False).all()
        users = User.query.filter_by(is_active=True).all()
        
        return render_template('issues/edit.html',
                             issue=issue,
                             projects=projects,
                             users=users,
                             statuses=list(IssueStatus),
                             priorities=list(Priority))
    
    try:
        # 更新议题信息
        issue.title = request.form['title']
        issue.assignee = request.form.get('assignee')
        # priority字段已删除，这行代码需要移除或替换为适当的字段
        issue.issue_status = IssueStatus(request.form.get('status', issue.issue_status.name))
        issue.updated_at = beijing_now()
        
        db.session.commit()
        
        flash(f'议题 {issue.issue_number} 更新成功', 'success')
        return redirect(url_for('issues.view_issue', issue_id=issue.id))
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'更新议题失败: {e}')
        flash('更新议题失败，请重试', 'error')
        return redirect(url_for('issues.edit_issue', issue_id=issue_id))


@issues.route('/<int:issue_id>/status', methods=['POST'])
def update_status(issue_id):
    """更新议题状态"""
    issue = Issue.query.get_or_404(issue_id)
    new_status = request.json.get('status')
    user = request.json.get('user', 'system')
    
    try:
        new_status_enum = IssueStatus(new_status)
        issue.update_status(new_status_enum, user)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'状态已更新为 {new_status_enum.value}',
            'new_status': new_status_enum.value
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': '更新状态失败'}), 400


@issues.route('/<int:issue_id>/comments', methods=['POST'])
@login_required
def add_comment(issue_id):
    """添加评论"""
    try:
        current_user = get_current_user()
        issue = Issue.query.get_or_404(issue_id)
        
        # 权限检查
        if not current_user.can_view_issue(issue):
            return jsonify({'success': False, 'error': '您没有权限查看此议题'}), 403
        
        data = request.get_json()
        if not data or not data.get('content'):
            return jsonify({'success': False, 'error': '评论内容不能为空'}), 400
        
        content = data['content'].strip()
        is_internal = data.get('is_internal', False)
        
        # 创建评论
        comment = Comment(
            content=content,
            issue_id=issue_id,
            author=current_user.username,  # 保存用户名
            author_role=current_user.role.name if current_user.role else None,  # 保存用户角色
            author_email=current_user.email,  # 保存用户邮箱
            is_internal=is_internal
        )
        
        # 提取@提醒
        mentioned_users = comment.extract_mentions()
        
        db.session.add(comment)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '评论添加成功',
            'comment_id': comment.id,
            'mentioned_users': mentioned_users
        })
        
    except Exception as e:
        current_app.logger.error(f'添加评论失败: {str(e)}')
        db.session.rollback()
        return jsonify({'success': False, 'error': '添加评论失败，请稍后重试'}), 500


@issues.route('/<int:issue_id>/comments/<int:comment_id>', methods=['PUT'])
@login_required
def edit_comment(issue_id, comment_id):
    """编辑评论"""
    try:
        current_user = get_current_user()
        issue = Issue.query.get_or_404(issue_id)
        comment = Comment.query.get_or_404(comment_id)
        
        # 权限检查：议题访问权限
        if not current_user.can_view_issue(issue):
            return jsonify({'success': False, 'error': '您没有权限访问此议题'}), 403
            
        # 权限检查：评论编辑权限
        if not comment.can_edit(current_user):
            return jsonify({'success': False, 'error': '您没有权限编辑此评论'}), 403
            
        # 检查评论是否属于此议题
        if comment.issue_id != issue_id:
            return jsonify({'success': False, 'error': '评论不属于此议题'}), 400
        
        data = request.get_json()
        if not data or not data.get('content'):
            return jsonify({'success': False, 'error': '评论内容不能为空'}), 400
        
        content = data['content'].strip()
        is_internal = data.get('is_internal', comment.is_internal)
        
        # 更新评论内容
        comment.content = content
        comment.is_internal = is_internal
        comment.mark_as_edited()  # 使用模型中的方法标记为已编辑
        
        # 重新提取@提醒
        mentioned_users = comment.extract_mentions()
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '评论编辑成功',
            'comment': {
                'id': comment.id,
                'content': comment.content,
                'is_edited': comment.is_edited,
                'updated_at': comment.updated_at.strftime('%Y-%m-%d %H:%M'),
                'mentioned_users': mentioned_users
            }
        })
        
    except Exception as e:
        current_app.logger.error(f'编辑评论失败: {str(e)}')
        db.session.rollback()
        return jsonify({'success': False, 'error': '编辑评论失败，请稍后重试'}), 500


@issues.route('/<int:issue_id>/comments/<int:comment_id>', methods=['DELETE'])
@login_required
def delete_comment(issue_id, comment_id):
    """删除评论"""
    try:
        current_user = get_current_user()
        issue = Issue.query.get_or_404(issue_id)
        comment = Comment.query.get_or_404(comment_id)
        
        # 权限检查：议题访问权限
        if not current_user.can_view_issue(issue):
            return jsonify({'success': False, 'error': '您没有权限访问此议题'}), 403
            
        # 权限检查：评论删除权限
        if not comment.can_delete(current_user):
            return jsonify({'success': False, 'error': '您没有权限删除此评论'}), 403
            
        # 检查评论是否属于此议题
        if comment.issue_id != issue_id:
            return jsonify({'success': False, 'error': '评论不属于此议题'}), 400
        
        # 软删除评论
        comment.soft_delete()  # 使用模型中的方法软删除
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '评论已删除'
        })
        
    except Exception as e:
        current_app.logger.error(f'删除评论失败: {str(e)}')
        db.session.rollback()
        return jsonify({'success': False, 'error': '删除评论失败，请稍后重试'}), 500


@issues.route('/<int:issue_id>/update-field', methods=['POST'])
def update_field(issue_id):
    """更新议题的单个字段"""
    issue = Issue.query.get_or_404(issue_id)
    
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': '没有提供数据'}), 400
        
        # 获取第一个字段名和值
        field_name = next(iter(data.keys()))
        field_value = data[field_name]
        
        # 验证字段是否存在于模型中
        if not hasattr(issue, field_name):
            return jsonify({'success': False, 'message': f'字段 {field_name} 不存在'}), 400
        
        # 处理特殊字段类型
        if field_name in ['breakdown', 'problem_in_meeting', 'complaint_in_meeting', 'reviewed', 'is_software_issue', 'warehouse_received_fault']:
            # 布尔字段
            field_value = field_value in ['true', 'True', '1', 1, True, '是']
        elif field_name in ['sequence_number', 'complaint_week', 'status_code', 'problem_code', 'mileage_km', 'engineer_first_review_week']:
            # 整数字段
            try:
                field_value = int(field_value) if field_value else None
            except (ValueError, TypeError):
                field_value = None
        elif field_name in ['report_date', 'production_date_zp8', 'engineer_first_review_date']:
            # 日期字段
            if field_value:
                try:
                    field_value = datetime.strptime(field_value, '%Y-%m-%d').date()
                except ValueError:
                    return jsonify({'success': False, 'message': '日期格式错误'}), 400
            else:
                field_value = None
        elif field_name in ['priority', 'issue_status', 'issue_type', 'severity', 'vehicle_status', 'meeting_module', 'review_status', 'complaint_category']:
            # 枚举字段
            if field_value:
                enum_class = None
                if field_name == 'priority':
                    enum_class = Priority
                elif field_name == 'issue_status':
                    enum_class = IssueStatus
                elif field_name == 'issue_type':
                    enum_class = IssueType
                elif field_name == 'severity':
                    enum_class = Severity
                elif field_name == 'vehicle_status':
                    enum_class = VehicleStatus
                elif field_name == 'meeting_module':
                    enum_class = MeetingModule
                elif field_name == 'review_status':
                    enum_class = ReviewStatus
                elif field_name == 'complaint_category':
                    enum_class = ComplaintCategory
                
                if enum_class:
                    try:
                        # 先尝试通过名称获取
                        field_value = enum_class[field_value]
                    except KeyError:
                        # 再尝试通过值获取
                        try:
                            field_value = next(e for e in enum_class if e.value == field_value)
                        except StopIteration:
                            return jsonify({'success': False, 'message': f'无效的{field_name}值'}), 400
            else:
                field_value = None
        elif field_name == 'maintenance_status':
            if field_value:
                try:
                    new_status = getattr(MaintenanceStatus, field_value)
                    setattr(issue, field_name, new_status)
                except AttributeError:
                    return jsonify({'success': False, 'error': '无效的维护状态值'}), 400
            else:
                setattr(issue, field_name, None)
        
        # 设置字段值
        setattr(issue, field_name, field_value)
        
        # 更新时间戳
        issue.updated_at = beijing_now()
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'字段 {field_name} 更新成功',
            'field': field_name,
            'value': str(field_value) if field_value is not None else ''
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'更新字段失败: {e}')
        return jsonify({'success': False, 'message': f'更新失败: {str(e)}'}), 500


@issues.route('/api/list')
@login_required
def api_list_issues():
    """获取议题列表的API接口"""
    current_user = get_current_user()
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    
    # 基于用户角色构建查询
    if current_user.role == UserRole.ADMIN or current_user.is_admin:
        # 管理员可以查看所有议题
        query = Issue.query
    elif current_user.role == UserRole.MANAGER and current_user.department:
        # 经理可以查看自己部门的所有议题
        query = Issue.query.filter(Issue.department == current_user.department)
    elif current_user.role == UserRole.PROJECT_MANAGER:
        # 项目负责人权限控制
        assigned_projects = current_user.get_assigned_projects()
        if assigned_projects:
            # 有激活的项目分配，能查看：分配的项目议题 + 分配给自己的议题
            query = Issue.query.filter(
                db.or_(
                    Issue.project_number.in_(assigned_projects),  # 管理的项目议题
                    Issue.assignee == current_user.username       # 分配给自己的议题
                )
            )
        else:
            # 没有激活的项目分配，降级为普通用户权限
            query = Issue.query.filter(
                db.or_(
                    Issue.creator == current_user.username,
                    Issue.assignee == current_user.username
                )
            )
    else:
        # 普通用户只能查看自己创建或分配给自己的议题
        query = Issue.query.filter(
            db.or_(
                Issue.creator == current_user.username,
                Issue.assignee == current_user.username
            )
        )
    
    query = query.order_by(Issue.created_at.desc())
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    
    return jsonify({
        'issues': [issue.to_dict() for issue in pagination.items],
        'pagination': {
            'page': pagination.page,
            'pages': pagination.pages,
            'total': pagination.total
        }
    })


@issues.route('/export')
@login_required
def export_issues():
    """导出筛选后的议题详细清单"""
    try:
        # 获取与list_issues相同的筛选参数
        status_filter = request.args.get('status')
        priority_filter = request.args.get('priority')
        severity_filter = request.args.get('severity')
        search_query = request.args.get('q', '').strip()
        
        # 新增筛选参数
        anlauf_number_filter = request.args.get('anlauf_number', '').strip()
        project_number_filter = request.args.get('project_number', '').strip()
        chassis_number_filter = request.args.get('chassis_number', '').strip()
        kdnr_filter = request.args.get('kdnr', '').strip()
        responsible_person_filter = request.args.get('responsible_person', '').strip()
        complaint_in_meeting_filter = request.args.get('complaint_in_meeting')
        vehicle_status_filter = request.args.get('vehicle_status')
        report_date_filter = request.args.get('report_date', '').strip()
        production_date_zp8_filter = request.args.get('production_date_zp8', '').strip()
        online_status_filter = request.args.get('online_status')
        
        # 构建与list_issues相同的查询逻辑，包含权限控制
        current_user = get_current_user()
        
        # 基于用户角色构建查询
        if current_user.role == UserRole.ADMIN or current_user.is_admin:
            # 管理员可以导出所有议题
            query = Issue.query
        elif current_user.role == UserRole.MANAGER and current_user.department:
            # 经理可以导出自己部门的所有议题
            query = Issue.query.filter(Issue.department == current_user.department)
        elif current_user.role == UserRole.PROJECT_MANAGER:
            # 项目负责人权限控制
            assigned_projects = current_user.get_assigned_projects()
            if assigned_projects:
                # 有激活的项目分配，能导出：分配的项目议题 + 分配给自己的议题
                query = Issue.query.filter(
                    db.or_(
                        Issue.project_number.in_(assigned_projects),  # 管理的项目议题
                        Issue.assignee == current_user.username       # 分配给自己的议题
                    )
                )
            else:
                # 没有激活的项目分配，降级为普通用户权限
                query = Issue.query.filter(
                    db.or_(
                        Issue.creator == current_user.username,
                        Issue.assignee == current_user.username
                    )
                )
        else:
            # 普通用户只能导出自己创建或分配给自己的议题
            query = Issue.query.filter(
                db.or_(
                    Issue.creator == current_user.username,
                    Issue.assignee == current_user.username
                )
            )
        
        if search_query:
            query = query.filter(
                db.or_(
                    Issue.title.contains(search_query),
                    Issue.issue_number.contains(search_query),
                    Issue.description.contains(search_query)
                )
            )
        
        # 处理在线状态过滤
        if online_status_filter:
            if online_status_filter == 'true':
                # 筛选所有已上线状态（以ONLINE_开头的状态）
                online_statuses = [status for status in IssueStatus if status.name.startswith('ONLINE_')]
                query = query.filter(Issue.issue_status.in_(online_statuses))
            elif online_status_filter == 'false':
                # 筛选所有未上线状态（以OFFLINE_开头的状态）
                offline_statuses = [status for status in IssueStatus if status.name.startswith('OFFLINE_')]
                query = query.filter(Issue.issue_status.in_(offline_statuses))
        
        if status_filter:
            try:
                # 特殊处理：未上线议题筛选
                if status_filter == 'NOT_ONLINE':
                    # 筛选所有非ONLINE_PRE_ANLAUF状态的议题
                    query = query.filter(Issue.issue_status != IssueStatus.ONLINE_PRE_ANLAUF)
                else:
                    status_enum = getattr(IssueStatus, status_filter)
                    query = query.filter(Issue.issue_status == status_enum)
            except AttributeError:
                pass
        
        if priority_filter:
            try:
                priority_enum = getattr(Priority, priority_filter)
                query = query.filter(Issue.priority == priority_enum)
            except AttributeError:
                pass
        
        if severity_filter:
            try:
                severity_enum = getattr(Severity, severity_filter)
                query = query.filter(Issue.severity == severity_enum)
            except AttributeError:
                pass
        
        # 新增筛选条件
        if anlauf_number_filter:
            query = query.filter(Issue.anlauf_number.contains(anlauf_number_filter))
        
        if project_number_filter:
            query = query.filter(Issue.project_number.contains(project_number_filter))
        
        if chassis_number_filter:
            query = query.filter(Issue.chassis_number.contains(chassis_number_filter))
        
        if kdnr_filter:
            query = query.filter(Issue.kdnr.contains(kdnr_filter))
        
        if responsible_person_filter:
            query = query.filter(Issue.assignee.contains(responsible_person_filter))
        
        if complaint_in_meeting_filter:
            is_meeting = complaint_in_meeting_filter == 'true'
            query = query.filter(Issue.complaint_in_meeting == is_meeting)
        
        if report_date_filter:
            try:
                filter_date = datetime.strptime(report_date_filter, '%Y-%m-%d').date()
                query = query.filter(Issue.report_date == filter_date)
            except ValueError:
                pass
        
        if production_date_zp8_filter:
            try:
                filter_date = datetime.strptime(production_date_zp8_filter, '%Y-%m-%d').date()
                query = query.filter(Issue.production_date_zp8 == filter_date)
            except ValueError:
                pass
        
        if vehicle_status_filter:
            try:
                vehicle_status_enum = getattr(VehicleStatus, vehicle_status_filter)
                query = query.filter(Issue.vehicle_status == vehicle_status_enum)
            except AttributeError:
                pass
        
        # 获取所有符合条件的议题（无分页限制）
        issues = query.order_by(Issue.created_at.desc()).all()
        
        # 构建导出数据
        export_data = []
        for issue in issues:
            row = {
                '议题编号': issue.issue_number,
                '标题': issue.title,
                'Anlauf编号': issue.anlauf_number or '',
                '序号': issue.sequence_number or '',
                '项目编号': issue.project_number or '',
                '审核状态': issue.review_status.value if issue.review_status else '',
                '问题上报日期': issue.report_date.strftime('%Y-%m-%d') if issue.report_date else '',
                '生产日期ZP8': issue.production_date_zp8.strftime('%Y-%m-%d') if issue.production_date_zp8 else '',
                '抱怨周次': issue.complaint_week or '',
                '创建时间': issue.created_at.strftime('%Y-%m-%d %H:%M:%S') if issue.created_at else '',
                '更新时间': issue.updated_at.strftime('%Y-%m-%d %H:%M:%S') if issue.updated_at else '',
                '工程师首次审核日期': issue.engineer_first_review_date.strftime('%Y-%m-%d') if issue.engineer_first_review_date else '',
                '工程师首次审核周次': issue.engineer_first_review_week or '',
                '底盘号': issue.chassis_number or '',
                '车辆状态': issue.vehicle_status.value if issue.vehicle_status else '',
                '抛锚': '是' if issue.breakdown else '否',
                '行驶里程KM': issue.mileage_km or '',
                'KDNR': issue.kdnr or '',
                'BANR': issue.banr or '',
                '抱怨类别': issue.complaint_category.value if issue.complaint_category else '',
                '状态编码': issue.status_code or '',
                '议题状态': issue.issue_status.chinese_label if issue.issue_status else '',
                '问题是否上会': '是' if issue.problem_in_meeting else '否',
                '抱怨是否上会': '是' if issue.complaint_in_meeting else '否',
                '已审核': '是' if issue.reviewed else '否',
                '是否软件问题': '是' if issue.is_software_issue else '否',
                '上会模块': issue.meeting_module.value if issue.meeting_module else '',
                '问题编号': issue.problem_code or '',
                '问题类型': issue.problem_type or '',
                '议题类型': issue.issue_type.value if issue.issue_type else '',
                '严重程度': issue.severity or '',
                '标记': issue.mark or '',
                '议题描述': issue.description or '',
                '问题描述': issue.problem_description or '',
                '上会议题名': issue.meeting_issue_name or '',
                '分析/措施': issue.analysis_measures or '',
                '备注': issue.remarks or '',
                '故障件状态': issue.fault_component_status or '',
                '仓库收到故障件': '是' if issue.warehouse_received_fault else '否',
                '创建人': issue.creator or '',
                '负责人': issue.assignee or '',
                '参与人员': issue.participants or '',
                '工程师': issue.engineer or '',
                '部门': issue.department or '',
                '经销商信息': issue.dealer_info or '',
                '自定义标签': issue.custom_tags or '',
                '附件列表': issue.attachments or '',
                '关联文档': issue.related_documents or ''
            }
            export_data.append(row)
        
        # 使用pandas创建Excel文件
        df = pd.DataFrame(export_data)
        
        # 创建Excel文件内存缓冲区
        excel_buffer = io.BytesIO()
        
        # 写入Excel文件
        with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='议题详细清单', index=False)
            
            # 获取工作表以设置列宽
            worksheet = writer.sheets['议题详细清单']
            
            # 自动调整列宽
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)  # 最大宽度50
                worksheet.column_dimensions[column_letter].width = adjusted_width
        
        excel_buffer.seek(0)
        
        # 生成文件名
        timestamp = beijing_now().strftime('%Y%m%d_%H%M%S')
        filename = f'议题详细清单_{timestamp}.xlsx'
        
        return send_file(
            excel_buffer,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )
        
    except Exception as e:
        current_app.logger.error(f'导出议题清单失败: {e}')
        flash('导出失败，请重试', 'error')
        return redirect(url_for('issues.list_issues'))


@issues.route('/send-batch-emails', methods=['POST'])
@login_required
def send_batch_emails():
    """批量发送邮件给筛选后的议题负责人"""
    try:
        from app.utils.email_utils import send_batch_emails_via_outlook, generate_issue_maintenance_email_body
        from collections import defaultdict
        
        # 权限验证：只有项目负责人和管理员可以使用批量邮件功能
        current_user = get_current_user()
        if not (current_user.role == UserRole.ADMIN or 
                current_user.role == UserRole.PROJECT_MANAGER or 
                current_user.is_admin):
            return jsonify({
                'success': False,
                'message': '权限不足：只有项目负责人和管理员可以使用批量邮件功能'
            }), 403
        
        # 获取与list_issues相同的筛选参数
        status_filter = request.args.get('status')
        priority_filter = request.args.get('priority')
        severity_filter = request.args.get('severity')
        search_query = request.args.get('q', '').strip()
        
        # 新增筛选参数
        anlauf_number_filter = request.args.get('anlauf_number', '').strip()
        project_number_filter = request.args.get('project_number', '').strip()
        chassis_number_filter = request.args.get('chassis_number', '').strip()
        kdnr_filter = request.args.get('kdnr', '').strip()
        responsible_person_filter = request.args.get('responsible_person', '').strip()
        complaint_in_meeting_filter = request.args.get('complaint_in_meeting')
        vehicle_status_filter = request.args.get('vehicle_status')
        report_date_filter = request.args.get('report_date', '').strip()
        production_date_zp8_filter = request.args.get('production_date_zp8', '').strip()
        online_status_filter = request.args.get('online_status')
        
        # 构建与list_issues和export_issues相同的查询逻辑，包含权限控制
        
        # 基于用户角色构建查询
        if current_user.role == UserRole.ADMIN or current_user.is_admin:
            # 管理员可以处理所有议题
            query = Issue.query
        elif current_user.role == UserRole.MANAGER and current_user.department:
            # 经理可以处理自己部门的所有议题
            query = Issue.query.filter(Issue.department == current_user.department)
        elif current_user.role == UserRole.PROJECT_MANAGER:
            # 项目负责人权限控制
            assigned_projects = current_user.get_assigned_projects()
            if assigned_projects:
                # 有激活的项目分配，能处理：分配的项目议题 + 分配给自己的议题
                query = Issue.query.filter(
                    db.or_(
                        Issue.project_number.in_(assigned_projects),  # 管理的项目议题
                        Issue.assignee == current_user.username       # 分配给自己的议题
                    )
                )
            else:
                # 没有激活的项目分配，降级为普通用户权限
                query = Issue.query.filter(
                    db.or_(
                        Issue.creator == current_user.username,
                        Issue.assignee == current_user.username
                    )
                )
        else:
            # 普通用户只能处理自己创建或分配给自己的议题
            query = Issue.query.filter(
                db.or_(
                    Issue.creator == current_user.username,
                    Issue.assignee == current_user.username
                )
            )
        
        # 应用筛选条件 - 与导出功能完全一致的逻辑
        if search_query:
            query = query.filter(
                db.or_(
                    Issue.title.contains(search_query),
                    Issue.issue_number.contains(search_query),
                    Issue.description.contains(search_query),
                    Issue.problem_description.contains(search_query)
                )
            )
        
        # 处理在线状态过滤
        if online_status_filter:
            if online_status_filter == 'true':
                # 筛选所有已上线状态（以ONLINE_开头的状态）
                online_statuses = [status for status in IssueStatus if status.name.startswith('ONLINE_')]
                query = query.filter(Issue.issue_status.in_(online_statuses))
            elif online_status_filter == 'false':
                # 筛选所有未上线状态（以OFFLINE_开头的状态）
                offline_statuses = [status for status in IssueStatus if status.name.startswith('OFFLINE_')]
                query = query.filter(Issue.issue_status.in_(offline_statuses))
        
        if status_filter:
            try:
                # 特殊处理：未上线议题筛选
                if status_filter == 'NOT_ONLINE':
                    # 筛选所有非ONLINE_PRE_ANLAUF状态的议题
                    query = query.filter(Issue.issue_status != IssueStatus.ONLINE_PRE_ANLAUF)
                else:
                    status_enum = getattr(IssueStatus, status_filter)
                    query = query.filter(Issue.issue_status == status_enum)
            except AttributeError:
                pass
        
        if priority_filter:
            try:
                priority_enum = getattr(Priority, priority_filter)
                query = query.filter(Issue.priority == priority_enum)
            except AttributeError:
                pass
        
        if severity_filter:
            try:
                severity_enum = getattr(Severity, severity_filter)
                query = query.filter(Issue.severity == severity_enum)
            except AttributeError:
                pass
        
        # 新增筛选条件
        if anlauf_number_filter:
            query = query.filter(Issue.anlauf_number.contains(anlauf_number_filter))
        
        if project_number_filter:
            query = query.filter(Issue.project_number.contains(project_number_filter))
        
        if chassis_number_filter:
            query = query.filter(Issue.chassis_number.contains(chassis_number_filter))
        
        if kdnr_filter:
            query = query.filter(Issue.kdnr.contains(kdnr_filter))
        
        if responsible_person_filter:
            query = query.filter(Issue.assignee.contains(responsible_person_filter))
        
        if complaint_in_meeting_filter:
            is_meeting = complaint_in_meeting_filter == 'true'
            query = query.filter(Issue.complaint_in_meeting == is_meeting)
        
        if report_date_filter:
            try:
                filter_date = datetime.strptime(report_date_filter, '%Y-%m-%d').date()
                query = query.filter(Issue.report_date == filter_date)
            except ValueError:
                pass
        
        if production_date_zp8_filter:
            try:
                filter_date = datetime.strptime(production_date_zp8_filter, '%Y-%m-%d').date()
                query = query.filter(Issue.production_date_zp8 == filter_date)
            except ValueError:
                pass
        
        if vehicle_status_filter:
            try:
                vehicle_status_enum = getattr(VehicleStatus, vehicle_status_filter)
                query = query.filter(Issue.vehicle_status == vehicle_status_enum)
            except AttributeError:
                pass
        
        # 获取所有符合条件的议题（无分页限制）
        issues = query.order_by(Issue.created_at.desc()).all()
        
        if not issues:
            return jsonify({
                'success': False,
                'message': '没有找到符合筛选条件的议题'
            })
        
        # 按负责人分组议题
        assignee_issues = defaultdict(list)
        
        for issue in issues:
            if issue.assignee:  # 只处理有负责人的议题
                assignee_issues[issue.assignee].append(issue)
        
        if not assignee_issues:
            return jsonify({
                'success': False,
                'message': '筛选结果中没有议题指定了负责人'
            })
        
        # 构建邮件列表
        email_list = []
        
        for assignee, assignee_issue_list in assignee_issues.items():
            # 查找负责人的邮箱
            user = User.query.filter_by(username=assignee).first()
            if not user or not user.email:
                current_app.logger.warning(f'负责人 {assignee} 没有找到用户记录或邮箱地址')
                continue
            
            # 收集项目编号
            project_numbers = list(set([issue.project_number for issue in assignee_issue_list if issue.project_number]))
            
            # 构建议题信息列表
            issues_info = []
            for issue in assignee_issue_list:
                issues_info.append({
                    'issue_number': issue.issue_number,
                    'project_number': issue.project_number,
                    'status': issue.issue_status.value if issue.issue_status else 'N/A',
                    'description': issue.problem_description or issue.title or 'N/A',
                    'report_date': issue.report_date.strftime('%Y-%m-%d') if issue.report_date else 'N/A'
                })
            
            # 生成邮件主题 - 根据需求：""项目编号"的议题维护"
            subject = f'"{", ".join(project_numbers)}"的议题维护'
            
            # 生成邮件正文
            body = generate_issue_maintenance_email_body(assignee, project_numbers, issues_info)
            
            email_list.append({
                'to_email': user.email,
                'subject': subject,
                'body': body
            })
        
        if not email_list:
            return jsonify({
                'success': False,
                'message': '没有找到有效的邮箱地址'
            })
        
        # 发送邮件
        current_app.logger.info(f'开始批量发送邮件，共 {len(email_list)} 封')
        results = send_batch_emails_via_outlook(email_list)
        
        # 记录发送结果
        current_app.logger.info(f'邮件发送完成 - 成功: {results["success_count"]}, 失败: {results["failed_count"]}')
        
        # 记录失败的邮件
        if results['failed_emails']:
            for failed_email in results['failed_emails']:
                current_app.logger.error(f'邮件发送失败 - {failed_email["email"]}: {failed_email["error"]}')
        
        return jsonify({
            'success': True,
            'success_count': results['success_count'],
            'failed_count': results['failed_count'],
            'unique_assignees': len(assignee_issues),
            'message': f'邮件发送完成，成功 {results["success_count"]} 封，失败 {results["failed_count"]} 封'
        })
        
    except Exception as e:
        current_app.logger.error(f'批量发送邮件失败: {e}')
        return jsonify({
            'success': False,
            'message': f'发送邮件时发生错误: {str(e)}'
        })


@issues.route('/<int:issue_id>/maintenance-status', methods=['POST'])
@login_required
def update_maintenance_status(issue_id):
    """更新议题维护状态"""
    try:
        current_user = get_current_user()
        issue = Issue.query.get_or_404(issue_id)
        
        # 权限检查
        if not current_user.is_admin and current_user.username not in [issue.creator, issue.assignee]:
            return jsonify({'success': False, 'error': '您没有权限修改此议题'}), 403
        
        data = request.get_json()
        if not data or 'status' not in data:
            return jsonify({'success': False, 'error': '缺少状态参数'}), 400
        
        status_name = data['status']
        
        # 验证状态值
        try:
            new_status = getattr(MaintenanceStatus, status_name)
        except AttributeError:
            return jsonify({'success': False, 'error': '无效的状态值'}), 400
        
        # 更新维护状态
        issue.update_maintenance_status(new_status, current_user.username)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'维护状态已更新为：{new_status.value}',
            'new_status': {
                'name': new_status.name,
                'value': new_status.value
            }
        })
        
    except Exception as e:
        current_app.logger.error(f'更新维护状态失败: {str(e)}')
        db.session.rollback()
        return jsonify({'success': False, 'error': '更新失败，请稍后重试'}), 500


@issues.route('/batch-maintenance-status', methods=['POST'])
@login_required
def batch_update_maintenance_status():
    """批量更新维护状态"""
    try:
        current_user = get_current_user()
        
        # 权限检查
        if current_user.role not in [UserRole.ADMIN, UserRole.PROJECT_MANAGER] and not current_user.is_admin:
            return jsonify({'success': False, 'error': '您没有权限执行批量操作'}), 403
        
        data = request.get_json()
        if not data or 'status' not in data:
            return jsonify({'success': False, 'error': '缺少状态参数'}), 400
        
        status_name = data['status']
        filters = data.get('filters', {})
        
        # 验证状态值
        try:
            new_status = getattr(MaintenanceStatus, status_name)
        except AttributeError:
            return jsonify({'success': False, 'error': '无效的状态值'}), 400
        
        # 构建查询，应用与列表页面相同的筛选逻辑
        query = Issue.query
        
        # 应用筛选条件（复用列表页面的筛选逻辑）
        if filters.get('anlauf_number'):
            query = query.filter(Issue.anlauf_number.contains(filters['anlauf_number']))
        if filters.get('project_number'):
            query = query.filter(Issue.project_number.contains(filters['project_number']))
        if filters.get('status'):
            try:
                status_enum = getattr(IssueStatus, filters['status'])
                query = query.filter(Issue.issue_status == status_enum)
            except AttributeError:
                pass
        # ... 可以添加更多筛选条件 ...
        
        # 执行批量更新
        updated_count = query.update({Issue.maintenance_status: new_status})
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'已成功更新 {updated_count} 个议题的维护状态',
            'updated_count': updated_count
        })
        
    except Exception as e:
        current_app.logger.error(f'批量更新维护状态失败: {str(e)}')
        db.session.rollback()
        return jsonify({'success': False, 'error': '批量更新失败，请稍后重试'}), 500


@issues.route('/api/users/search', methods=['GET'])
@login_required
def search_users():
    """搜索用户列表用于@功能"""
    try:
        current_user = get_current_user()
        query = request.args.get('q', '').strip()
        
        # 构建用户查询，只返回激活状态的用户
        user_query = User.query.filter(
            User.status == UserStatus.ACTIVE,
            User.is_active == True
        )
        
        # 如果有搜索关键词，进行模糊匹配
        if query:
            user_query = user_query.filter(
                db.or_(
                    User.username.contains(query),
                    User.employee_number.contains(query),
                    User.department.contains(query),
                    User.position.contains(query)
                )
            )
        
        # 限制返回数量，避免性能问题
        users = user_query.limit(20).all()
        
        # 构建返回数据
        user_list = []
        for user in users:
            user_data = {
                'id': user.id,
                'username': user.username,
                'employee_number': user.employee_number or '',
                'department': user.department or '',
                'position': user.position or '',
                'role': user.role.value if user.role else '',
                'display_name': f"{user.employee_number or user.username} - {user.department or '未知部门'}"
            }
            user_list.append(user_data)
        
        return jsonify({
            'success': True,
            'users': user_list,
            'total': len(user_list)
        })
        
    except Exception as e:
        current_app.logger.error(f'搜索用户失败: {str(e)}')
        return jsonify({'success': False, 'error': '搜索用户失败'}), 500 